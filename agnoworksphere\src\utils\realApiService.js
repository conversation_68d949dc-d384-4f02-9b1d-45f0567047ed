// src/utils/realApiService.js
// Real API service that connects to the backend

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

// Helper function to get headers with authentication
const getAuthHeaders = (organizationId = null) => {
  const token = localStorage.getItem('accessToken');
  const headers = {
    'Content-Type': 'application/json',
    ...(organizationId && { 'X-Organization-ID': organizationId })
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  const result = await response.json();

  if (!response.ok) {
    console.error('API Error Response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      body: result
    });

    const errorMessage = result.error?.message || result.message || result.detail || 'API request failed';
    const error = new Error(errorMessage);
    error.status = response.status;
    error.response = result;
    throw error;
  }

  return result;
};

const realApiService = {
  // Authentication
  auth: {
    // Register new user
    register: async (userData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: userData.email,
            password: userData.password,
            first_name: userData.firstName || userData.first_name || '',
            last_name: userData.lastName !== undefined ? userData.lastName : (userData.last_name || ''),
            organization_name: userData.organizationName || userData.organization_name || '',
            organization_slug: userData.organizationSlug || userData.organization_slug || ''
          }),
        });

        const result = await handleResponse(response);
        
        // Store tokens and user info
        if (result.data && result.data.tokens) {
          localStorage.setItem('accessToken', result.data.tokens.access_token);
          localStorage.setItem('currentUser', JSON.stringify(result.data.user));
          
          if (result.data.user.organizations && result.data.user.organizations.length > 0) {
            localStorage.setItem('organizationId', result.data.user.organizations[0].id);
            localStorage.setItem('userRole', result.data.user.organizations[0].role);
          }
        }

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Registration error:', error);
        return {
          data: null,
          error: error.message || 'Registration failed'
        };
      }
    },

    // Login user
    login: async (email, password) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            password
          }),
        });

        const result = await handleResponse(response);
        
        // Store tokens and user info
        if (result.data && result.data.tokens) {
          localStorage.setItem('accessToken', result.data.tokens.access_token);
          localStorage.setItem('currentUser', JSON.stringify(result.data.user));

          // Handle organization and role from enhanced_server response format
          if (result.data.organization && result.data.role) {
            localStorage.setItem('organizationId', result.data.organization.id);
            localStorage.setItem('userRole', result.data.role);
          } else if (result.data.user.organizations && result.data.user.organizations.length > 0) {
            localStorage.setItem('organizationId', result.data.user.organizations[0].id);
            localStorage.setItem('userRole', result.data.user.organizations[0].role);
          }
        }

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Login error:', error);
        return {
          data: null,
          error: error.message || 'Login failed'
        };
      }
    },

    // Get current user profile
    getCurrentUser: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Get current user error:', error);
        return {
          data: null,
          error: error.message || 'Failed to get user profile'
        };
      }
    },

    // Update user profile
    updateProfile: async (profileData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(profileData),
        });

        const result = await handleResponse(response);

        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Update profile error:', error);
        return {
          data: null,
          error: error.message || 'Failed to update profile'
        };
      }
    },

    // Logout
    logout: async () => {
      try {
        // Clear stored tokens and user data
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('organizationId');
        localStorage.removeItem('currentUser');

        return {
          error: null
        };
      } catch (error) {
        return {
          error: error.message || 'Logout failed'
        };
      }
    },

    // Check if user is authenticated
    isAuthenticated: () => {
      const token = localStorage.getItem('accessToken');
      return !!token;
    },

    // Get stored access token
    getAccessToken: () => {
      return localStorage.getItem('accessToken');
    },

    // Get user role
    getUserRole: () => {
      return localStorage.getItem('userRole') || 'member';
    },

    // Get organization ID
    getOrganizationId: () => {
      return localStorage.getItem('organizationId');
    }
  },

  // Organizations
  organizations: {
    // Get all organizations
    getAll: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get organizations error:', error);
        throw error;
      }
    },

    // Get organization by ID
    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get organization error:', error);
        throw error;
      }
    },

    // Get organization members
    getMembers: async (organizationId, filters = {}) => {
      try {
        // Build query parameters
        const params = new URLSearchParams();
        if (filters.page) params.append('page', filters.page);
        if (filters.limit) params.append('limit', filters.limit);
        if (filters.search) params.append('search', filters.search);
        if (filters.role) params.append('role', filters.role);

        const queryString = params.toString();
        const url = `${API_BASE_URL}/api/v1/organizations/${organizationId}/members${queryString ? `?${queryString}` : ''}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get organization members error:', error);
        throw error;
      }
    }
  },

  // Teams
  teams: {
    // Get member activity
    getMemberActivity: async (organizationId, userId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/teams/${organizationId}/members/${userId}/activity`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get member activity error:', error);
        throw error;
      }
    },

    // Invite team member
    inviteMember: async (organizationId, inviteData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/invite`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(inviteData),
        });

        const result = await handleResponse(response);
        return result;
      } catch (error) {
        console.error('Invite member error:', error);
        throw error;
      }
    },

    // Update member role
    updateMemberRole: async (organizationId, userId, roleData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}/role`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(roleData),
        });

        const result = await handleResponse(response);
        return result;
      } catch (error) {
        console.error('Update member role error:', error);
        throw error;
      }
    },

    // Remove member
    removeMember: async (organizationId, userId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result;
      } catch (error) {
        console.error('Remove member error:', error);
        throw error;
      }
    }
  },

  // Projects
  projects: {
    // Get all projects
    getAll: async (organizationId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {
          method: 'GET',
          headers: getAuthHeaders(organizationId),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get projects error:', error);
        throw error;
      }
    },

    // Get project by ID
    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get project error:', error);
        throw error;
      }
    },

    // Create project
    create: async (organizationId, projectData) => {
      try {
        // Include organization_id in the project data as required by the backend
        const dataWithOrgId = {
          ...projectData,
          organization_id: organizationId
        };

        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {
          method: 'POST',
          headers: getAuthHeaders(organizationId),
          body: JSON.stringify(dataWithOrgId),
        });

        const result = await handleResponse(response);
        return result;
      } catch (error) {
        console.error('Create project error:', error);
        throw error;
      }
    },

    // Update project
    update: async (id, projectData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(projectData),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Update project error:', error);
        throw error;
      }
    },

    // Delete project
    delete: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { success: true, data: result.data };
      } catch (error) {
        console.error('Delete project error:', error);
        throw error;
      }
    }
  },

  // Boards
  boards: {
    // Get boards by project
    getByProject: async (projectId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get boards error:', error);
        throw error;
      }
    }
  },

  // Columns
  columns: {
    // Get columns by board
    getByBoard: async (boardId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/columns?board_id=${boardId}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Get columns error:', error);
        throw error;
      }
    }
  },

  // Cards/Tasks
  cards: {
    // Get all cards (by column_id)
    getAll: async (columnId = null) => {
      try {
        const url = columnId
          ? `${API_BASE_URL}/api/v1/cards?column_id=${columnId}`
          : `${API_BASE_URL}/api/v1/cards`;

        const response = await fetch(url, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { data: result.data || [] };
      } catch (error) {
        console.error('Get cards error:', error);
        return { data: [] }; // Return empty array on error
      }
    },

    // Create card
    create: async (cardData) => {
      try {
        // Filter and format data for backend API
        const backendCardData = {
          title: cardData.title,
          description: cardData.description || null,
          column_id: cardData.column_id || cardData.columnId,
          position: cardData.position || 0,
          priority: cardData.priority || 'medium',
          assigned_to: cardData.assigned_to || cardData.assignedTo || null,
          checklist: cardData.checklist || null
        };

        // Handle due_date formatting
        const dueDate = cardData.due_date || cardData.dueDate;
        if (dueDate && dueDate.trim() !== '') {
          // Ensure the date is in ISO format for the backend
          try {
            const dateObj = new Date(dueDate);
            if (!isNaN(dateObj.getTime())) {
              backendCardData.due_date = dateObj.toISOString();
            }
          } catch (error) {
            console.warn('Invalid due date format:', dueDate);
          }
        }

        // Handle assigned_to formatting - ensure it's an array of user IDs
        const assignedTo = cardData.assigned_to || cardData.assignedTo;
        if (assignedTo && Array.isArray(assignedTo) && assignedTo.length > 0) {
          // Ensure all items are strings (user IDs)
          backendCardData.assigned_to = assignedTo.map(userId =>
            typeof userId === 'string' ? userId : String(userId)
          );
        }

        // Handle checklist formatting
        const checklist = cardData.checklist;
        if (checklist && Array.isArray(checklist) && checklist.length > 0) {
          // Format checklist items for backend
          backendCardData.checklist = checklist.map((item, index) => ({
            text: item.text || item.title || '',
            position: item.position !== undefined ? item.position : index,
            ai_generated: item.aiGenerated || item.ai_generated || false,
            confidence: item.confidence || null,
            metadata: item.metadata || null
          }));
        }

        // Validate required fields
        if (!backendCardData.title || !backendCardData.title.trim()) {
          throw new Error('Card title is required');
        }
        if (!backendCardData.column_id) {
          throw new Error('Column ID is required');
        }

        // Remove null values to avoid sending unnecessary data
        Object.keys(backendCardData).forEach(key => {
          if (backendCardData[key] === null || backendCardData[key] === undefined) {
            delete backendCardData[key];
          }
        });

        console.log('Original card data:', cardData);
        console.log('Sending card data to backend:', backendCardData);
        console.log('Column ID being sent:', backendCardData.column_id);

        const response = await fetch(`${API_BASE_URL}/api/v1/cards`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(backendCardData),
        });

        const result = await handleResponse(response);
        return { data: result.data };
      } catch (error) {
        console.error('Create card error:', error);
        console.error('Response status:', error.status);
        console.error('Response details:', error.message);

        // Try to get more details from the response
        if (error.response) {
          console.error('Error response body:', error.response);
        }

        throw error;
      }
    },

    // Update card
    update: async (cardId, cardData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(cardData),
        });

        const result = await handleResponse(response);
        return { data: result.data };
      } catch (error) {
        console.error('Update card error:', error);
        throw error;
      }
    },

    // Delete card
    delete: async (cardId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { success: true, data: result.data };
      } catch (error) {
        console.error('Delete card error:', error);
        throw error;
      }
    }
  },

  // Checklist
  checklist: {
    // Create multiple checklist items for a card
    createBulk: async (cardId, checklistData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(checklistData),
        });

        const result = await handleResponse(response);
        return { data: result };
      } catch (error) {
        console.error('Create checklist items error:', error);
        throw error;
      }
    },

    // Get checklist items for a card
    getByCard: async (cardId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { data: result };
      } catch (error) {
        console.error('Get checklist items error:', error);
        return { data: [] };
      }
    },

    // Update a checklist item
    updateItem: async (itemId, itemData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(itemData),
        });

        const result = await handleResponse(response);
        return { data: result };
      } catch (error) {
        console.error('Update checklist item error:', error);
        throw error;
      }
    },

    // Delete a checklist item
    deleteItem: async (itemId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { success: true, data: result };
      } catch (error) {
        console.error('Delete checklist item error:', error);
        throw error;
      }
    },

    // Generate AI checklist for a card
    generateAI: async (cardId, requestData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist/ai-generate`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(requestData),
        });

        const result = await handleResponse(response);
        return { data: result };
      } catch (error) {
        console.error('Generate AI checklist error:', error);
        throw error;
      }
    }
  },

  // Notifications
  notifications: {
    // Get all notifications
    getAll: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { data: result.data || [] };
      } catch (error) {
        console.error('Get notifications error:', error);
        return { data: [] }; // Return empty array on error
      }
    },

    // Create notification
    create: async (notificationData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(notificationData),
        });

        const result = await handleResponse(response);
        return { data: result.data };
      } catch (error) {
        console.error('Create notification error:', error);
        throw error;
      }
    },

    // Mark as read
    markAsRead: async (notificationId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}/read`, {
          method: 'PUT',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { data: result.data };
      } catch (error) {
        console.error('Mark notification as read error:', error);
        throw error;
      }
    },

    // Mark all as read
    markAllAsRead: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/read-all`, {
          method: 'PUT',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { data: result.data };
      } catch (error) {
        console.error('Mark all notifications as read error:', error);
        throw error;
      }
    },

    // Delete notification
    delete: async (notificationId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return { success: true, data: result.data };
      } catch (error) {
        console.error('Delete notification error:', error);
        throw error;
      }
    }
  },

  // Dashboard
  dashboard: {
    // Get dashboard stats
    getStats: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return {
          data: result.data,
          error: null
        };
      } catch (error) {
        console.error('Get dashboard stats error:', error);
        return {
          data: null,
          error: error.message || 'Failed to get dashboard stats'
        };
      }
    }
  },

  // AI Projects
  aiProjects: {
    // Generate AI project preview
    generatePreview: async (projectData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-preview`, {
          method: 'POST',
          headers: getAuthHeaders(projectData.organization_id),
          body: JSON.stringify(projectData),
        });

        const result = await handleResponse(response);
        return {
          data: result,
          error: null
        };
      } catch (error) {
        console.error('Generate AI project preview error:', error);
        return {
          data: null,
          error: error.message || 'Failed to generate AI project preview'
        };
      }
    },

    // Create AI project from preview
    createFromPreview: async (confirmationData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-create`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(confirmationData),
        });

        const result = await handleResponse(response);
        return {
          data: result,
          error: null
        };
      } catch (error) {
        console.error('Create AI project from preview error:', error);
        return {
          data: null,
          error: error.message || 'Failed to create AI project'
        };
      }
    },

    // Generate AI project directly (simplified flow)
    generateProject: async (projectData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/projects/ai-generate`, {
          method: 'POST',
          headers: getAuthHeaders(projectData.organization_id),
          body: JSON.stringify(projectData),
        });

        const result = await handleResponse(response);
        return {
          data: result,
          error: null
        };
      } catch (error) {
        console.error('Generate AI project error:', error);
        return {
          data: null,
          error: error.message || 'Failed to generate AI project'
        };
      }
    }
  }
};

export default realApiService;
