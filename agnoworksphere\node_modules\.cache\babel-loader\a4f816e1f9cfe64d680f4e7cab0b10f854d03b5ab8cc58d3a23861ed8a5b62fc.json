{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// src/contexts/AuthContext.jsx\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport authService from '../utils/authService';\n\n// Create context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Enhanced context provider with mock authentication\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkExistingSession = async () => {\n      try {\n        var _result$data;\n        // First check if there's a stored token\n        const token = localStorage.getItem('token');\n        if (!token) {\n          console.log('No token found, user not authenticated');\n          setLoading(false);\n          return;\n        }\n\n        // If token exists, verify it with the API\n        const result = await authService.getCurrentUser();\n        if (result !== null && result !== void 0 && (_result$data = result.data) !== null && _result$data !== void 0 && _result$data.user) {\n          setUser(result.data.user);\n          console.log('Session restored for user:', result.data.user.email);\n        } else {\n          // Invalid token, remove it\n          localStorage.removeItem('token');\n          console.log('Invalid token removed');\n        }\n      } catch (error) {\n        console.error('Session check failed:', error);\n        // If API call fails, remove the invalid token\n        localStorage.removeItem('token');\n        // Don't set user to null, just let it remain undefined\n        // This allows the app to work even when the API is not available\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkExistingSession();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const result = await authService.signIn(email, password);\n      if (result.error) {\n        return {\n          success: false,\n          error: result.error\n        };\n      }\n      setUser(result.data.user);\n      return {\n        success: true,\n        data: result.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await authService.signOut();\n      setUser(null);\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const register = async (email, password, userData) => {\n    try {\n      const result = await authService.signUp(email, password, userData);\n      if (result.error) {\n        return {\n          success: false,\n          error: result.error\n        };\n      }\n      setUser(result.data.user);\n      return {\n        success: true,\n        data: result.data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    register,\n    isAuthenticated: !!user,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  return useContext(AuthContext);\n};\n_s2(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "checkExistingSession", "_result$data", "token", "localStorage", "getItem", "console", "log", "result", "getCurrentUser", "data", "email", "removeItem", "error", "login", "password", "signIn", "success", "message", "logout", "signOut", "register", "userData", "signUp", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/contexts/AuthContext.jsx"], "sourcesContent": ["// src/contexts/AuthContext.jsx\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport authService from '../utils/authService';\n\n// Create context\nconst AuthContext = createContext();\n\n// Enhanced context provider with mock authentication\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkExistingSession = async () => {\n      try {\n        // First check if there's a stored token\n        const token = localStorage.getItem('token');\n        if (!token) {\n          console.log('No token found, user not authenticated');\n          setLoading(false);\n          return;\n        }\n\n        // If token exists, verify it with the API\n        const result = await authService.getCurrentUser();\n        if (result?.data?.user) {\n          setUser(result.data.user);\n          console.log('Session restored for user:', result.data.user.email);\n        } else {\n          // Invalid token, remove it\n          localStorage.removeItem('token');\n          console.log('Invalid token removed');\n        }\n      } catch (error) {\n        console.error('Session check failed:', error);\n        // If API call fails, remove the invalid token\n        localStorage.removeItem('token');\n        // Don't set user to null, just let it remain undefined\n        // This allows the app to work even when the API is not available\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkExistingSession();\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      const result = await authService.signIn(email, password);\n      if (result.error) {\n        return { success: false, error: result.error };\n      }\n\n      setUser(result.data.user);\n      return { success: true, data: result.data };\n    } catch (error) {\n      return { success: false, error: error.message };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authService.signOut();\n      setUser(null);\n      return { success: true };\n    } catch (error) {\n      return { success: false, error: error.message };\n    }\n  };\n\n  const register = async (email, password, userData) => {\n    try {\n      const result = await authService.signUp(email, password, userData);\n      if (result.error) {\n        return { success: false, error: result.error };\n      }\n\n      setUser(result.data.user);\n      return { success: true, data: result.data };\n    } catch (error) {\n      return { success: false, error: error.message };\n    }\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    register,\n    isAuthenticated: !!user,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook\nexport const useAuth = () => useContext(AuthContext);\n"], "mappings": ";;;AAAA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,WAAW,MAAM,sBAAsB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMY,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QAAA,IAAAC,YAAA;QACF;QACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACVG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrDP,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMQ,MAAM,GAAG,MAAMlB,WAAW,CAACmB,cAAc,CAAC,CAAC;QACjD,IAAID,MAAM,aAANA,MAAM,gBAAAN,YAAA,GAANM,MAAM,CAAEE,IAAI,cAAAR,YAAA,eAAZA,YAAA,CAAcL,IAAI,EAAE;UACtBC,OAAO,CAACU,MAAM,CAACE,IAAI,CAACb,IAAI,CAAC;UACzBS,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,MAAM,CAACE,IAAI,CAACb,IAAI,CAACc,KAAK,CAAC;QACnE,CAAC,MAAM;UACL;UACAP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;UAChCN,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACtC;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C;QACAT,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;QAChC;QACA;MACF,CAAC,SAAS;QACRZ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,KAAK,GAAG,MAAAA,CAAOH,KAAK,EAAEI,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMP,MAAM,GAAG,MAAMlB,WAAW,CAAC0B,MAAM,CAACL,KAAK,EAAEI,QAAQ,CAAC;MACxD,IAAIP,MAAM,CAACK,KAAK,EAAE;QAChB,OAAO;UAAEI,OAAO,EAAE,KAAK;UAAEJ,KAAK,EAAEL,MAAM,CAACK;QAAM,CAAC;MAChD;MAEAf,OAAO,CAACU,MAAM,CAACE,IAAI,CAACb,IAAI,CAAC;MACzB,OAAO;QAAEoB,OAAO,EAAE,IAAI;QAAEP,IAAI,EAAEF,MAAM,CAACE;MAAK,CAAC;IAC7C,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO;QAAEI,OAAO,EAAE,KAAK;QAAEJ,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM7B,WAAW,CAAC8B,OAAO,CAAC,CAAC;MAC3BtB,OAAO,CAAC,IAAI,CAAC;MACb,OAAO;QAAEmB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd,OAAO;QAAEI,OAAO,EAAE,KAAK;QAAEJ,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAAA,CAAOV,KAAK,EAAEI,QAAQ,EAAEO,QAAQ,KAAK;IACpD,IAAI;MACF,MAAMd,MAAM,GAAG,MAAMlB,WAAW,CAACiC,MAAM,CAACZ,KAAK,EAAEI,QAAQ,EAAEO,QAAQ,CAAC;MAClE,IAAId,MAAM,CAACK,KAAK,EAAE;QAChB,OAAO;UAAEI,OAAO,EAAE,KAAK;UAAEJ,KAAK,EAAEL,MAAM,CAACK;QAAM,CAAC;MAChD;MAEAf,OAAO,CAACU,MAAM,CAACE,IAAI,CAACb,IAAI,CAAC;MACzB,OAAO;QAAEoB,OAAO,EAAE,IAAI;QAAEP,IAAI,EAAEF,MAAM,CAACE;MAAK,CAAC;IAC7C,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO;QAAEI,OAAO,EAAE,KAAK;QAAEJ,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMM,KAAK,GAAG;IACZ3B,IAAI;IACJiB,KAAK;IACLK,MAAM;IACNE,QAAQ;IACRI,eAAe,EAAE,CAAC,CAAC5B,IAAI;IACvBE;EACF,CAAC;EAED,oBACEP,OAAA,CAACC,WAAW,CAACiC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA7B,QAAA,EAChCA;EAAQ;IAAAgC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAlC,EAAA,CA9FaF,YAAY;AAAAqC,EAAA,GAAZrC,YAAY;AA+FzB,OAAO,MAAMsC,OAAO,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAM9C,UAAU,CAACM,WAAW,CAAC;AAAA;AAACwC,GAAA,CAAxCD,OAAO;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}