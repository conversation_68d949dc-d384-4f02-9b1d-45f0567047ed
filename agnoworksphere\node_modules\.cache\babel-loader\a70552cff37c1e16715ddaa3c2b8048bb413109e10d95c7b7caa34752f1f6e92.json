{"ast": null, "code": "// src/utils/realApiService.js\n// Real API service that connects to the backend\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && {\n      'X-Organization-ID': organizationId\n    })\n  };\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async response => {\n  const result = await response.json();\n  if (!response.ok) {\n    var _result$error;\n    console.error('API Error Response:', {\n      status: response.status,\n      statusText: response.statusText,\n      url: response.url,\n      body: result\n    });\n    const errorMessage = ((_result$error = result.error) === null || _result$error === void 0 ? void 0 : _result$error.message) || result.message || result.detail || 'API request failed';\n    const error = new Error(errorMessage);\n    error.status = response.status;\n    error.response = result;\n    throw error;\n  }\n  return result;\n};\nconst realApiService = {\n  // Authentication\n  auth: {\n    // Register new user\n    register: async userData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email: userData.email,\n            password: userData.password,\n            first_name: userData.firstName || userData.first_name || '',\n            last_name: userData.lastName !== undefined ? userData.lastName : userData.last_name || '',\n            organization_name: userData.organizationName || userData.organization_name || '',\n            organization_slug: userData.organizationSlug || userData.organization_slug || ''\n          })\n        });\n        const result = await handleResponse(response);\n\n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Registration error:', error);\n        return {\n          data: null,\n          error: error.message || 'Registration failed'\n        };\n      }\n    },\n    // Login user\n    login: async (email, password) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email,\n            password\n          })\n        });\n        const result = await handleResponse(response);\n\n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n\n          // Handle organization and role from enhanced_server response format\n          if (result.data.organization && result.data.role) {\n            localStorage.setItem('organizationId', result.data.organization.id);\n            localStorage.setItem('userRole', result.data.role);\n          } else if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Login error:', error);\n        return {\n          data: null,\n          error: error.message || 'Login failed'\n        };\n      }\n    },\n    // Get current user profile\n    getCurrentUser: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get current user error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get user profile'\n        };\n      }\n    },\n    // Update user profile\n    updateProfile: async profileData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(profileData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Update profile error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to update profile'\n        };\n      }\n    },\n    // Logout\n    logout: async () => {\n      try {\n        // Clear stored tokens and user data\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        localStorage.removeItem('currentUser');\n        return {\n          error: null\n        };\n      } catch (error) {\n        return {\n          error: error.message || 'Logout failed'\n        };\n      }\n    },\n    // Check if user is authenticated\n    isAuthenticated: () => {\n      const token = localStorage.getItem('accessToken');\n      return !!token;\n    },\n    // Get stored access token\n    getAccessToken: () => {\n      return localStorage.getItem('accessToken');\n    },\n    // Get user role\n    getUserRole: () => {\n      return localStorage.getItem('userRole') || 'member';\n    },\n    // Get organization ID\n    getOrganizationId: () => {\n      return localStorage.getItem('organizationId');\n    }\n  },\n  // Organizations\n  organizations: {\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organizations error:', error);\n        throw error;\n      }\n    },\n    // Get organization by ID\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization error:', error);\n        throw error;\n      }\n    },\n    // Get organization members\n    getMembers: async (organizationId, filters = {}) => {\n      try {\n        // Build query parameters\n        const params = new URLSearchParams();\n        if (filters.page) params.append('page', filters.page);\n        if (filters.limit) params.append('limit', filters.limit);\n        if (filters.search) params.append('search', filters.search);\n        if (filters.role) params.append('role', filters.role);\n        const queryString = params.toString();\n        const url = `${API_BASE_URL}/api/v1/organizations/${organizationId}/members${queryString ? `?${queryString}` : ''}`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization members error:', error);\n        throw error;\n      }\n    }\n  },\n  // Teams\n  teams: {\n    // Get member activity\n    getMemberActivity: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/teams/${organizationId}/members/${userId}/activity`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get member activity error:', error);\n        throw error;\n      }\n    },\n    // Invite team member\n    inviteMember: async (organizationId, inviteData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/invite`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(inviteData)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Invite member error:', error);\n        throw error;\n      }\n    },\n    // Update member role\n    updateMemberRole: async (organizationId, userId, roleData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}/role`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(roleData)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Update member role error:', error);\n        throw error;\n      }\n    },\n    // Remove member\n    removeMember: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Remove member error:', error);\n        throw error;\n      }\n    }\n  },\n  // Projects\n  projects: {\n    // Get all projects\n    getAll: async organizationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'GET',\n          headers: getAuthHeaders(organizationId)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get projects error:', error);\n        throw error;\n      }\n    },\n    // Get project by ID\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get project error:', error);\n        throw error;\n      }\n    },\n    // Create project\n    create: async (organizationId, projectData) => {\n      try {\n        // Include organization_id in the project data as required by the backend\n        const dataWithOrgId = {\n          ...projectData,\n          organization_id: organizationId\n        };\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(organizationId),\n          body: JSON.stringify(dataWithOrgId)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Create project error:', error);\n        throw error;\n      }\n    },\n    // Update project\n    update: async (id, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Update project error:', error);\n        throw error;\n      }\n    },\n    // Delete project\n    delete: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete project error:', error);\n        throw error;\n      }\n    }\n  },\n  // Boards\n  boards: {\n    // Get boards by project\n    getByProject: async projectId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get boards error:', error);\n        throw error;\n      }\n    }\n  },\n  // Columns\n  columns: {\n    // Get columns by board\n    getByBoard: async boardId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/columns?board_id=${boardId}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get columns error:', error);\n        throw error;\n      }\n    }\n  },\n  // Cards/Tasks\n  cards: {\n    // Get all cards (by column_id)\n    getAll: async (columnId = null) => {\n      try {\n        const url = columnId ? `${API_BASE_URL}/api/v1/cards?column_id=${columnId}` : `${API_BASE_URL}/api/v1/cards`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data || []\n        };\n      } catch (error) {\n        console.error('Get cards error:', error);\n        return {\n          data: []\n        }; // Return empty array on error\n      }\n    },\n    // Create card\n    create: async cardData => {\n      try {\n        // Filter and format data for backend API\n        const backendCardData = {\n          title: cardData.title,\n          description: cardData.description || null,\n          column_id: cardData.column_id || cardData.columnId,\n          position: cardData.position || 0,\n          priority: cardData.priority || 'medium',\n          assigned_to: cardData.assigned_to || cardData.assignedTo || null,\n          checklist: cardData.checklist || null\n        };\n\n        // Handle due_date formatting\n        const dueDate = cardData.due_date || cardData.dueDate;\n        if (dueDate && dueDate.trim() !== '') {\n          // Ensure the date is in ISO format for the backend\n          try {\n            const dateObj = new Date(dueDate);\n            if (!isNaN(dateObj.getTime())) {\n              backendCardData.due_date = dateObj.toISOString();\n            }\n          } catch (error) {\n            console.warn('Invalid due date format:', dueDate);\n          }\n        }\n\n        // Handle assigned_to formatting - ensure it's an array of user IDs\n        const assignedTo = cardData.assigned_to || cardData.assignedTo;\n        if (assignedTo && Array.isArray(assignedTo) && assignedTo.length > 0) {\n          // Ensure all items are strings (user IDs)\n          backendCardData.assigned_to = assignedTo.map(userId => typeof userId === 'string' ? userId : String(userId));\n        }\n\n        // Handle checklist formatting\n        const checklist = cardData.checklist;\n        if (checklist && Array.isArray(checklist) && checklist.length > 0) {\n          // Format checklist items for backend\n          backendCardData.checklist = checklist.map((item, index) => ({\n            text: item.text || item.title || '',\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          }));\n        }\n\n        // Validate required fields\n        if (!backendCardData.title || !backendCardData.title.trim()) {\n          throw new Error('Card title is required');\n        }\n        if (!backendCardData.column_id) {\n          throw new Error('Column ID is required');\n        }\n\n        // Remove null values to avoid sending unnecessary data\n        Object.keys(backendCardData).forEach(key => {\n          if (backendCardData[key] === null || backendCardData[key] === undefined) {\n            delete backendCardData[key];\n          }\n        });\n        console.log('Original card data:', cardData);\n        console.log('Sending card data to backend:', backendCardData);\n        console.log('Column ID being sent:', backendCardData.column_id);\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(backendCardData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Create card error:', error);\n        console.error('Response status:', error.status);\n        console.error('Response details:', error.message);\n\n        // Try to get more details from the response\n        if (error.response) {\n          console.error('Error response body:', error.response);\n        }\n        throw error;\n      }\n    },\n    // Update card\n    update: async (cardId, cardData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(cardData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Update card error:', error);\n        throw error;\n      }\n    },\n    // Delete card\n    delete: async cardId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete card error:', error);\n        throw error;\n      }\n    }\n  },\n  // Checklist\n  checklist: {\n    // Create multiple checklist items for a card\n    createBulk: async (cardId, checklistData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(checklistData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Create checklist items error:', error);\n        throw error;\n      }\n    },\n    // Get checklist items for a card\n    getByCard: async cardId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Get checklist items error:', error);\n        return {\n          data: []\n        };\n      }\n    },\n    // Update a checklist item\n    updateItem: async (itemId, itemData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(itemData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Update checklist item error:', error);\n        throw error;\n      }\n    },\n    // Delete a checklist item\n    deleteItem: async itemId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result\n        };\n      } catch (error) {\n        console.error('Delete checklist item error:', error);\n        throw error;\n      }\n    },\n    // Generate AI checklist for a card\n    generateAI: async (cardId, requestData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(requestData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Generate AI checklist error:', error);\n        throw error;\n      }\n    }\n  },\n  // Notifications\n  notifications: {\n    // Get all notifications\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data || []\n        };\n      } catch (error) {\n        console.error('Get notifications error:', error);\n        return {\n          data: []\n        }; // Return empty array on error\n      }\n    },\n    // Create notification\n    create: async notificationData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(notificationData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Create notification error:', error);\n        throw error;\n      }\n    },\n    // Mark as read\n    markAsRead: async notificationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}/read`, {\n          method: 'PUT',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Mark notification as read error:', error);\n        throw error;\n      }\n    },\n    // Mark all as read\n    markAllAsRead: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/read-all`, {\n          method: 'PUT',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Mark all notifications as read error:', error);\n        throw error;\n      }\n    },\n    // Delete notification\n    delete: async notificationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete notification error:', error);\n        throw error;\n      }\n    }\n  },\n  // Dashboard\n  dashboard: {\n    // Get dashboard stats\n    getStats: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get dashboard stats error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get dashboard stats'\n        };\n      }\n    }\n  },\n  // AI Projects\n  aiProjects: {\n    // Generate AI project preview\n    generatePreview: async projectData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-preview`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project preview'\n        };\n      }\n    },\n    // Create AI project from preview\n    createFromPreview: async confirmationData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-create`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(confirmationData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Create AI project from preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to create AI project'\n        };\n      }\n    },\n    // Generate AI project directly (simplified flow)\n    generateProject: async projectData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project'\n        };\n      }\n    }\n  }\n};\nexport default realApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "getAuthHeaders", "organizationId", "token", "localStorage", "getItem", "headers", "handleResponse", "response", "result", "json", "ok", "_result$error", "console", "error", "status", "statusText", "url", "body", "errorMessage", "message", "detail", "Error", "realApiService", "auth", "register", "userData", "fetch", "method", "JSON", "stringify", "email", "password", "first_name", "firstName", "last_name", "lastName", "undefined", "organization_name", "organizationName", "organization_slug", "organizationSlug", "data", "tokens", "setItem", "access_token", "user", "organizations", "length", "id", "role", "login", "organization", "getCurrentUser", "updateProfile", "profileData", "logout", "removeItem", "isAuthenticated", "getAccessToken", "getUserRole", "getOrganizationId", "getAll", "getById", "getMembers", "filters", "params", "URLSearchParams", "page", "append", "limit", "search", "queryString", "toString", "teams", "getMemberActivity", "userId", "inviteMember", "inviteData", "updateMemberRole", "roleData", "removeMember", "projects", "create", "projectData", "dataWithOrgId", "organization_id", "update", "delete", "success", "boards", "getByProject", "projectId", "columns", "getByBoard", "boardId", "cards", "columnId", "cardData", "backendCardData", "title", "description", "column_id", "position", "priority", "assigned_to", "assignedTo", "checklist", "dueDate", "due_date", "trim", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toISOString", "warn", "Array", "isArray", "map", "String", "item", "index", "text", "ai_generated", "aiGenerated", "confidence", "metadata", "Object", "keys", "for<PERSON>ach", "key", "log", "cardId", "createBulk", "checklistData", "getByCard", "updateItem", "itemId", "itemData", "deleteItem", "generateAI", "requestData", "notifications", "notificationData", "mark<PERSON><PERSON><PERSON>", "notificationId", "markAllAsRead", "dashboard", "getStats", "aiProjects", "generatePreview", "createFromPreview", "confirmationData", "generateProject"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/realApiService.js"], "sourcesContent": ["// src/utils/realApiService.js\n// Real API service that connects to the backend\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && { 'X-Organization-ID': organizationId })\n  };\n\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async (response) => {\n  const result = await response.json();\n\n  if (!response.ok) {\n    console.error('API Error Response:', {\n      status: response.status,\n      statusText: response.statusText,\n      url: response.url,\n      body: result\n    });\n\n    const errorMessage = result.error?.message || result.message || result.detail || 'API request failed';\n    const error = new Error(errorMessage);\n    error.status = response.status;\n    error.response = result;\n    throw error;\n  }\n\n  return result;\n};\n\nconst realApiService = {\n  // Authentication\n  auth: {\n    // Register new user\n    register: async (userData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email: userData.email,\n            password: userData.password,\n            first_name: userData.firstName || userData.first_name || '',\n            last_name: userData.lastName !== undefined ? userData.lastName : (userData.last_name || ''),\n            organization_name: userData.organizationName || userData.organization_name || '',\n            organization_slug: userData.organizationSlug || userData.organization_slug || ''\n          }),\n        });\n\n        const result = await handleResponse(response);\n        \n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          \n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Registration error:', error);\n        return {\n          data: null,\n          error: error.message || 'Registration failed'\n        };\n      }\n    },\n\n    // Login user\n    login: async (email, password) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email,\n            password\n          }),\n        });\n\n        const result = await handleResponse(response);\n        \n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n\n          // Handle organization and role from enhanced_server response format\n          if (result.data.organization && result.data.role) {\n            localStorage.setItem('organizationId', result.data.organization.id);\n            localStorage.setItem('userRole', result.data.role);\n          } else if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Login error:', error);\n        return {\n          data: null,\n          error: error.message || 'Login failed'\n        };\n      }\n    },\n\n    // Get current user profile\n    getCurrentUser: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get current user error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get user profile'\n        };\n      }\n    },\n\n    // Update user profile\n    updateProfile: async (profileData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(profileData),\n        });\n\n        const result = await handleResponse(response);\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Update profile error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to update profile'\n        };\n      }\n    },\n\n    // Logout\n    logout: async () => {\n      try {\n        // Clear stored tokens and user data\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        localStorage.removeItem('currentUser');\n\n        return {\n          error: null\n        };\n      } catch (error) {\n        return {\n          error: error.message || 'Logout failed'\n        };\n      }\n    },\n\n    // Check if user is authenticated\n    isAuthenticated: () => {\n      const token = localStorage.getItem('accessToken');\n      return !!token;\n    },\n\n    // Get stored access token\n    getAccessToken: () => {\n      return localStorage.getItem('accessToken');\n    },\n\n    // Get user role\n    getUserRole: () => {\n      return localStorage.getItem('userRole') || 'member';\n    },\n\n    // Get organization ID\n    getOrganizationId: () => {\n      return localStorage.getItem('organizationId');\n    }\n  },\n\n  // Organizations\n  organizations: {\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organizations error:', error);\n        throw error;\n      }\n    },\n\n    // Get organization by ID\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization error:', error);\n        throw error;\n      }\n    },\n\n    // Get organization members\n    getMembers: async (organizationId, filters = {}) => {\n      try {\n        // Build query parameters\n        const params = new URLSearchParams();\n        if (filters.page) params.append('page', filters.page);\n        if (filters.limit) params.append('limit', filters.limit);\n        if (filters.search) params.append('search', filters.search);\n        if (filters.role) params.append('role', filters.role);\n\n        const queryString = params.toString();\n        const url = `${API_BASE_URL}/api/v1/organizations/${organizationId}/members${queryString ? `?${queryString}` : ''}`;\n\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization members error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Teams\n  teams: {\n    // Get member activity\n    getMemberActivity: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/teams/${organizationId}/members/${userId}/activity`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get member activity error:', error);\n        throw error;\n      }\n    },\n\n    // Invite team member\n    inviteMember: async (organizationId, inviteData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/invite`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(inviteData),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Invite member error:', error);\n        throw error;\n      }\n    },\n\n    // Update member role\n    updateMemberRole: async (organizationId, userId, roleData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}/role`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(roleData),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Update member role error:', error);\n        throw error;\n      }\n    },\n\n    // Remove member\n    removeMember: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Remove member error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Projects\n  projects: {\n    // Get all projects\n    getAll: async (organizationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'GET',\n          headers: getAuthHeaders(organizationId),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get projects error:', error);\n        throw error;\n      }\n    },\n\n    // Get project by ID\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get project error:', error);\n        throw error;\n      }\n    },\n\n    // Create project\n    create: async (organizationId, projectData) => {\n      try {\n        // Include organization_id in the project data as required by the backend\n        const dataWithOrgId = {\n          ...projectData,\n          organization_id: organizationId\n        };\n\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(organizationId),\n          body: JSON.stringify(dataWithOrgId),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Create project error:', error);\n        throw error;\n      }\n    },\n\n    // Update project\n    update: async (id, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Update project error:', error);\n        throw error;\n      }\n    },\n\n    // Delete project\n    delete: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete project error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Boards\n  boards: {\n    // Get boards by project\n    getByProject: async (projectId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get boards error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Columns\n  columns: {\n    // Get columns by board\n    getByBoard: async (boardId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/columns?board_id=${boardId}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get columns error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Cards/Tasks\n  cards: {\n    // Get all cards (by column_id)\n    getAll: async (columnId = null) => {\n      try {\n        const url = columnId\n          ? `${API_BASE_URL}/api/v1/cards?column_id=${columnId}`\n          : `${API_BASE_URL}/api/v1/cards`;\n\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data || [] };\n      } catch (error) {\n        console.error('Get cards error:', error);\n        return { data: [] }; // Return empty array on error\n      }\n    },\n\n    // Create card\n    create: async (cardData) => {\n      try {\n        // Filter and format data for backend API\n        const backendCardData = {\n          title: cardData.title,\n          description: cardData.description || null,\n          column_id: cardData.column_id || cardData.columnId,\n          position: cardData.position || 0,\n          priority: cardData.priority || 'medium',\n          assigned_to: cardData.assigned_to || cardData.assignedTo || null,\n          checklist: cardData.checklist || null\n        };\n\n        // Handle due_date formatting\n        const dueDate = cardData.due_date || cardData.dueDate;\n        if (dueDate && dueDate.trim() !== '') {\n          // Ensure the date is in ISO format for the backend\n          try {\n            const dateObj = new Date(dueDate);\n            if (!isNaN(dateObj.getTime())) {\n              backendCardData.due_date = dateObj.toISOString();\n            }\n          } catch (error) {\n            console.warn('Invalid due date format:', dueDate);\n          }\n        }\n\n        // Handle assigned_to formatting - ensure it's an array of user IDs\n        const assignedTo = cardData.assigned_to || cardData.assignedTo;\n        if (assignedTo && Array.isArray(assignedTo) && assignedTo.length > 0) {\n          // Ensure all items are strings (user IDs)\n          backendCardData.assigned_to = assignedTo.map(userId =>\n            typeof userId === 'string' ? userId : String(userId)\n          );\n        }\n\n        // Handle checklist formatting\n        const checklist = cardData.checklist;\n        if (checklist && Array.isArray(checklist) && checklist.length > 0) {\n          // Format checklist items for backend\n          backendCardData.checklist = checklist.map((item, index) => ({\n            text: item.text || item.title || '',\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          }));\n        }\n\n        // Validate required fields\n        if (!backendCardData.title || !backendCardData.title.trim()) {\n          throw new Error('Card title is required');\n        }\n        if (!backendCardData.column_id) {\n          throw new Error('Column ID is required');\n        }\n\n        // Remove null values to avoid sending unnecessary data\n        Object.keys(backendCardData).forEach(key => {\n          if (backendCardData[key] === null || backendCardData[key] === undefined) {\n            delete backendCardData[key];\n          }\n        });\n\n        console.log('Original card data:', cardData);\n        console.log('Sending card data to backend:', backendCardData);\n        console.log('Column ID being sent:', backendCardData.column_id);\n\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(backendCardData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Create card error:', error);\n        console.error('Response status:', error.status);\n        console.error('Response details:', error.message);\n\n        // Try to get more details from the response\n        if (error.response) {\n          console.error('Error response body:', error.response);\n        }\n\n        throw error;\n      }\n    },\n\n    // Update card\n    update: async (cardId, cardData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(cardData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Update card error:', error);\n        throw error;\n      }\n    },\n\n    // Delete card\n    delete: async (cardId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete card error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Checklist\n  checklist: {\n    // Create multiple checklist items for a card\n    createBulk: async (cardId, checklistData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(checklistData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Create checklist items error:', error);\n        throw error;\n      }\n    },\n\n    // Get checklist items for a card\n    getByCard: async (cardId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Get checklist items error:', error);\n        return { data: [] };\n      }\n    },\n\n    // Update a checklist item\n    updateItem: async (itemId, itemData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(itemData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Update checklist item error:', error);\n        throw error;\n      }\n    },\n\n    // Delete a checklist item\n    deleteItem: async (itemId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result };\n      } catch (error) {\n        console.error('Delete checklist item error:', error);\n        throw error;\n      }\n    },\n\n    // Generate AI checklist for a card\n    generateAI: async (cardId, requestData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(requestData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Generate AI checklist error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Notifications\n  notifications: {\n    // Get all notifications\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data || [] };\n      } catch (error) {\n        console.error('Get notifications error:', error);\n        return { data: [] }; // Return empty array on error\n      }\n    },\n\n    // Create notification\n    create: async (notificationData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(notificationData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Create notification error:', error);\n        throw error;\n      }\n    },\n\n    // Mark as read\n    markAsRead: async (notificationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}/read`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Mark notification as read error:', error);\n        throw error;\n      }\n    },\n\n    // Mark all as read\n    markAllAsRead: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/read-all`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Mark all notifications as read error:', error);\n        throw error;\n      }\n    },\n\n    // Delete notification\n    delete: async (notificationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete notification error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Dashboard\n  dashboard: {\n    // Get dashboard stats\n    getStats: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get dashboard stats error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get dashboard stats'\n        };\n      }\n    }\n  },\n\n  // AI Projects\n  aiProjects: {\n    // Generate AI project preview\n    generatePreview: async (projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-preview`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project preview'\n        };\n      }\n    },\n\n    // Create AI project from preview\n    createFromPreview: async (confirmationData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-create`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(confirmationData),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Create AI project from preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to create AI project'\n        };\n      }\n    },\n\n    // Generate AI project directly (simplified flow)\n    generateProject: async (projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project'\n        };\n      }\n    }\n  }\n};\n\nexport default realApiService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,cAAc,GAAGA,CAACC,cAAc,GAAG,IAAI,KAAK;EAChD,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACjD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE,kBAAkB;IAClC,IAAIJ,cAAc,IAAI;MAAE,mBAAmB,EAAEA;IAAe,CAAC;EAC/D,CAAC;EAED,IAAIC,KAAK,EAAE;IACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;EAC9C;EAEA,OAAOG,OAAO;AAChB,CAAC;;AAED;AACA,MAAMC,cAAc,GAAG,MAAOC,QAAQ,IAAK;EACzC,MAAMC,MAAM,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEpC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;IAAA,IAAAC,aAAA;IAChBC,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAE;MACnCC,MAAM,EAAEP,QAAQ,CAACO,MAAM;MACvBC,UAAU,EAAER,QAAQ,CAACQ,UAAU;MAC/BC,GAAG,EAAET,QAAQ,CAACS,GAAG;MACjBC,IAAI,EAAET;IACR,CAAC,CAAC;IAEF,MAAMU,YAAY,GAAG,EAAAP,aAAA,GAAAH,MAAM,CAACK,KAAK,cAAAF,aAAA,uBAAZA,aAAA,CAAcQ,OAAO,KAAIX,MAAM,CAACW,OAAO,IAAIX,MAAM,CAACY,MAAM,IAAI,oBAAoB;IACrG,MAAMP,KAAK,GAAG,IAAIQ,KAAK,CAACH,YAAY,CAAC;IACrCL,KAAK,CAACC,MAAM,GAAGP,QAAQ,CAACO,MAAM;IAC9BD,KAAK,CAACN,QAAQ,GAAGC,MAAM;IACvB,MAAMK,KAAK;EACb;EAEA,OAAOL,MAAM;AACf,CAAC;AAED,MAAMc,cAAc,GAAG;EACrB;EACAC,IAAI,EAAE;IACJ;IACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;MAC5B,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,uBAAuB,EAAE;UACnE+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDY,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC;YACnBC,KAAK,EAAEL,QAAQ,CAACK,KAAK;YACrBC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;YAC3BC,UAAU,EAAEP,QAAQ,CAACQ,SAAS,IAAIR,QAAQ,CAACO,UAAU,IAAI,EAAE;YAC3DE,SAAS,EAAET,QAAQ,CAACU,QAAQ,KAAKC,SAAS,GAAGX,QAAQ,CAACU,QAAQ,GAAIV,QAAQ,CAACS,SAAS,IAAI,EAAG;YAC3FG,iBAAiB,EAAEZ,QAAQ,CAACa,gBAAgB,IAAIb,QAAQ,CAACY,iBAAiB,IAAI,EAAE;YAChFE,iBAAiB,EAAEd,QAAQ,CAACe,gBAAgB,IAAIf,QAAQ,CAACc,iBAAiB,IAAI;UAChF,CAAC;QACH,CAAC,CAAC;QAEF,MAAM/B,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;QAE7C;QACA,IAAIC,MAAM,CAACiC,IAAI,IAAIjC,MAAM,CAACiC,IAAI,CAACC,MAAM,EAAE;UACrCvC,YAAY,CAACwC,OAAO,CAAC,aAAa,EAAEnC,MAAM,CAACiC,IAAI,CAACC,MAAM,CAACE,YAAY,CAAC;UACpEzC,YAAY,CAACwC,OAAO,CAAC,aAAa,EAAEf,IAAI,CAACC,SAAS,CAACrB,MAAM,CAACiC,IAAI,CAACI,IAAI,CAAC,CAAC;UAErE,IAAIrC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,IAAItC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/E5C,YAAY,CAACwC,OAAO,CAAC,gBAAgB,EAAEnC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;YAC5E7C,YAAY,CAACwC,OAAO,CAAC,UAAU,EAAEnC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO;UACLR,IAAI,EAAEjC,MAAM,CAACiC,IAAI;UACjB5B,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACA+B,KAAK,EAAE,MAAAA,CAAOpB,KAAK,EAAEC,QAAQ,KAAK;MAChC,IAAI;QACF,MAAMxB,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,oBAAoB,EAAE;UAChE+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDY,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC;YACnBC,KAAK;YACLC;UACF,CAAC;QACH,CAAC,CAAC;QAEF,MAAMvB,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;QAE7C;QACA,IAAIC,MAAM,CAACiC,IAAI,IAAIjC,MAAM,CAACiC,IAAI,CAACC,MAAM,EAAE;UACrCvC,YAAY,CAACwC,OAAO,CAAC,aAAa,EAAEnC,MAAM,CAACiC,IAAI,CAACC,MAAM,CAACE,YAAY,CAAC;UACpEzC,YAAY,CAACwC,OAAO,CAAC,aAAa,EAAEf,IAAI,CAACC,SAAS,CAACrB,MAAM,CAACiC,IAAI,CAACI,IAAI,CAAC,CAAC;;UAErE;UACA,IAAIrC,MAAM,CAACiC,IAAI,CAACU,YAAY,IAAI3C,MAAM,CAACiC,IAAI,CAACQ,IAAI,EAAE;YAChD9C,YAAY,CAACwC,OAAO,CAAC,gBAAgB,EAAEnC,MAAM,CAACiC,IAAI,CAACU,YAAY,CAACH,EAAE,CAAC;YACnE7C,YAAY,CAACwC,OAAO,CAAC,UAAU,EAAEnC,MAAM,CAACiC,IAAI,CAACQ,IAAI,CAAC;UACpD,CAAC,MAAM,IAAIzC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,IAAItC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YACtF5C,YAAY,CAACwC,OAAO,CAAC,gBAAgB,EAAEnC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;YAC5E7C,YAAY,CAACwC,OAAO,CAAC,UAAU,EAAEnC,MAAM,CAACiC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO;UACLR,IAAI,EAAEjC,MAAM,CAACiC,IAAI;UACjB5B,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAiC,cAAc,EAAE,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACF,MAAM7C,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,kBAAkB,EAAE;UAC9D+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAE7C,OAAO;UACLkC,IAAI,EAAEjC,MAAM,CAACiC,IAAI;UACjB5B,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAkC,aAAa,EAAE,MAAOC,WAAW,IAAK;MACpC,IAAI;QACF,MAAM/C,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,kBAAkB,EAAE;UAC9D+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACyB,WAAW;QAClC,CAAC,CAAC;QAEF,MAAM9C,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAE7C,OAAO;UACLkC,IAAI,EAAEjC,MAAM,CAACiC,IAAI;UACjB5B,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAoC,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF;QACApD,YAAY,CAACqD,UAAU,CAAC,aAAa,CAAC;QACtCrD,YAAY,CAACqD,UAAU,CAAC,cAAc,CAAC;QACvCrD,YAAY,CAACqD,UAAU,CAAC,UAAU,CAAC;QACnCrD,YAAY,CAACqD,UAAU,CAAC,gBAAgB,CAAC;QACzCrD,YAAY,CAACqD,UAAU,CAAC,aAAa,CAAC;QAEtC,OAAO;UACL3C,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd,OAAO;UACLA,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAsC,eAAe,EAAEA,CAAA,KAAM;MACrB,MAAMvD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACjD,OAAO,CAAC,CAACF,KAAK;IAChB,CAAC;IAED;IACAwD,cAAc,EAAEA,CAAA,KAAM;MACpB,OAAOvD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC5C,CAAC;IAED;IACAuD,WAAW,EAAEA,CAAA,KAAM;MACjB,OAAOxD,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,QAAQ;IACrD,CAAC;IAED;IACAwD,iBAAiB,EAAEA,CAAA,KAAM;MACvB,OAAOzD,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC/C;EACF,CAAC;EAED;EACA0C,aAAa,EAAE;IACb;IACAe,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAMtD,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,uBAAuB,EAAE;UACnE+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAiD,OAAO,EAAE,MAAOd,EAAE,IAAK;MACrB,IAAI;QACF,MAAMzC,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyBoD,EAAE,EAAE,EAAE;UACzErB,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAkD,UAAU,EAAE,MAAAA,CAAO9D,cAAc,EAAE+D,OAAO,GAAG,CAAC,CAAC,KAAK;MAClD,IAAI;QACF;QACA,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;QACpC,IAAIF,OAAO,CAACG,IAAI,EAAEF,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEJ,OAAO,CAACG,IAAI,CAAC;QACrD,IAAIH,OAAO,CAACK,KAAK,EAAEJ,MAAM,CAACG,MAAM,CAAC,OAAO,EAAEJ,OAAO,CAACK,KAAK,CAAC;QACxD,IAAIL,OAAO,CAACM,MAAM,EAAEL,MAAM,CAACG,MAAM,CAAC,QAAQ,EAAEJ,OAAO,CAACM,MAAM,CAAC;QAC3D,IAAIN,OAAO,CAACf,IAAI,EAAEgB,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEJ,OAAO,CAACf,IAAI,CAAC;QAErD,MAAMsB,WAAW,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC;QACrC,MAAMxD,GAAG,GAAG,GAAGpB,YAAY,yBAAyBK,cAAc,WAAWsE,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE;QAEnH,MAAMhE,QAAQ,GAAG,MAAMmB,KAAK,CAACV,GAAG,EAAE;UAChCW,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA4D,KAAK,EAAE;IACL;IACAC,iBAAiB,EAAE,MAAAA,CAAOzE,cAAc,EAAE0E,MAAM,KAAK;MACnD,IAAI;QACF,MAAMpE,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,iBAAiBK,cAAc,YAAY0E,MAAM,WAAW,EAAE;UACxGhD,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA+D,YAAY,EAAE,MAAAA,CAAO3E,cAAc,EAAE4E,UAAU,KAAK;MAClD,IAAI;QACF,MAAMtE,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyBK,cAAc,SAAS,EAAE;UAC5F0B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACgD,UAAU;QACjC,CAAC,CAAC;QAEF,MAAMrE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAiE,gBAAgB,EAAE,MAAAA,CAAO7E,cAAc,EAAE0E,MAAM,EAAEI,QAAQ,KAAK;MAC5D,IAAI;QACF,MAAMxE,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyBK,cAAc,YAAY0E,MAAM,OAAO,EAAE;UAC5GhD,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACkD,QAAQ;QAC/B,CAAC,CAAC;QAEF,MAAMvE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAmE,YAAY,EAAE,MAAAA,CAAO/E,cAAc,EAAE0E,MAAM,KAAK;MAC9C,IAAI;QACF,MAAMpE,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyBK,cAAc,YAAY0E,MAAM,EAAE,EAAE;UACvGhD,MAAM,EAAE,QAAQ;UAChBtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAoE,QAAQ,EAAE;IACR;IACApB,MAAM,EAAE,MAAO5D,cAAc,IAAK;MAChC,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,kBAAkB,EAAE;UAC9D+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAACC,cAAc;QACxC,CAAC,CAAC;QAEF,MAAMO,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAiD,OAAO,EAAE,MAAOd,EAAE,IAAK;MACrB,IAAI;QACF,MAAMzC,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,oBAAoBoD,EAAE,EAAE,EAAE;UACpErB,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAqE,MAAM,EAAE,MAAAA,CAAOjF,cAAc,EAAEkF,WAAW,KAAK;MAC7C,IAAI;QACF;QACA,MAAMC,aAAa,GAAG;UACpB,GAAGD,WAAW;UACdE,eAAe,EAAEpF;QACnB,CAAC;QAED,MAAMM,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,kBAAkB,EAAE;UAC9D+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAACC,cAAc,CAAC;UACvCgB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACuD,aAAa;QACpC,CAAC,CAAC;QAEF,MAAM5E,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAyE,MAAM,EAAE,MAAAA,CAAOtC,EAAE,EAAEmC,WAAW,KAAK;MACjC,IAAI;QACF,MAAM5E,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,oBAAoBoD,EAAE,EAAE,EAAE;UACpErB,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACsD,WAAW;QAClC,CAAC,CAAC;QAEF,MAAM3E,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA0E,MAAM,EAAE,MAAOvC,EAAE,IAAK;MACpB,IAAI;QACF,MAAMzC,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,oBAAoBoD,EAAE,EAAE,EAAE;UACpErB,MAAM,EAAE,QAAQ;UAChBtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEiF,OAAO,EAAE,IAAI;UAAE/C,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA4E,MAAM,EAAE;IACN;IACAC,YAAY,EAAE,MAAOC,SAAS,IAAK;MACjC,IAAI;QACF,MAAMpF,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,6BAA6B+F,SAAS,EAAE,EAAE;UACpFhE,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA+E,OAAO,EAAE;IACP;IACAC,UAAU,EAAE,MAAOC,OAAO,IAAK;MAC7B,IAAI;QACF,MAAMvF,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,4BAA4BkG,OAAO,EAAE,EAAE;UACjFnE,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACiC,IAAI;MACpB,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAkF,KAAK,EAAE;IACL;IACAlC,MAAM,EAAE,MAAAA,CAAOmC,QAAQ,GAAG,IAAI,KAAK;MACjC,IAAI;QACF,MAAMhF,GAAG,GAAGgF,QAAQ,GAChB,GAAGpG,YAAY,2BAA2BoG,QAAQ,EAAE,GACpD,GAAGpG,YAAY,eAAe;QAElC,MAAMW,QAAQ,GAAG,MAAMmB,KAAK,CAACV,GAAG,EAAE;UAChCW,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC,IAAI,IAAI;QAAG,CAAC;MACpC,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC,OAAO;UAAE4B,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACAyC,MAAM,EAAE,MAAOe,QAAQ,IAAK;MAC1B,IAAI;QACF;QACA,MAAMC,eAAe,GAAG;UACtBC,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrBC,WAAW,EAAEH,QAAQ,CAACG,WAAW,IAAI,IAAI;UACzCC,SAAS,EAAEJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACD,QAAQ;UAClDM,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,IAAI,CAAC;UAChCC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,IAAI,QAAQ;UACvCC,WAAW,EAAEP,QAAQ,CAACO,WAAW,IAAIP,QAAQ,CAACQ,UAAU,IAAI,IAAI;UAChEC,SAAS,EAAET,QAAQ,CAACS,SAAS,IAAI;QACnC,CAAC;;QAED;QACA,MAAMC,OAAO,GAAGV,QAAQ,CAACW,QAAQ,IAAIX,QAAQ,CAACU,OAAO;QACrD,IAAIA,OAAO,IAAIA,OAAO,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACpC;UACA,IAAI;YACF,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACJ,OAAO,CAAC;YACjC,IAAI,CAACK,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;cAC7Bf,eAAe,CAACU,QAAQ,GAAGE,OAAO,CAACI,WAAW,CAAC,CAAC;YAClD;UACF,CAAC,CAAC,OAAOrG,KAAK,EAAE;YACdD,OAAO,CAACuG,IAAI,CAAC,0BAA0B,EAAER,OAAO,CAAC;UACnD;QACF;;QAEA;QACA,MAAMF,UAAU,GAAGR,QAAQ,CAACO,WAAW,IAAIP,QAAQ,CAACQ,UAAU;QAC9D,IAAIA,UAAU,IAAIW,KAAK,CAACC,OAAO,CAACZ,UAAU,CAAC,IAAIA,UAAU,CAAC1D,MAAM,GAAG,CAAC,EAAE;UACpE;UACAmD,eAAe,CAACM,WAAW,GAAGC,UAAU,CAACa,GAAG,CAAC3C,MAAM,IACjD,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG4C,MAAM,CAAC5C,MAAM,CACrD,CAAC;QACH;;QAEA;QACA,MAAM+B,SAAS,GAAGT,QAAQ,CAACS,SAAS;QACpC,IAAIA,SAAS,IAAIU,KAAK,CAACC,OAAO,CAACX,SAAS,CAAC,IAAIA,SAAS,CAAC3D,MAAM,GAAG,CAAC,EAAE;UACjE;UACAmD,eAAe,CAACQ,SAAS,GAAGA,SAAS,CAACY,GAAG,CAAC,CAACE,IAAI,EAAEC,KAAK,MAAM;YAC1DC,IAAI,EAAEF,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACrB,KAAK,IAAI,EAAE;YACnCG,QAAQ,EAAEkB,IAAI,CAAClB,QAAQ,KAAKlE,SAAS,GAAGoF,IAAI,CAAClB,QAAQ,GAAGmB,KAAK;YAC7DE,YAAY,EAAEH,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACG,YAAY,IAAI,KAAK;YAC5DE,UAAU,EAAEL,IAAI,CAACK,UAAU,IAAI,IAAI;YACnCC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI;UAC7B,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,IAAI,CAAC5B,eAAe,CAACC,KAAK,IAAI,CAACD,eAAe,CAACC,KAAK,CAACU,IAAI,CAAC,CAAC,EAAE;UAC3D,MAAM,IAAIxF,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QACA,IAAI,CAAC6E,eAAe,CAACG,SAAS,EAAE;UAC9B,MAAM,IAAIhF,KAAK,CAAC,uBAAuB,CAAC;QAC1C;;QAEA;QACA0G,MAAM,CAACC,IAAI,CAAC9B,eAAe,CAAC,CAAC+B,OAAO,CAACC,GAAG,IAAI;UAC1C,IAAIhC,eAAe,CAACgC,GAAG,CAAC,KAAK,IAAI,IAAIhC,eAAe,CAACgC,GAAG,CAAC,KAAK9F,SAAS,EAAE;YACvE,OAAO8D,eAAe,CAACgC,GAAG,CAAC;UAC7B;QACF,CAAC,CAAC;QAEFtH,OAAO,CAACuH,GAAG,CAAC,qBAAqB,EAAElC,QAAQ,CAAC;QAC5CrF,OAAO,CAACuH,GAAG,CAAC,+BAA+B,EAAEjC,eAAe,CAAC;QAC7DtF,OAAO,CAACuH,GAAG,CAAC,uBAAuB,EAAEjC,eAAe,CAACG,SAAS,CAAC;QAE/D,MAAM9F,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,eAAe,EAAE;UAC3D+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACqE,eAAe;QACtC,CAAC,CAAC;QAEF,MAAM1F,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1CD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACC,MAAM,CAAC;QAC/CF,OAAO,CAACC,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAACM,OAAO,CAAC;;QAEjD;QACA,IAAIN,KAAK,CAACN,QAAQ,EAAE;UAClBK,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACN,QAAQ,CAAC;QACvD;QAEA,MAAMM,KAAK;MACb;IACF,CAAC;IAED;IACAyE,MAAM,EAAE,MAAAA,CAAO8C,MAAM,EAAEnC,QAAQ,KAAK;MAClC,IAAI;QACF,MAAM1F,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,iBAAiBwI,MAAM,EAAE,EAAE;UACrEzG,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACoE,QAAQ;QAC/B,CAAC,CAAC;QAEF,MAAMzF,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA0E,MAAM,EAAE,MAAO6C,MAAM,IAAK;MACxB,IAAI;QACF,MAAM7H,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,iBAAiBwI,MAAM,EAAE,EAAE;UACrEzG,MAAM,EAAE,QAAQ;UAChBtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEiF,OAAO,EAAE,IAAI;UAAE/C,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA6F,SAAS,EAAE;IACT;IACA2B,UAAU,EAAE,MAAAA,CAAOD,MAAM,EAAEE,aAAa,KAAK;MAC3C,IAAI;QACF,MAAM/H,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,2BAA2BwI,MAAM,YAAY,EAAE;UACzFzG,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACyG,aAAa;QACpC,CAAC,CAAC;QAEF,MAAM9H,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA0H,SAAS,EAAE,MAAOH,MAAM,IAAK;MAC3B,IAAI;QACF,MAAM7H,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,2BAA2BwI,MAAM,YAAY,EAAE;UACzFzG,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UAAE4B,IAAI,EAAE;QAAG,CAAC;MACrB;IACF,CAAC;IAED;IACA+F,UAAU,EAAE,MAAAA,CAAOC,MAAM,EAAEC,QAAQ,KAAK;MACtC,IAAI;QACF,MAAMnI,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,+BAA+B6I,MAAM,EAAE,EAAE;UACnF9G,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC6G,QAAQ;QAC/B,CAAC,CAAC;QAEF,MAAMlI,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA8H,UAAU,EAAE,MAAOF,MAAM,IAAK;MAC5B,IAAI;QACF,MAAMlI,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,+BAA+B6I,MAAM,EAAE,EAAE;UACnF9G,MAAM,EAAE,QAAQ;UAChBtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEiF,OAAO,EAAE,IAAI;UAAE/C,IAAI,EAAEjC;QAAO,CAAC;MACxC,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA+H,UAAU,EAAE,MAAAA,CAAOR,MAAM,EAAES,WAAW,KAAK;MACzC,IAAI;QACF,MAAMtI,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,2BAA2BwI,MAAM,wBAAwB,EAAE;UACrGzG,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACgH,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMrI,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAiI,aAAa,EAAE;IACb;IACAjF,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAMtD,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,uBAAuB,EAAE;UACnE+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC,IAAI,IAAI;QAAG,CAAC;MACpC,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,OAAO;UAAE4B,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACAyC,MAAM,EAAE,MAAO6D,gBAAgB,IAAK;MAClC,IAAI;QACF,MAAMxI,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,uBAAuB,EAAE;UACnE+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACkH,gBAAgB;QACvC,CAAC,CAAC;QAEF,MAAMvI,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAmI,UAAU,EAAE,MAAOC,cAAc,IAAK;MACpC,IAAI;QACF,MAAM1I,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyBqJ,cAAc,OAAO,EAAE;UAC1FtH,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAqI,aAAa,EAAE,MAAAA,CAAA,KAAY;MACzB,IAAI;QACF,MAAM3I,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,gCAAgC,EAAE;UAC5E+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEkC,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA0E,MAAM,EAAE,MAAO0D,cAAc,IAAK;MAChC,IAAI;QACF,MAAM1I,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyBqJ,cAAc,EAAE,EAAE;UACrFtH,MAAM,EAAE,QAAQ;UAChBtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAEiF,OAAO,EAAE,IAAI;UAAE/C,IAAI,EAAEjC,MAAM,CAACiC;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAsI,SAAS,EAAE;IACT;IACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;MACpB,IAAI;QACF,MAAM7I,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,yBAAyB,EAAE;UACrE+B,MAAM,EAAE,KAAK;UACbtB,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACLkC,IAAI,EAAEjC,MAAM,CAACiC,IAAI;UACjB5B,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF;EACF,CAAC;EAED;EACAkI,UAAU,EAAE;IACV;IACAC,eAAe,EAAE,MAAOnE,WAAW,IAAK;MACtC,IAAI;QACF,MAAM5E,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,gCAAgC,EAAE;UAC5E+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAACmF,WAAW,CAACE,eAAe,CAAC;UACpDpE,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACsD,WAAW;QAClC,CAAC,CAAC;QAEF,MAAM3E,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACLkC,IAAI,EAAEjC,MAAM;UACZK,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAoI,iBAAiB,EAAE,MAAOC,gBAAgB,IAAK;MAC7C,IAAI;QACF,MAAMjJ,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,+BAA+B,EAAE;UAC3E+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBiB,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC2H,gBAAgB;QACvC,CAAC,CAAC;QAEF,MAAMhJ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACLkC,IAAI,EAAEjC,MAAM;UACZK,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAsI,eAAe,EAAE,MAAOtE,WAAW,IAAK;MACtC,IAAI;QACF,MAAM5E,QAAQ,GAAG,MAAMmB,KAAK,CAAC,GAAG9B,YAAY,8BAA8B,EAAE;UAC1E+B,MAAM,EAAE,MAAM;UACdtB,OAAO,EAAEL,cAAc,CAACmF,WAAW,CAACE,eAAe,CAAC;UACpDpE,IAAI,EAAEW,IAAI,CAACC,SAAS,CAACsD,WAAW;QAClC,CAAC,CAAC;QAEF,MAAM3E,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACLkC,IAAI,EAAEjC,MAAM;UACZK,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UACL4B,IAAI,EAAE,IAAI;UACV5B,KAAK,EAAEA,KAAK,CAACM,OAAO,IAAI;QAC1B,CAAC;MACH;IACF;EACF;AACF,CAAC;AAED,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}