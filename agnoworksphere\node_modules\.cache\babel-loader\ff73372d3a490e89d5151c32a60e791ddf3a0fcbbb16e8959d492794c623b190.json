{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\kanban-board\\\\components\\\\AddCardModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Icon from '../../../components/AppIcon';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport Select from '../../../components/ui/Select';\nimport { getAssignableMembers, getAssignmentRestrictionMessage, getRolePermissions } from '../../../utils/rolePermissions';\nimport { generateAIChecklist } from '../../../utils/aiChecklistService';\nimport { validateTaskAssignment, handleAIError, handleError, displayError } from '../../../utils/errorHandling';\nimport authService from '../../../utils/authService';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddCardModal = ({\n  isOpen,\n  onClose,\n  onSave,\n  columnId,\n  members\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    priority: 'medium',\n    assignedTo: [],\n    dueDate: '',\n    labels: [],\n    checklist: []\n  });\n  const [errors, setErrors] = useState({});\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [isGeneratingChecklist, setIsGeneratingChecklist] = useState(false);\n  const [aiChecklistGenerated, setAiChecklistGenerated] = useState(false);\n\n  // Load user data for role-based permissions\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        var _userResult$data;\n        // Check if user is authenticated first\n        if (!isAuthenticated || !user) {\n          console.log('User not authenticated, using default role');\n          setUserRole('member');\n          setCurrentUser(null);\n          return;\n        }\n\n        // Use user from auth context if available\n        if (user) {\n          setCurrentUser(user);\n          setUserRole(user.role || 'member');\n          return;\n        }\n\n        // Fallback to API call if user not in context\n        const userResult = await authService.getCurrentUser();\n        if (userResult !== null && userResult !== void 0 && (_userResult$data = userResult.data) !== null && _userResult$data !== void 0 && _userResult$data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        } else {\n          setUserRole('member');\n          setCurrentUser(null);\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n        setUserRole('member');\n        setCurrentUser(null);\n      }\n    };\n    if (isOpen) {\n      loadUserData();\n    }\n  }, [isOpen, isAuthenticated, user]);\n  const priorityOptions = [{\n    value: 'low',\n    label: 'Low Priority'\n  }, {\n    value: 'medium',\n    label: 'Medium Priority'\n  }, {\n    value: 'high',\n    label: 'High Priority'\n  }];\n\n  // Filter members based on role permissions\n  const assignableMembers = currentUser ? getAssignableMembers(members, userRole, currentUser.id) : members;\n  const memberOptions = assignableMembers.map(member => ({\n    value: member.id,\n    label: member.name,\n    description: member.role\n  }));\n  const rolePermissions = getRolePermissions(userRole);\n  const assignmentRestrictionMessage = getAssignmentRestrictionMessage(userRole);\n  const labelOptions = [{\n    value: 'bug',\n    label: 'Bug',\n    color: '#ef4444'\n  }, {\n    value: 'feature',\n    label: 'Feature',\n    color: '#3b82f6'\n  }, {\n    value: 'improvement',\n    label: 'Improvement',\n    color: '#10b981'\n  }, {\n    value: 'documentation',\n    label: 'Documentation',\n    color: '#f59e0b'\n  }, {\n    value: 'testing',\n    label: 'Testing',\n    color: '#8b5cf6'\n  }].map(label => ({\n    value: label.value,\n    label: label.label\n  }));\n  const handleInputChange = (field, value) => {\n    // Validate task assignments in real-time\n    if (field === 'assignedTo' && currentUser) {\n      const invalidAssignments = value.filter(userId => {\n        const validationError = validateTaskAssignment(userRole, userId, currentUser.id);\n        return validationError !== null;\n      });\n      if (invalidAssignments.length > 0) {\n        const error = validateTaskAssignment(userRole, invalidAssignments[0], currentUser.id);\n        displayError(error);\n        return; // Don't update the field if assignment is invalid\n      }\n    }\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleAISuggestion = async () => {\n    if (!formData.title.trim()) {\n      alert(\"Please enter a card title first.\");\n      return;\n    }\n    setIsGeneratingChecklist(true);\n    try {\n      // Generate AI checklist\n      const checklistResult = await generateAIChecklist(formData.title, formData.description, formData.priority, 'general' // Could be determined from project context\n      );\n      if (checklistResult.success) {\n        // Enhanced AI suggestions with checklist\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n          .toISOString().split('T')[0],\n          checklist: checklistResult.items\n        };\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate,\n          checklist: mockAI.checklist\n        }));\n        setAiChecklistGenerated(true);\n      } else {\n        const aiError = handleAIError(new Error(checklistResult.error), 'checklist generation');\n        displayError(aiError);\n\n        // Fallback to basic AI suggestion\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]\n        };\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate\n        }));\n      }\n    } catch (error) {\n      const aiError = handleAIError(error, 'AI suggestion');\n      displayError(aiError);\n    } finally {\n      setIsGeneratingChecklist(false);\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.title.trim()) {\n      newErrors.title = 'Card title is required';\n    }\n    if (formData.title.length > 100) {\n      newErrors.title = 'Title must be less than 100 characters';\n    }\n    if (formData.description.length > 500) {\n      newErrors.description = 'Description must be less than 500 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    const newCard = {\n      id: Date.now().toString(),\n      columnId,\n      // For frontend use\n      column_id: columnId,\n      // For backend API\n      title: formData.title.trim(),\n      description: formData.description.trim(),\n      priority: formData.priority,\n      position: 0,\n      // Add position for backend\n      assignedTo: formData.assignedTo,\n      assigned_to: formData.assignedTo,\n      // For backend API\n      dueDate: formData.dueDate || null,\n      due_date: formData.dueDate || null,\n      // For backend API\n      labels: formData.labels.map(labelValue => {\n        const labelData = [{\n          value: 'bug',\n          label: 'Bug',\n          color: '#ef4444'\n        }, {\n          value: 'feature',\n          label: 'Feature',\n          color: '#3b82f6'\n        }, {\n          value: 'improvement',\n          label: 'Improvement',\n          color: '#10b981'\n        }, {\n          value: 'documentation',\n          label: 'Documentation',\n          color: '#f59e0b'\n        }, {\n          value: 'testing',\n          label: 'Testing',\n          color: '#8b5cf6'\n        }].find(l => l.value === labelValue);\n        return {\n          id: labelValue,\n          name: labelData.label,\n          color: labelData.color\n        };\n      }),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      checklist: formData.checklist || [],\n      comments: [],\n      attachments: []\n    };\n    onSave(newCard);\n    handleClose();\n  };\n  const handleClose = () => {\n    setFormData({\n      title: '',\n      description: '',\n      priority: 'medium',\n      assignedTo: [],\n      dueDate: '',\n      labels: [],\n      checklist: []\n    });\n    setErrors({});\n    setAiChecklistGenerated(false);\n    setIsGeneratingChecklist(false);\n    onClose();\n  };\n  if (!isOpen) return null;\n\n  // Show authentication required message if user is not logged in\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-background rounded-lg p-6 w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-foreground\",\n            children: \"Authentication Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-muted-foreground hover:text-foreground\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"X\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted-foreground mb-4\",\n          children: \"You need to log in to create cards. Please log in and try again.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: onClose,\n            variant: \"outline\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black/50 backdrop-blur-sm\",\n      onClick: handleClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-surface rounded-lg shadow-focused w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-text-primary\",\n          children: \"Add New Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"icon\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: \"X\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Card Title\",\n          type: \"text\",\n          placeholder: \"Enter card title...\",\n          value: formData.title,\n          onChange: e => handleInputChange('title', e.target.value),\n          error: errors.title,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end -mt-2 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleAISuggestion,\n            disabled: isGeneratingChecklist,\n            className: \"text-sm text-primary underline hover:no-underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1\",\n            children: isGeneratingChecklist ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Loader2\",\n                size: 14,\n                className: \"animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Generating...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Zap\",\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Generate with AI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-text-primary mb-2\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: \"Enter card description...\",\n            value: formData.description,\n            onChange: e => handleInputChange('description', e.target.value),\n            className: \"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none\",\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-destructive\",\n            children: errors.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Priority\",\n          options: priorityOptions,\n          value: formData.priority,\n          onChange: value => handleInputChange('priority', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            label: \"Assign Members\",\n            options: memberOptions,\n            value: formData.assignedTo,\n            onChange: value => handleInputChange('assignedTo', value),\n            multiple: true,\n            searchable: true,\n            placeholder: rolePermissions.canAssignTasksToOthers ? \"Select team members...\" : \"You can only assign to yourself\",\n            disabled: !rolePermissions.canAssignTasksToSelf && !rolePermissions.canAssignTasksToOthers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), !rolePermissions.canAssignTasksToOthers && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-xs text-text-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Info\",\n              size: 12,\n              className: \"inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), assignmentRestrictionMessage]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Due Date\",\n          type: \"date\",\n          value: formData.dueDate,\n          onChange: e => handleInputChange('dueDate', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Labels\",\n          options: labelOptions,\n          value: formData.labels,\n          onChange: value => handleInputChange('labels', value),\n          multiple: true,\n          placeholder: \"Select labels...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), aiChecklistGenerated && formData.checklist.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-text-primary\",\n              children: \"AI Generated Checklist\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 text-xs text-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Zap\",\n                size: 12,\n                className: \"text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [formData.checklist.length, \" items\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-muted/30 rounded-md p-3 max-h-32 overflow-y-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: [formData.checklist.slice(0, 5).map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 border border-border rounded-sm mt-0.5 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-text-secondary line-clamp-1\",\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 21\n              }, this)), formData.checklist.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-text-secondary pl-5\",\n                children: [\"+\", formData.checklist.length - 5, \" more items...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-text-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Info\",\n              size: 12,\n              className: \"inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), \"You can edit these checklist items after creating the card\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-3 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            variant: \"outline\",\n            onClick: handleClose,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"default\",\n            children: \"Create Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 297,\n    columnNumber: 5\n  }, this);\n};\n_s(AddCardModal, \"aGenZKPbaX8VelU+fkn9c+btQXc=\", false, function () {\n  return [useAuth];\n});\n_c = AddCardModal;\nexport default AddCardModal;\nvar _c;\n$RefreshReg$(_c, \"AddCardModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Icon", "<PERSON><PERSON>", "Input", "Select", "getAssignableMembers", "getAssignmentRestrictionMessage", "getRolePermissions", "generateAIChecklist", "validateTaskAssignment", "handleAIError", "handleError", "displayError", "authService", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddCardModal", "isOpen", "onClose", "onSave", "columnId", "members", "_s", "user", "isAuthenticated", "formData", "setFormData", "title", "description", "priority", "assignedTo", "dueDate", "labels", "checklist", "errors", "setErrors", "currentUser", "setCurrentUser", "userRole", "setUserRole", "isGeneratingChecklist", "setIsGeneratingChecklist", "aiChecklistGenerated", "setAiChecklistGenerated", "loadUserData", "_userResult$data", "console", "log", "role", "userResult", "getCurrentUser", "data", "error", "priorityOptions", "value", "label", "assignableMembers", "id", "memberOptions", "map", "member", "name", "rolePermissions", "assignmentRestrictionMessage", "labelOptions", "color", "handleInputChange", "field", "invalidAssignments", "filter", "userId", "validationError", "length", "prev", "handleAISuggestion", "trim", "alert", "checklistResult", "success", "mockAI", "Date", "now", "toISOString", "split", "items", "aiError", "Error", "validateForm", "newErrors", "Object", "keys", "handleSubmit", "e", "preventDefault", "newCard", "toString", "column_id", "position", "assigned_to", "due_date", "labelValue", "labelData", "find", "l", "createdAt", "updatedAt", "comments", "attachments", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "variant", "onSubmit", "type", "placeholder", "onChange", "target", "required", "disabled", "rows", "options", "multiple", "searchable", "canAssignTasksToOthers", "canAssignTasksToSelf", "slice", "item", "index", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/kanban-board/components/AddCardModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Icon from '../../../components/AppIcon';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport Select from '../../../components/ui/Select';\nimport { getAssignableMembers, getAssignmentRestrictionMessage, getRolePermissions } from '../../../utils/rolePermissions';\nimport { generateAIChecklist } from '../../../utils/aiChecklistService';\nimport { validateTaskAssignment, handleAIError, handleError, displayError } from '../../../utils/errorHandling';\nimport authService from '../../../utils/authService';\nimport { useAuth } from '../../../contexts/AuthContext';\n\nconst AddCardModal = ({ isOpen, onClose, onSave, columnId, members }) => {\n  const { user, isAuthenticated } = useAuth();\n\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    priority: 'medium',\n    assignedTo: [],\n    dueDate: '',\n    labels: [],\n    checklist: []\n  });\n\n  const [errors, setErrors] = useState({});\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [isGeneratingChecklist, setIsGeneratingChecklist] = useState(false);\n  const [aiChecklistGenerated, setAiChecklistGenerated] = useState(false);\n\n  // Load user data for role-based permissions\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        // Check if user is authenticated first\n        if (!isAuthenticated || !user) {\n          console.log('User not authenticated, using default role');\n          setUserRole('member');\n          setCurrentUser(null);\n          return;\n        }\n\n        // Use user from auth context if available\n        if (user) {\n          setCurrentUser(user);\n          setUserRole(user.role || 'member');\n          return;\n        }\n\n        // Fallback to API call if user not in context\n        const userResult = await authService.getCurrentUser();\n        if (userResult?.data?.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        } else {\n          setUserRole('member');\n          setCurrentUser(null);\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n        setUserRole('member');\n        setCurrentUser(null);\n      }\n    };\n\n    if (isOpen) {\n      loadUserData();\n    }\n  }, [isOpen, isAuthenticated, user]);\n\n  const priorityOptions = [\n    { value: 'low', label: 'Low Priority' },\n    { value: 'medium', label: 'Medium Priority' },\n    { value: 'high', label: 'High Priority' }\n  ];\n\n  // Filter members based on role permissions\n  const assignableMembers = currentUser\n    ? getAssignableMembers(members, userRole, currentUser.id)\n    : members;\n\n  const memberOptions = assignableMembers.map(member => ({\n    value: member.id,\n    label: member.name,\n    description: member.role\n  }));\n\n  const rolePermissions = getRolePermissions(userRole);\n  const assignmentRestrictionMessage = getAssignmentRestrictionMessage(userRole);\n\n  const labelOptions = [\n    { value: 'bug', label: 'Bug', color: '#ef4444' },\n    { value: 'feature', label: 'Feature', color: '#3b82f6' },\n    { value: 'improvement', label: 'Improvement', color: '#10b981' },\n    { value: 'documentation', label: 'Documentation', color: '#f59e0b' },\n    { value: 'testing', label: 'Testing', color: '#8b5cf6' }\n  ].map(label => ({\n    value: label.value,\n    label: label.label\n  }));\n\n  const handleInputChange = (field, value) => {\n    // Validate task assignments in real-time\n    if (field === 'assignedTo' && currentUser) {\n      const invalidAssignments = value.filter(userId => {\n        const validationError = validateTaskAssignment(userRole, userId, currentUser.id);\n        return validationError !== null;\n      });\n\n      if (invalidAssignments.length > 0) {\n        const error = validateTaskAssignment(userRole, invalidAssignments[0], currentUser.id);\n        displayError(error);\n        return; // Don't update the field if assignment is invalid\n      }\n    }\n\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleAISuggestion = async () => {\n    if (!formData.title.trim()) {\n      alert(\"Please enter a card title first.\");\n      return;\n    }\n\n    setIsGeneratingChecklist(true);\n\n    try {\n      // Generate AI checklist\n      const checklistResult = await generateAIChecklist(\n        formData.title,\n        formData.description,\n        formData.priority,\n        'general' // Could be determined from project context\n      );\n\n      if (checklistResult.success) {\n        // Enhanced AI suggestions with checklist\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n            .toISOString()\n            .split('T')[0],\n          checklist: checklistResult.items\n        };\n\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate,\n          checklist: mockAI.checklist\n        }));\n\n        setAiChecklistGenerated(true);\n      } else {\n        const aiError = handleAIError(new Error(checklistResult.error), 'checklist generation');\n        displayError(aiError);\n\n        // Fallback to basic AI suggestion\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n            .toISOString()\n            .split('T')[0]\n        };\n\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate\n        }));\n      }\n    } catch (error) {\n      const aiError = handleAIError(error, 'AI suggestion');\n      displayError(aiError);\n    } finally {\n      setIsGeneratingChecklist(false);\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Card title is required';\n    }\n\n    if (formData.title.length > 100) {\n      newErrors.title = 'Title must be less than 100 characters';\n    }\n\n    if (formData.description.length > 500) {\n      newErrors.description = 'Description must be less than 500 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    const newCard = {\n      id: Date.now().toString(),\n      columnId, // For frontend use\n      column_id: columnId, // For backend API\n      title: formData.title.trim(),\n      description: formData.description.trim(),\n      priority: formData.priority,\n      position: 0, // Add position for backend\n      assignedTo: formData.assignedTo,\n      assigned_to: formData.assignedTo, // For backend API\n      dueDate: formData.dueDate || null,\n      due_date: formData.dueDate || null, // For backend API\n      labels: formData.labels.map(labelValue => {\n        const labelData = [\n          { value: 'bug', label: 'Bug', color: '#ef4444' },\n          { value: 'feature', label: 'Feature', color: '#3b82f6' },\n          { value: 'improvement', label: 'Improvement', color: '#10b981' },\n          { value: 'documentation', label: 'Documentation', color: '#f59e0b' },\n          { value: 'testing', label: 'Testing', color: '#8b5cf6' }\n        ].find(l => l.value === labelValue);\n        return {\n          id: labelValue,\n          name: labelData.label,\n          color: labelData.color\n        };\n      }),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      checklist: formData.checklist || [],\n      comments: [],\n      attachments: []\n    };\n\n    onSave(newCard);\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setFormData({\n      title: '',\n      description: '',\n      priority: 'medium',\n      assignedTo: [],\n      dueDate: '',\n      labels: [],\n      checklist: []\n    });\n    setErrors({});\n    setAiChecklistGenerated(false);\n    setIsGeneratingChecklist(false);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  // Show authentication required message if user is not logged in\n  if (!isAuthenticated) {\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n        <div className=\"bg-background rounded-lg p-6 w-full max-w-md mx-4\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-xl font-semibold text-foreground\">Authentication Required</h2>\n            <button\n              onClick={onClose}\n              className=\"text-muted-foreground hover:text-foreground\"\n            >\n              <Icon name=\"X\" size={20} />\n            </button>\n          </div>\n          <p className=\"text-muted-foreground mb-4\">\n            You need to log in to create cards. Please log in and try again.\n          </p>\n          <div className=\"flex justify-end\">\n            <Button onClick={onClose} variant=\"outline\">\n              Close\n            </Button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n        onClick={handleClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-surface rounded-lg shadow-focused w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-border\">\n          <h2 className=\"text-lg font-semibold text-text-primary\">Add New Card</h2>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleClose}\n          >\n            <Icon name=\"X\" size={20} />\n          </Button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n          <Input\n            label=\"Card Title\"\n            type=\"text\"\n            placeholder=\"Enter card title...\"\n            value={formData.title}\n            onChange={(e) => handleInputChange('title', e.target.value)}\n            error={errors.title}\n            required\n          />\n\n          {/* ✨ AI Suggestion Button */}\n          <div className=\"flex justify-end -mt-2 mb-2\">\n            <button\n              type=\"button\"\n              onClick={handleAISuggestion}\n              disabled={isGeneratingChecklist}\n              className=\"text-sm text-primary underline hover:no-underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1\"\n            >\n              {isGeneratingChecklist ? (\n                <>\n                  <Icon name=\"Loader2\" size={14} className=\"animate-spin\" />\n                  <span>Generating...</span>\n                </>\n              ) : (\n                <>\n                  <Icon name=\"Zap\" size={14} />\n                  <span>Generate with AI</span>\n                </>\n              )}\n            </button>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-text-primary mb-2\">\n              Description\n            </label>\n            <textarea\n              placeholder=\"Enter card description...\"\n              value={formData.description}\n              onChange={(e) => handleInputChange('description', e.target.value)}\n              className=\"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none\"\n              rows={3}\n            />\n            {errors.description && (\n              <p className=\"mt-1 text-sm text-destructive\">{errors.description}</p>\n            )}\n          </div>\n\n          <Select\n            label=\"Priority\"\n            options={priorityOptions}\n            value={formData.priority}\n            onChange={(value) => handleInputChange('priority', value)}\n          />\n\n          <div>\n            <Select\n              label=\"Assign Members\"\n              options={memberOptions}\n              value={formData.assignedTo}\n              onChange={(value) => handleInputChange('assignedTo', value)}\n              multiple\n              searchable\n              placeholder={rolePermissions.canAssignTasksToOthers ? \"Select team members...\" : \"You can only assign to yourself\"}\n              disabled={!rolePermissions.canAssignTasksToSelf && !rolePermissions.canAssignTasksToOthers}\n            />\n            {!rolePermissions.canAssignTasksToOthers && (\n              <p className=\"mt-1 text-xs text-text-secondary\">\n                <Icon name=\"Info\" size={12} className=\"inline mr-1\" />\n                {assignmentRestrictionMessage}\n              </p>\n            )}\n          </div>\n\n          <Input\n            label=\"Due Date\"\n            type=\"date\"\n            value={formData.dueDate}\n            onChange={(e) => handleInputChange('dueDate', e.target.value)}\n          />\n\n          <Select\n            label=\"Labels\"\n            options={labelOptions}\n            value={formData.labels}\n            onChange={(value) => handleInputChange('labels', value)}\n            multiple\n            placeholder=\"Select labels...\"\n          />\n\n          {/* AI Generated Checklist Preview */}\n          {aiChecklistGenerated && formData.checklist.length > 0 && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <label className=\"block text-sm font-medium text-text-primary\">\n                  AI Generated Checklist\n                </label>\n                <div className=\"flex items-center space-x-1 text-xs text-text-secondary\">\n                  <Icon name=\"Zap\" size={12} className=\"text-primary\" />\n                  <span>{formData.checklist.length} items</span>\n                </div>\n              </div>\n              <div className=\"bg-muted/30 rounded-md p-3 max-h-32 overflow-y-auto\">\n                <div className=\"space-y-1\">\n                  {formData.checklist.slice(0, 5).map((item, index) => (\n                    <div key={item.id} className=\"flex items-start space-x-2 text-sm\">\n                      <div className=\"w-3 h-3 border border-border rounded-sm mt-0.5 flex-shrink-0\"></div>\n                      <span className=\"text-text-secondary line-clamp-1\">{item.text}</span>\n                    </div>\n                  ))}\n                  {formData.checklist.length > 5 && (\n                    <div className=\"text-xs text-text-secondary pl-5\">\n                      +{formData.checklist.length - 5} more items...\n                    </div>\n                  )}\n                </div>\n              </div>\n              <p className=\"text-xs text-text-secondary\">\n                <Icon name=\"Info\" size={12} className=\"inline mr-1\" />\n                You can edit these checklist items after creating the card\n              </p>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex items-center justify-end space-x-3 pt-4\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              variant=\"default\"\n            >\n              Create Card\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddCardModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,oBAAoB,EAAEC,+BAA+B,EAAEC,kBAAkB,QAAQ,gCAAgC;AAC1H,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,sBAAsB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,YAAY,QAAQ,8BAA8B;AAC/G,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGb,OAAO,CAAC,CAAC;EAE3C,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC4C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC8C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM+C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QAAA,IAAAC,gBAAA;QACF;QACA,IAAI,CAACrB,eAAe,IAAI,CAACD,IAAI,EAAE;UAC7BuB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;UACzDR,WAAW,CAAC,QAAQ,CAAC;UACrBF,cAAc,CAAC,IAAI,CAAC;UACpB;QACF;;QAEA;QACA,IAAId,IAAI,EAAE;UACRc,cAAc,CAACd,IAAI,CAAC;UACpBgB,WAAW,CAAChB,IAAI,CAACyB,IAAI,IAAI,QAAQ,CAAC;UAClC;QACF;;QAEA;QACA,MAAMC,UAAU,GAAG,MAAMvC,WAAW,CAACwC,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,aAAVA,UAAU,gBAAAJ,gBAAA,GAAVI,UAAU,CAAEE,IAAI,cAAAN,gBAAA,eAAhBA,gBAAA,CAAkBtB,IAAI,EAAE;UAC1Bc,cAAc,CAACY,UAAU,CAACE,IAAI,CAAC5B,IAAI,CAAC;UACpCgB,WAAW,CAACU,UAAU,CAACE,IAAI,CAAC5B,IAAI,CAACyB,IAAI,IAAI,QAAQ,CAAC;QACpD,CAAC,MAAM;UACLT,WAAW,CAAC,QAAQ,CAAC;UACrBF,cAAc,CAAC,IAAI,CAAC;QACtB;MACF,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDb,WAAW,CAAC,QAAQ,CAAC;QACrBF,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC;IAED,IAAIpB,MAAM,EAAE;MACV2B,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC3B,MAAM,EAAEO,eAAe,EAAED,IAAI,CAAC,CAAC;EAEnC,MAAM8B,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC7C;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,CAC1C;;EAED;EACA,MAAMC,iBAAiB,GAAGpB,WAAW,GACjClC,oBAAoB,CAACmB,OAAO,EAAEiB,QAAQ,EAAEF,WAAW,CAACqB,EAAE,CAAC,GACvDpC,OAAO;EAEX,MAAMqC,aAAa,GAAGF,iBAAiB,CAACG,GAAG,CAACC,MAAM,KAAK;IACrDN,KAAK,EAAEM,MAAM,CAACH,EAAE;IAChBF,KAAK,EAAEK,MAAM,CAACC,IAAI;IAClBjC,WAAW,EAAEgC,MAAM,CAACZ;EACtB,CAAC,CAAC,CAAC;EAEH,MAAMc,eAAe,GAAG1D,kBAAkB,CAACkC,QAAQ,CAAC;EACpD,MAAMyB,4BAA4B,GAAG5D,+BAA+B,CAACmC,QAAQ,CAAC;EAE9E,MAAM0B,YAAY,GAAG,CACnB;IAAEV,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEU,KAAK,EAAE;EAAU,CAAC,EAChD;IAAEX,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEU,KAAK,EAAE;EAAU,CAAC,EACxD;IAAEX,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEU,KAAK,EAAE;EAAU,CAAC,EAChE;IAAEX,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEU,KAAK,EAAE;EAAU,CAAC,EACpE;IAAEX,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEU,KAAK,EAAE;EAAU,CAAC,CACzD,CAACN,GAAG,CAACJ,KAAK,KAAK;IACdD,KAAK,EAAEC,KAAK,CAACD,KAAK;IAClBC,KAAK,EAAEA,KAAK,CAACA;EACf,CAAC,CAAC,CAAC;EAEH,MAAMW,iBAAiB,GAAGA,CAACC,KAAK,EAAEb,KAAK,KAAK;IAC1C;IACA,IAAIa,KAAK,KAAK,YAAY,IAAI/B,WAAW,EAAE;MACzC,MAAMgC,kBAAkB,GAAGd,KAAK,CAACe,MAAM,CAACC,MAAM,IAAI;QAChD,MAAMC,eAAe,GAAGjE,sBAAsB,CAACgC,QAAQ,EAAEgC,MAAM,EAAElC,WAAW,CAACqB,EAAE,CAAC;QAChF,OAAOc,eAAe,KAAK,IAAI;MACjC,CAAC,CAAC;MAEF,IAAIH,kBAAkB,CAACI,MAAM,GAAG,CAAC,EAAE;QACjC,MAAMpB,KAAK,GAAG9C,sBAAsB,CAACgC,QAAQ,EAAE8B,kBAAkB,CAAC,CAAC,CAAC,EAAEhC,WAAW,CAACqB,EAAE,CAAC;QACrFhD,YAAY,CAAC2C,KAAK,CAAC;QACnB,OAAO,CAAC;MACV;IACF;IAEA1B,WAAW,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACN,KAAK,GAAGb;IAAM,CAAC,CAAC,CAAC;IAClD,IAAIpB,MAAM,CAACiC,KAAK,CAAC,EAAE;MACjBhC,SAAS,CAACsC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACN,KAAK,GAAG;MAAK,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACjD,QAAQ,CAACE,KAAK,CAACgD,IAAI,CAAC,CAAC,EAAE;MAC1BC,KAAK,CAAC,kCAAkC,CAAC;MACzC;IACF;IAEAnC,wBAAwB,CAAC,IAAI,CAAC;IAE9B,IAAI;MACF;MACA,MAAMoC,eAAe,GAAG,MAAMxE,mBAAmB,CAC/CoB,QAAQ,CAACE,KAAK,EACdF,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,QAAQ,EACjB,SAAS,CAAC;MACZ,CAAC;MAED,IAAIgD,eAAe,CAACC,OAAO,EAAE;QAC3B;QACA,MAAMC,MAAM,GAAG;UACbnD,WAAW,EAAEH,QAAQ,CAACG,WAAW,IAAI,uBAAuBH,QAAQ,CAACE,KAAK,sEAAsE;UAChJK,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UAC9BD,OAAO,EAAE,IAAIiD,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;UAAA,CACrDC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAChBlD,SAAS,EAAE4C,eAAe,CAACO;QAC7B,CAAC;QAED1D,WAAW,CAAC+C,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP7C,WAAW,EAAEmD,MAAM,CAACnD,WAAW;UAC/BI,MAAM,EAAE+C,MAAM,CAAC/C,MAAM;UACrBD,OAAO,EAAEgD,MAAM,CAAChD,OAAO;UACvBE,SAAS,EAAE8C,MAAM,CAAC9C;QACpB,CAAC,CAAC,CAAC;QAEHU,uBAAuB,CAAC,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL,MAAM0C,OAAO,GAAG9E,aAAa,CAAC,IAAI+E,KAAK,CAACT,eAAe,CAACzB,KAAK,CAAC,EAAE,sBAAsB,CAAC;QACvF3C,YAAY,CAAC4E,OAAO,CAAC;;QAErB;QACA,MAAMN,MAAM,GAAG;UACbnD,WAAW,EAAEH,QAAQ,CAACG,WAAW,IAAI,uBAAuBH,QAAQ,CAACE,KAAK,sEAAsE;UAChJK,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UAC9BD,OAAO,EAAE,IAAIiD,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CACpDC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACjB,CAAC;QAEDzD,WAAW,CAAC+C,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP7C,WAAW,EAAEmD,MAAM,CAACnD,WAAW;UAC/BI,MAAM,EAAE+C,MAAM,CAAC/C,MAAM;UACrBD,OAAO,EAAEgD,MAAM,CAAChD;QAClB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACd,MAAMiC,OAAO,GAAG9E,aAAa,CAAC6C,KAAK,EAAE,eAAe,CAAC;MACrD3C,YAAY,CAAC4E,OAAO,CAAC;IACvB,CAAC,SAAS;MACR5C,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC/D,QAAQ,CAACE,KAAK,CAACgD,IAAI,CAAC,CAAC,EAAE;MAC1Ba,SAAS,CAAC7D,KAAK,GAAG,wBAAwB;IAC5C;IAEA,IAAIF,QAAQ,CAACE,KAAK,CAAC6C,MAAM,GAAG,GAAG,EAAE;MAC/BgB,SAAS,CAAC7D,KAAK,GAAG,wCAAwC;IAC5D;IAEA,IAAIF,QAAQ,CAACG,WAAW,CAAC4C,MAAM,GAAG,GAAG,EAAE;MACrCgB,SAAS,CAAC5D,WAAW,GAAG,8CAA8C;IACxE;IAEAO,SAAS,CAACqD,SAAS,CAAC;IACpB,OAAOC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAAChB,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,MAAMO,OAAO,GAAG;MACdrC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC;MACzB3E,QAAQ;MAAE;MACV4E,SAAS,EAAE5E,QAAQ;MAAE;MACrBO,KAAK,EAAEF,QAAQ,CAACE,KAAK,CAACgD,IAAI,CAAC,CAAC;MAC5B/C,WAAW,EAAEH,QAAQ,CAACG,WAAW,CAAC+C,IAAI,CAAC,CAAC;MACxC9C,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;MAC3BoE,QAAQ,EAAE,CAAC;MAAE;MACbnE,UAAU,EAAEL,QAAQ,CAACK,UAAU;MAC/BoE,WAAW,EAAEzE,QAAQ,CAACK,UAAU;MAAE;MAClCC,OAAO,EAAEN,QAAQ,CAACM,OAAO,IAAI,IAAI;MACjCoE,QAAQ,EAAE1E,QAAQ,CAACM,OAAO,IAAI,IAAI;MAAE;MACpCC,MAAM,EAAEP,QAAQ,CAACO,MAAM,CAAC2B,GAAG,CAACyC,UAAU,IAAI;QACxC,MAAMC,SAAS,GAAG,CAChB;UAAE/C,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE,KAAK;UAAEU,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEX,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEU,KAAK,EAAE;QAAU,CAAC,EACxD;UAAEX,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,aAAa;UAAEU,KAAK,EAAE;QAAU,CAAC,EAChE;UAAEX,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAE,eAAe;UAAEU,KAAK,EAAE;QAAU,CAAC,EACpE;UAAEX,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEU,KAAK,EAAE;QAAU,CAAC,CACzD,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,KAAK,KAAK8C,UAAU,CAAC;QACnC,OAAO;UACL3C,EAAE,EAAE2C,UAAU;UACdvC,IAAI,EAAEwC,SAAS,CAAC9C,KAAK;UACrBU,KAAK,EAAEoC,SAAS,CAACpC;QACnB,CAAC;MACH,CAAC,CAAC;MACFuC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnCuB,SAAS,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnCjD,SAAS,EAAER,QAAQ,CAACQ,SAAS,IAAI,EAAE;MACnCyE,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;IACf,CAAC;IAEDxF,MAAM,CAAC2E,OAAO,CAAC;IACfc,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMA,WAAW,GAAGA,CAAA,KAAM;IACxBlF,WAAW,CAAC;MACVC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbQ,uBAAuB,CAAC,KAAK,CAAC;IAC9BF,wBAAwB,CAAC,KAAK,CAAC;IAC/BvB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;;EAExB;EACA,IAAI,CAACO,eAAe,EAAE;IACpB,oBACEX,OAAA;MAAKgG,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFjG,OAAA;QAAKgG,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEjG,OAAA;UAAKgG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDjG,OAAA;YAAIgG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFrG,OAAA;YACEsG,OAAO,EAAEjG,OAAQ;YACjB2F,SAAS,EAAC,6CAA6C;YAAAC,QAAA,eAEvDjG,OAAA,CAACf,IAAI;cAAC+D,IAAI,EAAC,GAAG;cAACuD,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrG,OAAA;UAAGgG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrG,OAAA;UAAKgG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BjG,OAAA,CAACd,MAAM;YAACoH,OAAO,EAAEjG,OAAQ;YAACmG,OAAO,EAAC,SAAS;YAAAP,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErG,OAAA;IAAKgG,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElEjG,OAAA;MACEgG,SAAS,EAAC,+CAA+C;MACzDM,OAAO,EAAEP;IAAY;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGFrG,OAAA;MAAKgG,SAAS,EAAC,iGAAiG;MAAAC,QAAA,gBAE9GjG,OAAA;QAAKgG,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EjG,OAAA;UAAIgG,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzErG,OAAA,CAACd,MAAM;UACLsH,OAAO,EAAC,OAAO;UACfD,IAAI,EAAC,MAAM;UACXD,OAAO,EAAEP,WAAY;UAAAE,QAAA,eAErBjG,OAAA,CAACf,IAAI;YAAC+D,IAAI,EAAC,GAAG;YAACuD,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrG,OAAA;QAAMyG,QAAQ,EAAE3B,YAAa;QAACkB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBACrDjG,OAAA,CAACb,KAAK;UACJuD,KAAK,EAAC,YAAY;UAClBgE,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,qBAAqB;UACjClE,KAAK,EAAE7B,QAAQ,CAACE,KAAM;UACtB8F,QAAQ,EAAG7B,CAAC,IAAK1B,iBAAiB,CAAC,OAAO,EAAE0B,CAAC,CAAC8B,MAAM,CAACpE,KAAK,CAAE;UAC5DF,KAAK,EAAElB,MAAM,CAACP,KAAM;UACpBgG,QAAQ;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGFrG,OAAA;UAAKgG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CjG,OAAA;YACE0G,IAAI,EAAC,QAAQ;YACbJ,OAAO,EAAEzC,kBAAmB;YAC5BkD,QAAQ,EAAEpF,qBAAsB;YAChCqE,SAAS,EAAC,+HAA+H;YAAAC,QAAA,EAExItE,qBAAqB,gBACpB3B,OAAA,CAAAE,SAAA;cAAA+F,QAAA,gBACEjG,OAAA,CAACf,IAAI;gBAAC+D,IAAI,EAAC,SAAS;gBAACuD,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DrG,OAAA;gBAAAiG,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC1B,CAAC,gBAEHrG,OAAA,CAAAE,SAAA;cAAA+F,QAAA,gBACEjG,OAAA,CAACf,IAAI;gBAAC+D,IAAI,EAAC,KAAK;gBAACuD,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7BrG,OAAA;gBAAAiG,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC7B;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrG,OAAA;UAAAiG,QAAA,gBACEjG,OAAA;YAAOgG,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrG,OAAA;YACE2G,WAAW,EAAC,2BAA2B;YACvClE,KAAK,EAAE7B,QAAQ,CAACG,WAAY;YAC5B6F,QAAQ,EAAG7B,CAAC,IAAK1B,iBAAiB,CAAC,aAAa,EAAE0B,CAAC,CAAC8B,MAAM,CAACpE,KAAK,CAAE;YAClEuD,SAAS,EAAC,iHAAiH;YAC3HgB,IAAI,EAAE;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACDhF,MAAM,CAACN,WAAW,iBACjBf,OAAA;YAAGgG,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAE5E,MAAM,CAACN;UAAW;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENrG,OAAA,CAACZ,MAAM;UACLsD,KAAK,EAAC,UAAU;UAChBuE,OAAO,EAAEzE,eAAgB;UACzBC,KAAK,EAAE7B,QAAQ,CAACI,QAAS;UACzB4F,QAAQ,EAAGnE,KAAK,IAAKY,iBAAiB,CAAC,UAAU,EAAEZ,KAAK;QAAE;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAEFrG,OAAA;UAAAiG,QAAA,gBACEjG,OAAA,CAACZ,MAAM;YACLsD,KAAK,EAAC,gBAAgB;YACtBuE,OAAO,EAAEpE,aAAc;YACvBJ,KAAK,EAAE7B,QAAQ,CAACK,UAAW;YAC3B2F,QAAQ,EAAGnE,KAAK,IAAKY,iBAAiB,CAAC,YAAY,EAAEZ,KAAK,CAAE;YAC5DyE,QAAQ;YACRC,UAAU;YACVR,WAAW,EAAE1D,eAAe,CAACmE,sBAAsB,GAAG,wBAAwB,GAAG,iCAAkC;YACnHL,QAAQ,EAAE,CAAC9D,eAAe,CAACoE,oBAAoB,IAAI,CAACpE,eAAe,CAACmE;UAAuB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,EACD,CAACpD,eAAe,CAACmE,sBAAsB,iBACtCpH,OAAA;YAAGgG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC7CjG,OAAA,CAACf,IAAI;cAAC+D,IAAI,EAAC,MAAM;cAACuD,IAAI,EAAE,EAAG;cAACP,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACrDnD,4BAA4B;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENrG,OAAA,CAACb,KAAK;UACJuD,KAAK,EAAC,UAAU;UAChBgE,IAAI,EAAC,MAAM;UACXjE,KAAK,EAAE7B,QAAQ,CAACM,OAAQ;UACxB0F,QAAQ,EAAG7B,CAAC,IAAK1B,iBAAiB,CAAC,SAAS,EAAE0B,CAAC,CAAC8B,MAAM,CAACpE,KAAK;QAAE;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEFrG,OAAA,CAACZ,MAAM;UACLsD,KAAK,EAAC,QAAQ;UACduE,OAAO,EAAE9D,YAAa;UACtBV,KAAK,EAAE7B,QAAQ,CAACO,MAAO;UACvByF,QAAQ,EAAGnE,KAAK,IAAKY,iBAAiB,CAAC,QAAQ,EAAEZ,KAAK,CAAE;UACxDyE,QAAQ;UACRP,WAAW,EAAC;QAAkB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAGDxE,oBAAoB,IAAIjB,QAAQ,CAACQ,SAAS,CAACuC,MAAM,GAAG,CAAC,iBACpD3D,OAAA;UAAKgG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjG,OAAA;YAAKgG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjG,OAAA;cAAOgG,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrG,OAAA;cAAKgG,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtEjG,OAAA,CAACf,IAAI;gBAAC+D,IAAI,EAAC,KAAK;gBAACuD,IAAI,EAAE,EAAG;gBAACP,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDrG,OAAA;gBAAAiG,QAAA,GAAOrF,QAAQ,CAACQ,SAAS,CAACuC,MAAM,EAAC,QAAM;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrG,OAAA;YAAKgG,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClEjG,OAAA;cAAKgG,SAAS,EAAC,WAAW;cAAAC,QAAA,GACvBrF,QAAQ,CAACQ,SAAS,CAACkG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACxE,GAAG,CAAC,CAACyE,IAAI,EAAEC,KAAK,kBAC9CxH,OAAA;gBAAmBgG,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBAC/DjG,OAAA;kBAAKgG,SAAS,EAAC;gBAA8D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpFrG,OAAA;kBAAMgG,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEsB,IAAI,CAACE;gBAAI;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAF7DkB,IAAI,CAAC3E,EAAE;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGZ,CACN,CAAC,EACDzF,QAAQ,CAACQ,SAAS,CAACuC,MAAM,GAAG,CAAC,iBAC5B3D,OAAA;gBAAKgG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,GAC/C,EAACrF,QAAQ,CAACQ,SAAS,CAACuC,MAAM,GAAG,CAAC,EAAC,gBAClC;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrG,OAAA;YAAGgG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACxCjG,OAAA,CAACf,IAAI;cAAC+D,IAAI,EAAC,MAAM;cAACuD,IAAI,EAAE,EAAG;cAACP,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8DAExD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAGDrG,OAAA;UAAKgG,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DjG,OAAA,CAACd,MAAM;YACLwH,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAC,SAAS;YACjBF,OAAO,EAAEP,WAAY;YAAAE,QAAA,EACtB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrG,OAAA,CAACd,MAAM;YACLwH,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAC,SAAS;YAAAP,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5F,EAAA,CApcIN,YAAY;EAAA,QACkBL,OAAO;AAAA;AAAA4H,EAAA,GADrCvH,YAAY;AAsclB,eAAeA,YAAY;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}