{"ast": null, "code": "// src/utils/realApiService.js\n// Real API service that connects to the backend\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && {\n      'X-Organization-ID': organizationId\n    })\n  };\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async response => {\n  const result = await response.json();\n  if (!response.ok) {\n    var _result$error;\n    throw new Error(((_result$error = result.error) === null || _result$error === void 0 ? void 0 : _result$error.message) || result.message || 'API request failed');\n  }\n  return result;\n};\nconst realApiService = {\n  // Authentication\n  auth: {\n    // Register new user\n    register: async userData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email: userData.email,\n            password: userData.password,\n            first_name: userData.firstName || userData.first_name || '',\n            last_name: userData.lastName !== undefined ? userData.lastName : userData.last_name || '',\n            organization_name: userData.organizationName || userData.organization_name || '',\n            organization_slug: userData.organizationSlug || userData.organization_slug || ''\n          })\n        });\n        const result = await handleResponse(response);\n\n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Registration error:', error);\n        return {\n          data: null,\n          error: error.message || 'Registration failed'\n        };\n      }\n    },\n    // Login user\n    login: async (email, password) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email,\n            password\n          })\n        });\n        const result = await handleResponse(response);\n\n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n\n          // Handle organization and role from enhanced_server response format\n          if (result.data.organization && result.data.role) {\n            localStorage.setItem('organizationId', result.data.organization.id);\n            localStorage.setItem('userRole', result.data.role);\n          } else if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Login error:', error);\n        return {\n          data: null,\n          error: error.message || 'Login failed'\n        };\n      }\n    },\n    // Get current user profile\n    getCurrentUser: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get current user error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get user profile'\n        };\n      }\n    },\n    // Update user profile\n    updateProfile: async profileData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(profileData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Update profile error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to update profile'\n        };\n      }\n    },\n    // Logout\n    logout: async () => {\n      try {\n        // Clear stored tokens and user data\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        localStorage.removeItem('currentUser');\n        return {\n          error: null\n        };\n      } catch (error) {\n        return {\n          error: error.message || 'Logout failed'\n        };\n      }\n    },\n    // Check if user is authenticated\n    isAuthenticated: () => {\n      const token = localStorage.getItem('accessToken');\n      return !!token;\n    },\n    // Get stored access token\n    getAccessToken: () => {\n      return localStorage.getItem('accessToken');\n    },\n    // Get user role\n    getUserRole: () => {\n      return localStorage.getItem('userRole') || 'member';\n    },\n    // Get organization ID\n    getOrganizationId: () => {\n      return localStorage.getItem('organizationId');\n    }\n  },\n  // Organizations\n  organizations: {\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organizations error:', error);\n        throw error;\n      }\n    },\n    // Get organization by ID\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization error:', error);\n        throw error;\n      }\n    },\n    // Get organization members\n    getMembers: async (organizationId, filters = {}) => {\n      try {\n        // Build query parameters\n        const params = new URLSearchParams();\n        if (filters.page) params.append('page', filters.page);\n        if (filters.limit) params.append('limit', filters.limit);\n        if (filters.search) params.append('search', filters.search);\n        if (filters.role) params.append('role', filters.role);\n        const queryString = params.toString();\n        const url = `${API_BASE_URL}/api/v1/organizations/${organizationId}/members${queryString ? `?${queryString}` : ''}`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization members error:', error);\n        throw error;\n      }\n    }\n  },\n  // Teams\n  teams: {\n    // Get member activity\n    getMemberActivity: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/teams/${organizationId}/members/${userId}/activity`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get member activity error:', error);\n        throw error;\n      }\n    },\n    // Invite team member\n    inviteMember: async (organizationId, inviteData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/invite`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(inviteData)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Invite member error:', error);\n        throw error;\n      }\n    },\n    // Update member role\n    updateMemberRole: async (organizationId, userId, roleData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}/role`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(roleData)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Update member role error:', error);\n        throw error;\n      }\n    },\n    // Remove member\n    removeMember: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Remove member error:', error);\n        throw error;\n      }\n    }\n  },\n  // Projects\n  projects: {\n    // Get all projects\n    getAll: async organizationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'GET',\n          headers: getAuthHeaders(organizationId)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get projects error:', error);\n        throw error;\n      }\n    },\n    // Get project by ID\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get project error:', error);\n        throw error;\n      }\n    },\n    // Create project\n    create: async (organizationId, projectData) => {\n      try {\n        // Include organization_id in the project data as required by the backend\n        const dataWithOrgId = {\n          ...projectData,\n          organization_id: organizationId\n        };\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(organizationId),\n          body: JSON.stringify(dataWithOrgId)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Create project error:', error);\n        throw error;\n      }\n    },\n    // Update project\n    update: async (id, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Update project error:', error);\n        throw error;\n      }\n    },\n    // Delete project\n    delete: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete project error:', error);\n        throw error;\n      }\n    }\n  },\n  // Boards\n  boards: {\n    // Get boards by project\n    getByProject: async projectId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get boards error:', error);\n        throw error;\n      }\n    }\n  },\n  // Columns\n  columns: {\n    // Get columns by board\n    getByBoard: async boardId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/columns?board_id=${boardId}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get columns error:', error);\n        throw error;\n      }\n    }\n  },\n  // Cards/Tasks\n  cards: {\n    // Get all cards (by column_id)\n    getAll: async (columnId = null) => {\n      try {\n        const url = columnId ? `${API_BASE_URL}/api/v1/cards?column_id=${columnId}` : `${API_BASE_URL}/api/v1/cards`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data || []\n        };\n      } catch (error) {\n        console.error('Get cards error:', error);\n        return {\n          data: []\n        }; // Return empty array on error\n      }\n    },\n    // Create card\n    create: async cardData => {\n      try {\n        // Filter and format data for backend API\n        const backendCardData = {\n          title: cardData.title,\n          description: cardData.description || null,\n          column_id: cardData.column_id || cardData.columnId,\n          position: cardData.position || 0,\n          priority: cardData.priority || 'medium',\n          assigned_to: cardData.assigned_to || cardData.assignedTo || null,\n          checklist: cardData.checklist || null\n        };\n\n        // Handle due_date formatting\n        const dueDate = cardData.due_date || cardData.dueDate;\n        if (dueDate && dueDate.trim() !== '') {\n          // Ensure the date is in ISO format for the backend\n          try {\n            const dateObj = new Date(dueDate);\n            if (!isNaN(dateObj.getTime())) {\n              backendCardData.due_date = dateObj.toISOString();\n            }\n          } catch (error) {\n            console.warn('Invalid due date format:', dueDate);\n          }\n        }\n\n        // Handle assigned_to formatting - ensure it's an array of user IDs\n        const assignedTo = cardData.assigned_to || cardData.assignedTo;\n        if (assignedTo && Array.isArray(assignedTo) && assignedTo.length > 0) {\n          // Ensure all items are strings (user IDs)\n          backendCardData.assigned_to = assignedTo.map(userId => typeof userId === 'string' ? userId : String(userId));\n        }\n\n        // Handle checklist formatting\n        const checklist = cardData.checklist;\n        if (checklist && Array.isArray(checklist) && checklist.length > 0) {\n          // Format checklist items for backend\n          backendCardData.checklist = checklist.map((item, index) => ({\n            text: item.text || item.title || '',\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          }));\n        }\n\n        // Validate required fields\n        if (!backendCardData.title || !backendCardData.title.trim()) {\n          throw new Error('Card title is required');\n        }\n        if (!backendCardData.column_id) {\n          throw new Error('Column ID is required');\n        }\n\n        // Remove null values to avoid sending unnecessary data\n        Object.keys(backendCardData).forEach(key => {\n          if (backendCardData[key] === null || backendCardData[key] === undefined) {\n            delete backendCardData[key];\n          }\n        });\n        console.log('Original card data:', cardData);\n        console.log('Sending card data to backend:', backendCardData);\n        console.log('Column ID being sent:', backendCardData.column_id);\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(backendCardData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Create card error:', error);\n        console.error('Response status:', error.status);\n        console.error('Response details:', error.message);\n\n        // Try to get more details from the response\n        if (error.response) {\n          console.error('Error response body:', error.response);\n        }\n        throw error;\n      }\n    },\n    // Update card\n    update: async (cardId, cardData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(cardData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Update card error:', error);\n        throw error;\n      }\n    },\n    // Delete card\n    delete: async cardId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete card error:', error);\n        throw error;\n      }\n    }\n  },\n  // Checklist\n  checklist: {\n    // Create multiple checklist items for a card\n    createBulk: async (cardId, checklistData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(checklistData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Create checklist items error:', error);\n        throw error;\n      }\n    },\n    // Get checklist items for a card\n    getByCard: async cardId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Get checklist items error:', error);\n        return {\n          data: []\n        };\n      }\n    },\n    // Update a checklist item\n    updateItem: async (itemId, itemData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(itemData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Update checklist item error:', error);\n        throw error;\n      }\n    },\n    // Delete a checklist item\n    deleteItem: async itemId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result\n        };\n      } catch (error) {\n        console.error('Delete checklist item error:', error);\n        throw error;\n      }\n    },\n    // Generate AI checklist for a card\n    generateAI: async (cardId, requestData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(requestData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result\n        };\n      } catch (error) {\n        console.error('Generate AI checklist error:', error);\n        throw error;\n      }\n    }\n  },\n  // Notifications\n  notifications: {\n    // Get all notifications\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data || []\n        };\n      } catch (error) {\n        console.error('Get notifications error:', error);\n        return {\n          data: []\n        }; // Return empty array on error\n      }\n    },\n    // Create notification\n    create: async notificationData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(notificationData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Create notification error:', error);\n        throw error;\n      }\n    },\n    // Mark as read\n    markAsRead: async notificationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}/read`, {\n          method: 'PUT',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Mark notification as read error:', error);\n        throw error;\n      }\n    },\n    // Mark all as read\n    markAllAsRead: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/read-all`, {\n          method: 'PUT',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Mark all notifications as read error:', error);\n        throw error;\n      }\n    },\n    // Delete notification\n    delete: async notificationId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          success: true,\n          data: result.data\n        };\n      } catch (error) {\n        console.error('Delete notification error:', error);\n        throw error;\n      }\n    }\n  },\n  // Dashboard\n  dashboard: {\n    // Get dashboard stats\n    getStats: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get dashboard stats error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get dashboard stats'\n        };\n      }\n    }\n  },\n  // AI Projects\n  aiProjects: {\n    // Generate AI project preview\n    generatePreview: async projectData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-preview`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project preview'\n        };\n      }\n    },\n    // Create AI project from preview\n    createFromPreview: async confirmationData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-create`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(confirmationData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Create AI project from preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to create AI project'\n        };\n      }\n    },\n    // Generate AI project directly (simplified flow)\n    generateProject: async projectData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData)\n        });\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project'\n        };\n      }\n    }\n  }\n};\nexport default realApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "getAuthHeaders", "organizationId", "token", "localStorage", "getItem", "headers", "handleResponse", "response", "result", "json", "ok", "_result$error", "Error", "error", "message", "realApiService", "auth", "register", "userData", "fetch", "method", "body", "JSON", "stringify", "email", "password", "first_name", "firstName", "last_name", "lastName", "undefined", "organization_name", "organizationName", "organization_slug", "organizationSlug", "data", "tokens", "setItem", "access_token", "user", "organizations", "length", "id", "role", "console", "login", "organization", "getCurrentUser", "updateProfile", "profileData", "logout", "removeItem", "isAuthenticated", "getAccessToken", "getUserRole", "getOrganizationId", "getAll", "getById", "getMembers", "filters", "params", "URLSearchParams", "page", "append", "limit", "search", "queryString", "toString", "url", "teams", "getMemberActivity", "userId", "inviteMember", "inviteData", "updateMemberRole", "roleData", "removeMember", "projects", "create", "projectData", "dataWithOrgId", "organization_id", "update", "delete", "success", "boards", "getByProject", "projectId", "columns", "getByBoard", "boardId", "cards", "columnId", "cardData", "backendCardData", "title", "description", "column_id", "position", "priority", "assigned_to", "assignedTo", "checklist", "dueDate", "due_date", "trim", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toISOString", "warn", "Array", "isArray", "map", "String", "item", "index", "text", "ai_generated", "aiGenerated", "confidence", "metadata", "Object", "keys", "for<PERSON>ach", "key", "log", "status", "cardId", "createBulk", "checklistData", "getByCard", "updateItem", "itemId", "itemData", "deleteItem", "generateAI", "requestData", "notifications", "notificationData", "mark<PERSON><PERSON><PERSON>", "notificationId", "markAllAsRead", "dashboard", "getStats", "aiProjects", "generatePreview", "createFromPreview", "confirmationData", "generateProject"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/realApiService.js"], "sourcesContent": ["// src/utils/realApiService.js\n// Real API service that connects to the backend\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && { 'X-Organization-ID': organizationId })\n  };\n\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async (response) => {\n  const result = await response.json();\n\n  if (!response.ok) {\n    throw new Error(result.error?.message || result.message || 'API request failed');\n  }\n\n  return result;\n};\n\nconst realApiService = {\n  // Authentication\n  auth: {\n    // Register new user\n    register: async (userData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email: userData.email,\n            password: userData.password,\n            first_name: userData.firstName || userData.first_name || '',\n            last_name: userData.lastName !== undefined ? userData.lastName : (userData.last_name || ''),\n            organization_name: userData.organizationName || userData.organization_name || '',\n            organization_slug: userData.organizationSlug || userData.organization_slug || ''\n          }),\n        });\n\n        const result = await handleResponse(response);\n        \n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n          \n          if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Registration error:', error);\n        return {\n          data: null,\n          error: error.message || 'Registration failed'\n        };\n      }\n    },\n\n    // Login user\n    login: async (email, password) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email,\n            password\n          }),\n        });\n\n        const result = await handleResponse(response);\n        \n        // Store tokens and user info\n        if (result.data && result.data.tokens) {\n          localStorage.setItem('accessToken', result.data.tokens.access_token);\n          localStorage.setItem('currentUser', JSON.stringify(result.data.user));\n\n          // Handle organization and role from enhanced_server response format\n          if (result.data.organization && result.data.role) {\n            localStorage.setItem('organizationId', result.data.organization.id);\n            localStorage.setItem('userRole', result.data.role);\n          } else if (result.data.user.organizations && result.data.user.organizations.length > 0) {\n            localStorage.setItem('organizationId', result.data.user.organizations[0].id);\n            localStorage.setItem('userRole', result.data.user.organizations[0].role);\n          }\n        }\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Login error:', error);\n        return {\n          data: null,\n          error: error.message || 'Login failed'\n        };\n      }\n    },\n\n    // Get current user profile\n    getCurrentUser: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get current user error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get user profile'\n        };\n      }\n    },\n\n    // Update user profile\n    updateProfile: async (profileData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(profileData),\n        });\n\n        const result = await handleResponse(response);\n\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Update profile error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to update profile'\n        };\n      }\n    },\n\n    // Logout\n    logout: async () => {\n      try {\n        // Clear stored tokens and user data\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        localStorage.removeItem('currentUser');\n\n        return {\n          error: null\n        };\n      } catch (error) {\n        return {\n          error: error.message || 'Logout failed'\n        };\n      }\n    },\n\n    // Check if user is authenticated\n    isAuthenticated: () => {\n      const token = localStorage.getItem('accessToken');\n      return !!token;\n    },\n\n    // Get stored access token\n    getAccessToken: () => {\n      return localStorage.getItem('accessToken');\n    },\n\n    // Get user role\n    getUserRole: () => {\n      return localStorage.getItem('userRole') || 'member';\n    },\n\n    // Get organization ID\n    getOrganizationId: () => {\n      return localStorage.getItem('organizationId');\n    }\n  },\n\n  // Organizations\n  organizations: {\n    // Get all organizations\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organizations error:', error);\n        throw error;\n      }\n    },\n\n    // Get organization by ID\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization error:', error);\n        throw error;\n      }\n    },\n\n    // Get organization members\n    getMembers: async (organizationId, filters = {}) => {\n      try {\n        // Build query parameters\n        const params = new URLSearchParams();\n        if (filters.page) params.append('page', filters.page);\n        if (filters.limit) params.append('limit', filters.limit);\n        if (filters.search) params.append('search', filters.search);\n        if (filters.role) params.append('role', filters.role);\n\n        const queryString = params.toString();\n        const url = `${API_BASE_URL}/api/v1/organizations/${organizationId}/members${queryString ? `?${queryString}` : ''}`;\n\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get organization members error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Teams\n  teams: {\n    // Get member activity\n    getMemberActivity: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/teams/${organizationId}/members/${userId}/activity`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get member activity error:', error);\n        throw error;\n      }\n    },\n\n    // Invite team member\n    inviteMember: async (organizationId, inviteData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/invite`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(inviteData),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Invite member error:', error);\n        throw error;\n      }\n    },\n\n    // Update member role\n    updateMemberRole: async (organizationId, userId, roleData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}/role`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(roleData),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Update member role error:', error);\n        throw error;\n      }\n    },\n\n    // Remove member\n    removeMember: async (organizationId, userId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/organizations/${organizationId}/members/${userId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Remove member error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Projects\n  projects: {\n    // Get all projects\n    getAll: async (organizationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'GET',\n          headers: getAuthHeaders(organizationId),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get projects error:', error);\n        throw error;\n      }\n    },\n\n    // Get project by ID\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get project error:', error);\n        throw error;\n      }\n    },\n\n    // Create project\n    create: async (organizationId, projectData) => {\n      try {\n        // Include organization_id in the project data as required by the backend\n        const dataWithOrgId = {\n          ...projectData,\n          organization_id: organizationId\n        };\n\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(organizationId),\n          body: JSON.stringify(dataWithOrgId),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Create project error:', error);\n        throw error;\n      }\n    },\n\n    // Update project\n    update: async (id, projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Update project error:', error);\n        throw error;\n      }\n    },\n\n    // Delete project\n    delete: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete project error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Boards\n  boards: {\n    // Get boards by project\n    getByProject: async (projectId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/boards?project_id=${projectId}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get boards error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Columns\n  columns: {\n    // Get columns by board\n    getByBoard: async (boardId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/columns?board_id=${boardId}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Get columns error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Cards/Tasks\n  cards: {\n    // Get all cards (by column_id)\n    getAll: async (columnId = null) => {\n      try {\n        const url = columnId\n          ? `${API_BASE_URL}/api/v1/cards?column_id=${columnId}`\n          : `${API_BASE_URL}/api/v1/cards`;\n\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data || [] };\n      } catch (error) {\n        console.error('Get cards error:', error);\n        return { data: [] }; // Return empty array on error\n      }\n    },\n\n    // Create card\n    create: async (cardData) => {\n      try {\n        // Filter and format data for backend API\n        const backendCardData = {\n          title: cardData.title,\n          description: cardData.description || null,\n          column_id: cardData.column_id || cardData.columnId,\n          position: cardData.position || 0,\n          priority: cardData.priority || 'medium',\n          assigned_to: cardData.assigned_to || cardData.assignedTo || null,\n          checklist: cardData.checklist || null\n        };\n\n        // Handle due_date formatting\n        const dueDate = cardData.due_date || cardData.dueDate;\n        if (dueDate && dueDate.trim() !== '') {\n          // Ensure the date is in ISO format for the backend\n          try {\n            const dateObj = new Date(dueDate);\n            if (!isNaN(dateObj.getTime())) {\n              backendCardData.due_date = dateObj.toISOString();\n            }\n          } catch (error) {\n            console.warn('Invalid due date format:', dueDate);\n          }\n        }\n\n        // Handle assigned_to formatting - ensure it's an array of user IDs\n        const assignedTo = cardData.assigned_to || cardData.assignedTo;\n        if (assignedTo && Array.isArray(assignedTo) && assignedTo.length > 0) {\n          // Ensure all items are strings (user IDs)\n          backendCardData.assigned_to = assignedTo.map(userId =>\n            typeof userId === 'string' ? userId : String(userId)\n          );\n        }\n\n        // Handle checklist formatting\n        const checklist = cardData.checklist;\n        if (checklist && Array.isArray(checklist) && checklist.length > 0) {\n          // Format checklist items for backend\n          backendCardData.checklist = checklist.map((item, index) => ({\n            text: item.text || item.title || '',\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          }));\n        }\n\n        // Validate required fields\n        if (!backendCardData.title || !backendCardData.title.trim()) {\n          throw new Error('Card title is required');\n        }\n        if (!backendCardData.column_id) {\n          throw new Error('Column ID is required');\n        }\n\n        // Remove null values to avoid sending unnecessary data\n        Object.keys(backendCardData).forEach(key => {\n          if (backendCardData[key] === null || backendCardData[key] === undefined) {\n            delete backendCardData[key];\n          }\n        });\n\n        console.log('Original card data:', cardData);\n        console.log('Sending card data to backend:', backendCardData);\n        console.log('Column ID being sent:', backendCardData.column_id);\n\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(backendCardData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Create card error:', error);\n        console.error('Response status:', error.status);\n        console.error('Response details:', error.message);\n\n        // Try to get more details from the response\n        if (error.response) {\n          console.error('Error response body:', error.response);\n        }\n\n        throw error;\n      }\n    },\n\n    // Update card\n    update: async (cardId, cardData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(cardData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Update card error:', error);\n        throw error;\n      }\n    },\n\n    // Delete card\n    delete: async (cardId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/cards/${cardId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete card error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Checklist\n  checklist: {\n    // Create multiple checklist items for a card\n    createBulk: async (cardId, checklistData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(checklistData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Create checklist items error:', error);\n        throw error;\n      }\n    },\n\n    // Get checklist items for a card\n    getByCard: async (cardId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Get checklist items error:', error);\n        return { data: [] };\n      }\n    },\n\n    // Update a checklist item\n    updateItem: async (itemId, itemData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(itemData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Update checklist item error:', error);\n        throw error;\n      }\n    },\n\n    // Delete a checklist item\n    deleteItem: async (itemId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/checklist/${itemId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result };\n      } catch (error) {\n        console.error('Delete checklist item error:', error);\n        throw error;\n      }\n    },\n\n    // Generate AI checklist for a card\n    generateAI: async (cardId, requestData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/checklist/cards/${cardId}/checklist/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(requestData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result };\n      } catch (error) {\n        console.error('Generate AI checklist error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Notifications\n  notifications: {\n    // Get all notifications\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data || [] };\n      } catch (error) {\n        console.error('Get notifications error:', error);\n        return { data: [] }; // Return empty array on error\n      }\n    },\n\n    // Create notification\n    create: async (notificationData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(notificationData),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Create notification error:', error);\n        throw error;\n      }\n    },\n\n    // Mark as read\n    markAsRead: async (notificationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}/read`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Mark notification as read error:', error);\n        throw error;\n      }\n    },\n\n    // Mark all as read\n    markAllAsRead: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/read-all`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { data: result.data };\n      } catch (error) {\n        console.error('Mark all notifications as read error:', error);\n        throw error;\n      }\n    },\n\n    // Delete notification\n    delete: async (notificationId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/notifications/${notificationId}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return { success: true, data: result.data };\n      } catch (error) {\n        console.error('Delete notification error:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Dashboard\n  dashboard: {\n    // Get dashboard stats\n    getStats: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/stats`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result.data,\n          error: null\n        };\n      } catch (error) {\n        console.error('Get dashboard stats error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to get dashboard stats'\n        };\n      }\n    }\n  },\n\n  // AI Projects\n  aiProjects: {\n    // Generate AI project preview\n    generatePreview: async (projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-preview`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project preview'\n        };\n      }\n    },\n\n    // Create AI project from preview\n    createFromPreview: async (confirmationData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/ai-projects/ai-create`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(confirmationData),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Create AI project from preview error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to create AI project'\n        };\n      }\n    },\n\n    // Generate AI project directly (simplified flow)\n    generateProject: async (projectData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/v1/projects/ai-generate`, {\n          method: 'POST',\n          headers: getAuthHeaders(projectData.organization_id),\n          body: JSON.stringify(projectData),\n        });\n\n        const result = await handleResponse(response);\n        return {\n          data: result,\n          error: null\n        };\n      } catch (error) {\n        console.error('Generate AI project error:', error);\n        return {\n          data: null,\n          error: error.message || 'Failed to generate AI project'\n        };\n      }\n    }\n  }\n};\n\nexport default realApiService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,cAAc,GAAGA,CAACC,cAAc,GAAG,IAAI,KAAK;EAChD,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACjD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE,kBAAkB;IAClC,IAAIJ,cAAc,IAAI;MAAE,mBAAmB,EAAEA;IAAe,CAAC;EAC/D,CAAC;EAED,IAAIC,KAAK,EAAE;IACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;EAC9C;EAEA,OAAOG,OAAO;AAChB,CAAC;;AAED;AACA,MAAMC,cAAc,GAAG,MAAOC,QAAQ,IAAK;EACzC,MAAMC,MAAM,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEpC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;IAAA,IAAAC,aAAA;IAChB,MAAM,IAAIC,KAAK,CAAC,EAAAD,aAAA,GAAAH,MAAM,CAACK,KAAK,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,OAAO,KAAIN,MAAM,CAACM,OAAO,IAAI,oBAAoB,CAAC;EAClF;EAEA,OAAON,MAAM;AACf,CAAC;AAED,MAAMO,cAAc,GAAG;EACrB;EACAC,IAAI,EAAE;IACJ;IACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;MAC5B,IAAI;QACF,MAAMX,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDgB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,KAAK,EAAEN,QAAQ,CAACM,KAAK;YACrBC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;YAC3BC,UAAU,EAAER,QAAQ,CAACS,SAAS,IAAIT,QAAQ,CAACQ,UAAU,IAAI,EAAE;YAC3DE,SAAS,EAAEV,QAAQ,CAACW,QAAQ,KAAKC,SAAS,GAAGZ,QAAQ,CAACW,QAAQ,GAAIX,QAAQ,CAACU,SAAS,IAAI,EAAG;YAC3FG,iBAAiB,EAAEb,QAAQ,CAACc,gBAAgB,IAAId,QAAQ,CAACa,iBAAiB,IAAI,EAAE;YAChFE,iBAAiB,EAAEf,QAAQ,CAACgB,gBAAgB,IAAIhB,QAAQ,CAACe,iBAAiB,IAAI;UAChF,CAAC;QACH,CAAC,CAAC;QAEF,MAAMzB,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;QAE7C;QACA,IAAIC,MAAM,CAAC2B,IAAI,IAAI3B,MAAM,CAAC2B,IAAI,CAACC,MAAM,EAAE;UACrCjC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAE7B,MAAM,CAAC2B,IAAI,CAACC,MAAM,CAACE,YAAY,CAAC;UACpEnC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAEf,IAAI,CAACC,SAAS,CAACf,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAAC,CAAC;UAErE,IAAI/B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,IAAIhC,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/EtC,YAAY,CAACkC,OAAO,CAAC,gBAAgB,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;YAC5EvC,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO;UACLR,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACA+B,KAAK,EAAE,MAAAA,CAAOrB,KAAK,EAAEC,QAAQ,KAAK;MAChC,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB,EAAE;UAChEwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDgB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,KAAK;YACLC;UACF,CAAC;QACH,CAAC,CAAC;QAEF,MAAMjB,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;QAE7C;QACA,IAAIC,MAAM,CAAC2B,IAAI,IAAI3B,MAAM,CAAC2B,IAAI,CAACC,MAAM,EAAE;UACrCjC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAE7B,MAAM,CAAC2B,IAAI,CAACC,MAAM,CAACE,YAAY,CAAC;UACpEnC,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAEf,IAAI,CAACC,SAAS,CAACf,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAAC,CAAC;;UAErE;UACA,IAAI/B,MAAM,CAAC2B,IAAI,CAACW,YAAY,IAAItC,MAAM,CAAC2B,IAAI,CAACQ,IAAI,EAAE;YAChDxC,YAAY,CAACkC,OAAO,CAAC,gBAAgB,EAAE7B,MAAM,CAAC2B,IAAI,CAACW,YAAY,CAACJ,EAAE,CAAC;YACnEvC,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAAC2B,IAAI,CAACQ,IAAI,CAAC;UACpD,CAAC,MAAM,IAAInC,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,IAAIhC,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;YACtFtC,YAAY,CAACkC,OAAO,CAAC,gBAAgB,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;YAC5EvC,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC;UAC1E;QACF;QAEA,OAAO;UACLR,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAiC,cAAc,EAAE,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACF,MAAMxC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,kBAAkB,EAAE;UAC9DwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAE7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAkC,aAAa,EAAE,MAAOC,WAAW,IAAK;MACpC,IAAI;QACF,MAAM1C,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,kBAAkB,EAAE;UAC9DwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC0B,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMzC,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAE7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAoC,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF;QACA/C,YAAY,CAACgD,UAAU,CAAC,aAAa,CAAC;QACtChD,YAAY,CAACgD,UAAU,CAAC,cAAc,CAAC;QACvChD,YAAY,CAACgD,UAAU,CAAC,UAAU,CAAC;QACnChD,YAAY,CAACgD,UAAU,CAAC,gBAAgB,CAAC;QACzChD,YAAY,CAACgD,UAAU,CAAC,aAAa,CAAC;QAEtC,OAAO;UACLtC,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd,OAAO;UACLA,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAsC,eAAe,EAAEA,CAAA,KAAM;MACrB,MAAMlD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACjD,OAAO,CAAC,CAACF,KAAK;IAChB,CAAC;IAED;IACAmD,cAAc,EAAEA,CAAA,KAAM;MACpB,OAAOlD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC5C,CAAC;IAED;IACAkD,WAAW,EAAEA,CAAA,KAAM;MACjB,OAAOnD,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,QAAQ;IACrD,CAAC;IAED;IACAmD,iBAAiB,EAAEA,CAAA,KAAM;MACvB,OAAOpD,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC/C;EACF,CAAC;EAED;EACAoC,aAAa,EAAE;IACb;IACAgB,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAMjD,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA4C,OAAO,EAAE,MAAOf,EAAE,IAAK;MACrB,IAAI;QACF,MAAMnC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyB8C,EAAE,EAAE,EAAE;UACzEtB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA6C,UAAU,EAAE,MAAAA,CAAOzD,cAAc,EAAE0D,OAAO,GAAG,CAAC,CAAC,KAAK;MAClD,IAAI;QACF;QACA,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;QACpC,IAAIF,OAAO,CAACG,IAAI,EAAEF,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEJ,OAAO,CAACG,IAAI,CAAC;QACrD,IAAIH,OAAO,CAACK,KAAK,EAAEJ,MAAM,CAACG,MAAM,CAAC,OAAO,EAAEJ,OAAO,CAACK,KAAK,CAAC;QACxD,IAAIL,OAAO,CAACM,MAAM,EAAEL,MAAM,CAACG,MAAM,CAAC,QAAQ,EAAEJ,OAAO,CAACM,MAAM,CAAC;QAC3D,IAAIN,OAAO,CAAChB,IAAI,EAAEiB,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEJ,OAAO,CAAChB,IAAI,CAAC;QAErD,MAAMuB,WAAW,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC;QACrC,MAAMC,GAAG,GAAG,GAAGxE,YAAY,yBAAyBK,cAAc,WAAWiE,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE;QAEnH,MAAM3D,QAAQ,GAAG,MAAMY,KAAK,CAACiD,GAAG,EAAE;UAChChD,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAwD,KAAK,EAAE;IACL;IACAC,iBAAiB,EAAE,MAAAA,CAAOrE,cAAc,EAAEsE,MAAM,KAAK;MACnD,IAAI;QACF,MAAMhE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,iBAAiBK,cAAc,YAAYsE,MAAM,WAAW,EAAE;UACxGnD,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA2D,YAAY,EAAE,MAAAA,CAAOvE,cAAc,EAAEwE,UAAU,KAAK;MAClD,IAAI;QACF,MAAMlE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyBK,cAAc,SAAS,EAAE;UAC5FmB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACkD,UAAU;QACjC,CAAC,CAAC;QAEF,MAAMjE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA6D,gBAAgB,EAAE,MAAAA,CAAOzE,cAAc,EAAEsE,MAAM,EAAEI,QAAQ,KAAK;MAC5D,IAAI;QACF,MAAMpE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyBK,cAAc,YAAYsE,MAAM,OAAO,EAAE;UAC5GnD,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACoD,QAAQ;QAC/B,CAAC,CAAC;QAEF,MAAMnE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA+D,YAAY,EAAE,MAAAA,CAAO3E,cAAc,EAAEsE,MAAM,KAAK;MAC9C,IAAI;QACF,MAAMhE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyBK,cAAc,YAAYsE,MAAM,EAAE,EAAE;UACvGnD,MAAM,EAAE,QAAQ;UAChBf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAgE,QAAQ,EAAE;IACR;IACArB,MAAM,EAAE,MAAOvD,cAAc,IAAK;MAChC,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,kBAAkB,EAAE;UAC9DwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAACC,cAAc;QACxC,CAAC,CAAC;QAEF,MAAMO,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA4C,OAAO,EAAE,MAAOf,EAAE,IAAK;MACrB,IAAI;QACF,MAAMnC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB8C,EAAE,EAAE,EAAE;UACpEtB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAiE,MAAM,EAAE,MAAAA,CAAO7E,cAAc,EAAE8E,WAAW,KAAK;MAC7C,IAAI;QACF;QACA,MAAMC,aAAa,GAAG;UACpB,GAAGD,WAAW;UACdE,eAAe,EAAEhF;QACnB,CAAC;QAED,MAAMM,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,kBAAkB,EAAE;UAC9DwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAACC,cAAc,CAAC;UACvCoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACyD,aAAa;QACpC,CAAC,CAAC;QAEF,MAAMxE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAqE,MAAM,EAAE,MAAAA,CAAOxC,EAAE,EAAEqC,WAAW,KAAK;MACjC,IAAI;QACF,MAAMxE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB8C,EAAE,EAAE,EAAE;UACpEtB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACwD,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMvE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAsE,MAAM,EAAE,MAAOzC,EAAE,IAAK;MACpB,IAAI;QACF,MAAMnC,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,oBAAoB8C,EAAE,EAAE,EAAE;UACpEtB,MAAM,EAAE,QAAQ;UAChBf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE6E,OAAO,EAAE,IAAI;UAAEjD,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAwE,MAAM,EAAE;IACN;IACAC,YAAY,EAAE,MAAOC,SAAS,IAAK;MACjC,IAAI;QACF,MAAMhF,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,6BAA6B2F,SAAS,EAAE,EAAE;UACpFnE,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA2E,OAAO,EAAE;IACP;IACAC,UAAU,EAAE,MAAOC,OAAO,IAAK;MAC7B,IAAI;QACF,MAAMnF,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,4BAA4B8F,OAAO,EAAE,EAAE;UACjFtE,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAAC2B,IAAI;MACpB,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA8E,KAAK,EAAE;IACL;IACAnC,MAAM,EAAE,MAAAA,CAAOoC,QAAQ,GAAG,IAAI,KAAK;MACjC,IAAI;QACF,MAAMxB,GAAG,GAAGwB,QAAQ,GAChB,GAAGhG,YAAY,2BAA2BgG,QAAQ,EAAE,GACpD,GAAGhG,YAAY,eAAe;QAElC,MAAMW,QAAQ,GAAG,MAAMY,KAAK,CAACiD,GAAG,EAAE;UAChChD,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI,IAAI;QAAG,CAAC;MACpC,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC,OAAO;UAAEsB,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACA2C,MAAM,EAAE,MAAOe,QAAQ,IAAK;MAC1B,IAAI;QACF;QACA,MAAMC,eAAe,GAAG;UACtBC,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrBC,WAAW,EAAEH,QAAQ,CAACG,WAAW,IAAI,IAAI;UACzCC,SAAS,EAAEJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACD,QAAQ;UAClDM,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,IAAI,CAAC;UAChCC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,IAAI,QAAQ;UACvCC,WAAW,EAAEP,QAAQ,CAACO,WAAW,IAAIP,QAAQ,CAACQ,UAAU,IAAI,IAAI;UAChEC,SAAS,EAAET,QAAQ,CAACS,SAAS,IAAI;QACnC,CAAC;;QAED;QACA,MAAMC,OAAO,GAAGV,QAAQ,CAACW,QAAQ,IAAIX,QAAQ,CAACU,OAAO;QACrD,IAAIA,OAAO,IAAIA,OAAO,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACpC;UACA,IAAI;YACF,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACJ,OAAO,CAAC;YACjC,IAAI,CAACK,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;cAC7Bf,eAAe,CAACU,QAAQ,GAAGE,OAAO,CAACI,WAAW,CAAC,CAAC;YAClD;UACF,CAAC,CAAC,OAAOjG,KAAK,EAAE;YACd+B,OAAO,CAACmE,IAAI,CAAC,0BAA0B,EAAER,OAAO,CAAC;UACnD;QACF;;QAEA;QACA,MAAMF,UAAU,GAAGR,QAAQ,CAACO,WAAW,IAAIP,QAAQ,CAACQ,UAAU;QAC9D,IAAIA,UAAU,IAAIW,KAAK,CAACC,OAAO,CAACZ,UAAU,CAAC,IAAIA,UAAU,CAAC5D,MAAM,GAAG,CAAC,EAAE;UACpE;UACAqD,eAAe,CAACM,WAAW,GAAGC,UAAU,CAACa,GAAG,CAAC3C,MAAM,IACjD,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG4C,MAAM,CAAC5C,MAAM,CACrD,CAAC;QACH;;QAEA;QACA,MAAM+B,SAAS,GAAGT,QAAQ,CAACS,SAAS;QACpC,IAAIA,SAAS,IAAIU,KAAK,CAACC,OAAO,CAACX,SAAS,CAAC,IAAIA,SAAS,CAAC7D,MAAM,GAAG,CAAC,EAAE;UACjE;UACAqD,eAAe,CAACQ,SAAS,GAAGA,SAAS,CAACY,GAAG,CAAC,CAACE,IAAI,EAAEC,KAAK,MAAM;YAC1DC,IAAI,EAAEF,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACrB,KAAK,IAAI,EAAE;YACnCG,QAAQ,EAAEkB,IAAI,CAAClB,QAAQ,KAAKpE,SAAS,GAAGsF,IAAI,CAAClB,QAAQ,GAAGmB,KAAK;YAC7DE,YAAY,EAAEH,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACG,YAAY,IAAI,KAAK;YAC5DE,UAAU,EAAEL,IAAI,CAACK,UAAU,IAAI,IAAI;YACnCC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI;UAC7B,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,IAAI,CAAC5B,eAAe,CAACC,KAAK,IAAI,CAACD,eAAe,CAACC,KAAK,CAACU,IAAI,CAAC,CAAC,EAAE;UAC3D,MAAM,IAAI7F,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QACA,IAAI,CAACkF,eAAe,CAACG,SAAS,EAAE;UAC9B,MAAM,IAAIrF,KAAK,CAAC,uBAAuB,CAAC;QAC1C;;QAEA;QACA+G,MAAM,CAACC,IAAI,CAAC9B,eAAe,CAAC,CAAC+B,OAAO,CAACC,GAAG,IAAI;UAC1C,IAAIhC,eAAe,CAACgC,GAAG,CAAC,KAAK,IAAI,IAAIhC,eAAe,CAACgC,GAAG,CAAC,KAAKhG,SAAS,EAAE;YACvE,OAAOgE,eAAe,CAACgC,GAAG,CAAC;UAC7B;QACF,CAAC,CAAC;QAEFlF,OAAO,CAACmF,GAAG,CAAC,qBAAqB,EAAElC,QAAQ,CAAC;QAC5CjD,OAAO,CAACmF,GAAG,CAAC,+BAA+B,EAAEjC,eAAe,CAAC;QAC7DlD,OAAO,CAACmF,GAAG,CAAC,uBAAuB,EAAEjC,eAAe,CAACG,SAAS,CAAC;QAE/D,MAAM1F,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,eAAe,EAAE;UAC3DwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACuE,eAAe;QACtC,CAAC,CAAC;QAEF,MAAMtF,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C+B,OAAO,CAAC/B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACmH,MAAM,CAAC;QAC/CpF,OAAO,CAAC/B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAACC,OAAO,CAAC;;QAEjD;QACA,IAAID,KAAK,CAACN,QAAQ,EAAE;UAClBqC,OAAO,CAAC/B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACN,QAAQ,CAAC;QACvD;QAEA,MAAMM,KAAK;MACb;IACF,CAAC;IAED;IACAqE,MAAM,EAAE,MAAAA,CAAO+C,MAAM,EAAEpC,QAAQ,KAAK;MAClC,IAAI;QACF,MAAMtF,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,iBAAiBqI,MAAM,EAAE,EAAE;UACrE7G,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACsE,QAAQ;QAC/B,CAAC,CAAC;QAEF,MAAMrF,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAsE,MAAM,EAAE,MAAO8C,MAAM,IAAK;MACxB,IAAI;QACF,MAAM1H,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,iBAAiBqI,MAAM,EAAE,EAAE;UACrE7G,MAAM,EAAE,QAAQ;UAChBf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE6E,OAAO,EAAE,IAAI;UAAEjD,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAyF,SAAS,EAAE;IACT;IACA4B,UAAU,EAAE,MAAAA,CAAOD,MAAM,EAAEE,aAAa,KAAK;MAC3C,IAAI;QACF,MAAM5H,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,2BAA2BqI,MAAM,YAAY,EAAE;UACzF7G,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC4G,aAAa;QACpC,CAAC,CAAC;QAEF,MAAM3H,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAuH,SAAS,EAAE,MAAOH,MAAM,IAAK;MAC3B,IAAI;QACF,MAAM1H,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,2BAA2BqI,MAAM,YAAY,EAAE;UACzF7G,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UAAEsB,IAAI,EAAE;QAAG,CAAC;MACrB;IACF,CAAC;IAED;IACAkG,UAAU,EAAE,MAAAA,CAAOC,MAAM,EAAEC,QAAQ,KAAK;MACtC,IAAI;QACF,MAAMhI,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,+BAA+B0I,MAAM,EAAE,EAAE;UACnFlH,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACgH,QAAQ;QAC/B,CAAC,CAAC;QAEF,MAAM/H,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA2H,UAAU,EAAE,MAAOF,MAAM,IAAK;MAC5B,IAAI;QACF,MAAM/H,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,+BAA+B0I,MAAM,EAAE,EAAE;UACnFlH,MAAM,EAAE,QAAQ;UAChBf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE6E,OAAO,EAAE,IAAI;UAAEjD,IAAI,EAAE3B;QAAO,CAAC;MACxC,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACA4H,UAAU,EAAE,MAAAA,CAAOR,MAAM,EAAES,WAAW,KAAK;MACzC,IAAI;QACF,MAAMnI,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,2BAA2BqI,MAAM,wBAAwB,EAAE;UACrG7G,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmH,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMlI,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B;QAAO,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA8H,aAAa,EAAE;IACb;IACAnF,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAMjD,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI,IAAI;QAAG,CAAC;MACpC,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,OAAO;UAAEsB,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACA2C,MAAM,EAAE,MAAO8D,gBAAgB,IAAK;MAClC,IAAI;QACF,MAAMrI,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,uBAAuB,EAAE;UACnEwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACqH,gBAAgB;QACvC,CAAC,CAAC;QAEF,MAAMpI,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAgI,UAAU,EAAE,MAAOC,cAAc,IAAK;MACpC,IAAI;QACF,MAAMvI,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyBkJ,cAAc,OAAO,EAAE;UAC1F1H,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAkI,aAAa,EAAE,MAAAA,CAAA,KAAY;MACzB,IAAI;QACF,MAAMxI,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,gCAAgC,EAAE;UAC5EwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE4B,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC9B,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,MAAMA,KAAK;MACb;IACF,CAAC;IAED;IACAsE,MAAM,EAAE,MAAO2D,cAAc,IAAK;MAChC,IAAI;QACF,MAAMvI,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyBkJ,cAAc,EAAE,EAAE;UACrF1H,MAAM,EAAE,QAAQ;UAChBf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UAAE6E,OAAO,EAAE,IAAI;UAAEjD,IAAI,EAAE3B,MAAM,CAAC2B;QAAK,CAAC;MAC7C,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAmI,SAAS,EAAE;IACT;IACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;MACpB,IAAI;QACF,MAAM1I,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,yBAAyB,EAAE;UACrEwB,MAAM,EAAE,KAAK;UACbf,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM,CAAC2B,IAAI;UACjBtB,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF;EACF,CAAC;EAED;EACAoI,UAAU,EAAE;IACV;IACAC,eAAe,EAAE,MAAOpE,WAAW,IAAK;MACtC,IAAI;QACF,MAAMxE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,gCAAgC,EAAE;UAC5EwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC+E,WAAW,CAACE,eAAe,CAAC;UACpD5D,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACwD,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMvE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM;UACZK,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAsI,iBAAiB,EAAE,MAAOC,gBAAgB,IAAK;MAC7C,IAAI;QACF,MAAM9I,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,+BAA+B,EAAE;UAC3EwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC8H,gBAAgB;QACvC,CAAC,CAAC;QAEF,MAAM7I,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM;UACZK,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF,CAAC;IAED;IACAwI,eAAe,EAAE,MAAOvE,WAAW,IAAK;MACtC,IAAI;QACF,MAAMxE,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAGvB,YAAY,8BAA8B,EAAE;UAC1EwB,MAAM,EAAE,MAAM;UACdf,OAAO,EAAEL,cAAc,CAAC+E,WAAW,CAACE,eAAe,CAAC;UACpD5D,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACwD,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMvE,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAO;UACL4B,IAAI,EAAE3B,MAAM;UACZK,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO;UACLsB,IAAI,EAAE,IAAI;UACVtB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;QAC1B,CAAC;MACH;IACF;EACF;AACF,CAAC;AAED,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}