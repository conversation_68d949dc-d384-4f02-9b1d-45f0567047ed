{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\contexts\\\\ProjectContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../utils/apiService';\nimport authService from '../utils/authService';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectContext = /*#__PURE__*/createContext();\nexport const useProject = () => {\n  _s();\n  const context = useContext(ProjectContext);\n  if (!context) {\n    throw new Error('useProject must be used within a ProjectProvider');\n  }\n  return context;\n};\n_s(useProject, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ProjectProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    isAuthenticated,\n    loading: authLoading\n  } = useAuth();\n  const [currentProject, setCurrentProject] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Load all projects for the current organization\n  const loadProjects = async () => {\n    try {\n      // Check authentication before making API calls\n      if (!isAuthenticated) {\n        console.log('Cannot load projects - user not authenticated');\n        return;\n      }\n      setLoading(true);\n      setError(null);\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n      const response = await apiService.projects.getAll(organizationId);\n      if (response && response.data) {\n        setProjects(response.data);\n\n        // If no current project is set, set the first one as current\n        if (!currentProject && response.data.length > 0) {\n          setCurrentProject(response.data[0]);\n          localStorage.setItem('currentProjectId', response.data[0].id);\n          localStorage.setItem('currentProject', JSON.stringify(response.data[0]));\n        }\n      }\n    } catch (error) {\n      console.error('Failed to load projects:', error);\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Switch to a different project\n  const switchProject = async projectId => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Find project in current projects list\n      let project = projects.find(p => p.id === projectId);\n\n      // If not found in current list, fetch from API\n      if (!project) {\n        project = await apiService.projects.getById(projectId);\n      }\n      if (project) {\n        setCurrentProject(project);\n        localStorage.setItem('currentProjectId', project.id);\n        localStorage.setItem('currentProject', JSON.stringify(project));\n\n        // Emit project change event for other components\n        window.dispatchEvent(new CustomEvent('projectChanged', {\n          detail: {\n            project,\n            projectId\n          }\n        }));\n      } else {\n        throw new Error('Project not found');\n      }\n    } catch (error) {\n      console.error('Failed to switch project:', error);\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Update current project data\n  const updateCurrentProject = updatedProject => {\n    setCurrentProject(updatedProject);\n    localStorage.setItem('currentProject', JSON.stringify(updatedProject));\n\n    // Update in projects list as well\n    setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));\n\n    // Emit project update event\n    window.dispatchEvent(new CustomEvent('projectUpdated', {\n      detail: {\n        project: updatedProject\n      }\n    }));\n  };\n\n  // Add a new project to the list\n  const addProject = newProject => {\n    setProjects(prev => [...prev, newProject]);\n\n    // Emit project added event\n    window.dispatchEvent(new CustomEvent('projectAdded', {\n      detail: {\n        project: newProject\n      }\n    }));\n  };\n\n  // Remove a project from the list\n  const removeProject = projectId => {\n    setProjects(prev => prev.filter(p => p.id !== projectId));\n\n    // If the removed project was the current one, switch to another\n    if (currentProject && currentProject.id === projectId) {\n      const remainingProjects = projects.filter(p => p.id !== projectId);\n      if (remainingProjects.length > 0) {\n        switchProject(remainingProjects[0].id);\n      } else {\n        setCurrentProject(null);\n        localStorage.removeItem('currentProjectId');\n        localStorage.removeItem('currentProject');\n      }\n    }\n\n    // Emit project removed event\n    window.dispatchEvent(new CustomEvent('projectRemoved', {\n      detail: {\n        projectId\n      }\n    }));\n  };\n\n  // Initialize project context\n  useEffect(() => {\n    const initializeProject = async () => {\n      // Don't load projects if user is not authenticated or auth is still loading\n      if (authLoading || !isAuthenticated) {\n        console.log('Skipping project initialization - user not authenticated');\n        return;\n      }\n\n      // Try to get project from localStorage first\n      const storedProjectId = localStorage.getItem('currentProjectId');\n      const storedProject = localStorage.getItem('currentProject');\n      if (storedProject && storedProjectId) {\n        try {\n          const project = JSON.parse(storedProject);\n          setCurrentProject(project);\n        } catch (e) {\n          console.warn('Failed to parse stored project:', e);\n        }\n      }\n\n      // Load all projects\n      await loadProjects();\n    };\n    initializeProject();\n  }, [isAuthenticated, authLoading]); // Re-run when auth state changes\n\n  // Listen for external project updates\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'currentProject' && e.newValue) {\n        try {\n          const project = JSON.parse(e.newValue);\n          setCurrentProject(project);\n        } catch (error) {\n          console.warn('Failed to parse project from storage:', error);\n        }\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n  const value = {\n    currentProject,\n    projects,\n    loading,\n    error,\n    switchProject,\n    updateCurrentProject,\n    addProject,\n    removeProject,\n    loadProjects\n  };\n  return /*#__PURE__*/_jsxDEV(ProjectContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_s2(ProjectProvider, \"Et2h8U7CxI7XzKaOGVc32Fi0J5Q=\", false, function () {\n  return [useAuth];\n});\n_c = ProjectProvider;\nexport default ProjectContext;\nvar _c;\n$RefreshReg$(_c, \"ProjectProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "authService", "useAuth", "jsxDEV", "_jsxDEV", "ProjectContext", "useProject", "_s", "context", "Error", "ProjectProvider", "children", "_s2", "isAuthenticated", "loading", "authLoading", "currentProject", "setCurrentProject", "projects", "setProjects", "setLoading", "error", "setError", "loadProjects", "console", "log", "organizationId", "getOrganizationId", "response", "getAll", "data", "length", "localStorage", "setItem", "id", "JSON", "stringify", "message", "switchProject", "projectId", "project", "find", "p", "getById", "window", "dispatchEvent", "CustomEvent", "detail", "updateCurrentProject", "updatedProject", "prev", "map", "addProject", "newProject", "removeProject", "filter", "remainingProjects", "removeItem", "initializeProject", "storedProjectId", "getItem", "storedProject", "parse", "e", "warn", "handleStorageChange", "key", "newValue", "addEventListener", "removeEventListener", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/contexts/ProjectContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../utils/apiService';\nimport authService from '../utils/authService';\nimport { useAuth } from './AuthContext';\n\nconst ProjectContext = createContext();\n\nexport const useProject = () => {\n  const context = useContext(ProjectContext);\n  if (!context) {\n    throw new Error('useProject must be used within a ProjectProvider');\n  }\n  return context;\n};\n\nexport const ProjectProvider = ({ children }) => {\n  const { isAuthenticated, loading: authLoading } = useAuth();\n  const [currentProject, setCurrentProject] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Load all projects for the current organization\n  const loadProjects = async () => {\n    try {\n      // Check authentication before making API calls\n      if (!isAuthenticated) {\n        console.log('Cannot load projects - user not authenticated');\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      const response = await apiService.projects.getAll(organizationId);\n      if (response && response.data) {\n        setProjects(response.data);\n\n        // If no current project is set, set the first one as current\n        if (!currentProject && response.data.length > 0) {\n          setCurrentProject(response.data[0]);\n          localStorage.setItem('currentProjectId', response.data[0].id);\n          localStorage.setItem('currentProject', JSON.stringify(response.data[0]));\n        }\n      }\n    } catch (error) {\n      console.error('Failed to load projects:', error);\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Switch to a different project\n  const switchProject = async (projectId) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Find project in current projects list\n      let project = projects.find(p => p.id === projectId);\n      \n      // If not found in current list, fetch from API\n      if (!project) {\n        project = await apiService.projects.getById(projectId);\n      }\n\n      if (project) {\n        setCurrentProject(project);\n        localStorage.setItem('currentProjectId', project.id);\n        localStorage.setItem('currentProject', JSON.stringify(project));\n        \n        // Emit project change event for other components\n        window.dispatchEvent(new CustomEvent('projectChanged', {\n          detail: { project, projectId }\n        }));\n      } else {\n        throw new Error('Project not found');\n      }\n    } catch (error) {\n      console.error('Failed to switch project:', error);\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Update current project data\n  const updateCurrentProject = (updatedProject) => {\n    setCurrentProject(updatedProject);\n    localStorage.setItem('currentProject', JSON.stringify(updatedProject));\n    \n    // Update in projects list as well\n    setProjects(prev => prev.map(p => \n      p.id === updatedProject.id ? updatedProject : p\n    ));\n\n    // Emit project update event\n    window.dispatchEvent(new CustomEvent('projectUpdated', {\n      detail: { project: updatedProject }\n    }));\n  };\n\n  // Add a new project to the list\n  const addProject = (newProject) => {\n    setProjects(prev => [...prev, newProject]);\n    \n    // Emit project added event\n    window.dispatchEvent(new CustomEvent('projectAdded', {\n      detail: { project: newProject }\n    }));\n  };\n\n  // Remove a project from the list\n  const removeProject = (projectId) => {\n    setProjects(prev => prev.filter(p => p.id !== projectId));\n    \n    // If the removed project was the current one, switch to another\n    if (currentProject && currentProject.id === projectId) {\n      const remainingProjects = projects.filter(p => p.id !== projectId);\n      if (remainingProjects.length > 0) {\n        switchProject(remainingProjects[0].id);\n      } else {\n        setCurrentProject(null);\n        localStorage.removeItem('currentProjectId');\n        localStorage.removeItem('currentProject');\n      }\n    }\n\n    // Emit project removed event\n    window.dispatchEvent(new CustomEvent('projectRemoved', {\n      detail: { projectId }\n    }));\n  };\n\n  // Initialize project context\n  useEffect(() => {\n    const initializeProject = async () => {\n      // Don't load projects if user is not authenticated or auth is still loading\n      if (authLoading || !isAuthenticated) {\n        console.log('Skipping project initialization - user not authenticated');\n        return;\n      }\n\n      // Try to get project from localStorage first\n      const storedProjectId = localStorage.getItem('currentProjectId');\n      const storedProject = localStorage.getItem('currentProject');\n\n      if (storedProject && storedProjectId) {\n        try {\n          const project = JSON.parse(storedProject);\n          setCurrentProject(project);\n        } catch (e) {\n          console.warn('Failed to parse stored project:', e);\n        }\n      }\n\n      // Load all projects\n      await loadProjects();\n    };\n\n    initializeProject();\n  }, [isAuthenticated, authLoading]); // Re-run when auth state changes\n\n  // Listen for external project updates\n  useEffect(() => {\n    const handleStorageChange = (e) => {\n      if (e.key === 'currentProject' && e.newValue) {\n        try {\n          const project = JSON.parse(e.newValue);\n          setCurrentProject(project);\n        } catch (error) {\n          console.warn('Failed to parse project from storage:', error);\n        }\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  const value = {\n    currentProject,\n    projects,\n    loading,\n    error,\n    switchProject,\n    updateCurrentProject,\n    addProject,\n    removeProject,\n    loadProjects\n  };\n\n  return (\n    <ProjectContext.Provider value={value}>\n      {children}\n    </ProjectContext.Provider>\n  );\n};\n\nexport default ProjectContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,cAAc,gBAAGT,aAAa,CAAC,CAAC;AAEtC,OAAO,MAAMU,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,OAAO,GAAGX,UAAU,CAACQ,cAAc,CAAC;EAC1C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;EACrE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,UAAU;AAQvB,OAAO,MAAMI,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC/C,MAAM;IAAEC,eAAe;IAAEC,OAAO,EAAEC;EAAY,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3D,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEM,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMyB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,IAAI,CAACV,eAAe,EAAE;QACpBW,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;MACF;MAEAL,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMI,cAAc,GAAGzB,WAAW,CAAC0B,iBAAiB,CAAC,CAAC;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAIjB,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEA,MAAMmB,QAAQ,GAAG,MAAM5B,UAAU,CAACkB,QAAQ,CAACW,MAAM,CAACH,cAAc,CAAC;MACjE,IAAIE,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;QAC7BX,WAAW,CAACS,QAAQ,CAACE,IAAI,CAAC;;QAE1B;QACA,IAAI,CAACd,cAAc,IAAIY,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAC/Cd,iBAAiB,CAACW,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;UACnCE,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEL,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAACI,EAAE,CAAC;UAC7DF,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACR,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E;MACF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IACzB,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkB,aAAa,GAAG,MAAOC,SAAS,IAAK;IACzC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAIkB,OAAO,GAAGtB,QAAQ,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,SAAS,CAAC;;MAEpD;MACA,IAAI,CAACC,OAAO,EAAE;QACZA,OAAO,GAAG,MAAMxC,UAAU,CAACkB,QAAQ,CAACyB,OAAO,CAACJ,SAAS,CAAC;MACxD;MAEA,IAAIC,OAAO,EAAE;QACXvB,iBAAiB,CAACuB,OAAO,CAAC;QAC1BR,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEO,OAAO,CAACN,EAAE,CAAC;QACpDF,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACI,OAAO,CAAC,CAAC;;QAE/D;QACAI,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,gBAAgB,EAAE;UACrDC,MAAM,EAAE;YAAEP,OAAO;YAAED;UAAU;QAC/B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAI9B,KAAK,CAAC,mBAAmB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IACzB,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,oBAAoB,GAAIC,cAAc,IAAK;IAC/ChC,iBAAiB,CAACgC,cAAc,CAAC;IACjCjB,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACa,cAAc,CAAC,CAAC;;IAEtE;IACA9B,WAAW,CAAC+B,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACT,CAAC,IAC5BA,CAAC,CAACR,EAAE,KAAKe,cAAc,CAACf,EAAE,GAAGe,cAAc,GAAGP,CAChD,CAAC,CAAC;;IAEF;IACAE,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,gBAAgB,EAAE;MACrDC,MAAM,EAAE;QAAEP,OAAO,EAAES;MAAe;IACpC,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjClC,WAAW,CAAC+B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEG,UAAU,CAAC,CAAC;;IAE1C;IACAT,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAE;MACnDC,MAAM,EAAE;QAAEP,OAAO,EAAEa;MAAW;IAChC,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIf,SAAS,IAAK;IACnCpB,WAAW,CAAC+B,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACb,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,SAAS,CAAC,CAAC;;IAEzD;IACA,IAAIvB,cAAc,IAAIA,cAAc,CAACkB,EAAE,KAAKK,SAAS,EAAE;MACrD,MAAMiB,iBAAiB,GAAGtC,QAAQ,CAACqC,MAAM,CAACb,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,SAAS,CAAC;MAClE,IAAIiB,iBAAiB,CAACzB,MAAM,GAAG,CAAC,EAAE;QAChCO,aAAa,CAACkB,iBAAiB,CAAC,CAAC,CAAC,CAACtB,EAAE,CAAC;MACxC,CAAC,MAAM;QACLjB,iBAAiB,CAAC,IAAI,CAAC;QACvBe,YAAY,CAACyB,UAAU,CAAC,kBAAkB,CAAC;QAC3CzB,YAAY,CAACyB,UAAU,CAAC,gBAAgB,CAAC;MAC3C;IACF;;IAEA;IACAb,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,gBAAgB,EAAE;MACrDC,MAAM,EAAE;QAAER;MAAU;IACtB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACd,MAAM2D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC;MACA,IAAI3C,WAAW,IAAI,CAACF,eAAe,EAAE;QACnCW,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE;MACF;;MAEA;MACA,MAAMkC,eAAe,GAAG3B,YAAY,CAAC4B,OAAO,CAAC,kBAAkB,CAAC;MAChE,MAAMC,aAAa,GAAG7B,YAAY,CAAC4B,OAAO,CAAC,gBAAgB,CAAC;MAE5D,IAAIC,aAAa,IAAIF,eAAe,EAAE;QACpC,IAAI;UACF,MAAMnB,OAAO,GAAGL,IAAI,CAAC2B,KAAK,CAACD,aAAa,CAAC;UACzC5C,iBAAiB,CAACuB,OAAO,CAAC;QAC5B,CAAC,CAAC,OAAOuB,CAAC,EAAE;UACVvC,OAAO,CAACwC,IAAI,CAAC,iCAAiC,EAAED,CAAC,CAAC;QACpD;MACF;;MAEA;MACA,MAAMxC,YAAY,CAAC,CAAC;IACtB,CAAC;IAEDmC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC7C,eAAe,EAAEE,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEpC;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMkE,mBAAmB,GAAIF,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACG,GAAG,KAAK,gBAAgB,IAAIH,CAAC,CAACI,QAAQ,EAAE;QAC5C,IAAI;UACF,MAAM3B,OAAO,GAAGL,IAAI,CAAC2B,KAAK,CAACC,CAAC,CAACI,QAAQ,CAAC;UACtClD,iBAAiB,CAACuB,OAAO,CAAC;QAC5B,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACdG,OAAO,CAACwC,IAAI,CAAC,uCAAuC,EAAE3C,KAAK,CAAC;QAC9D;MACF;IACF,CAAC;IAEDuB,MAAM,CAACwB,gBAAgB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IACvD,OAAO,MAAMrB,MAAM,CAACyB,mBAAmB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;EACzE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,KAAK,GAAG;IACZtD,cAAc;IACdE,QAAQ;IACRJ,OAAO;IACPO,KAAK;IACLiB,aAAa;IACbU,oBAAoB;IACpBI,UAAU;IACVE,aAAa;IACb/B;EACF,CAAC;EAED,oBACEnB,OAAA,CAACC,cAAc,CAACkE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3D,QAAA,EACnCA;EAAQ;IAAA6D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;AAAC/D,GAAA,CA5LWF,eAAe;EAAA,QACwBR,OAAO;AAAA;AAAA0E,EAAA,GAD9ClE,eAAe;AA8L5B,eAAeL,cAAc;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}