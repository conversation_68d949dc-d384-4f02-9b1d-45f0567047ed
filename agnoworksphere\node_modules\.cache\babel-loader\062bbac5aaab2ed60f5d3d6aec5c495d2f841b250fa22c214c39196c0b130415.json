{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\kanban-board\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../utils/authService';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport BoardHeader from './components/BoardHeader';\nimport BoardColumn from './components/BoardColumn';\nimport AddCardModal from './components/AddCardModal';\nimport AddColumnModal from './components/AddColumnModal';\nimport InviteMemberModal from './components/InviteMemberModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KanbanBoard = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n\n  // User state and role\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n\n  // Project context - get from location state or default\n  const [currentProject] = useState(() => {\n    const locationState = window.location.state;\n    return locationState !== null && locationState !== void 0 && locationState.projectId ? {\n      id: locationState.projectId,\n      name: 'Current Project',\n      memberRole: 'assigned' // This would come from API\n    } : {\n      id: 1,\n      name: 'Website Redesign',\n      memberRole: 'assigned' // assigned, not-assigned\n    };\n  });\n\n  // Check if current user is assigned to this project\n  const isUserAssignedToProject = () => {\n    // For members, check if they're assigned to this specific project\n    if (userRole === 'member') {\n      return currentProject.memberRole === 'assigned';\n    }\n    // Admins and owners have access to all projects\n    return ['admin', 'owner'].includes(userRole);\n  };\n\n  // Mock data\n  const [board] = useState({\n    id: 'board-1',\n    title: 'Project Management Board',\n    description: 'Main project tracking board for Q4 initiatives',\n    isPrivate: false,\n    createdAt: '2025-01-15T10:00:00Z',\n    updatedAt: '2025-01-28T05:54:23Z'\n  });\n\n  // Real members data - will be loaded from team service\n  const [members, setMembers] = useState([]);\n\n  // Initialize with default columns to ensure they're always visible\n  const [columns, setColumns] = useState([{\n    id: 'col-1',\n    title: 'To-Do',\n    status: 'todo',\n    order: 1\n  }, {\n    id: 'col-2',\n    title: 'In Progress',\n    status: 'in-progress',\n    order: 2\n  }, {\n    id: 'col-3',\n    title: 'Review',\n    status: 'review',\n    order: 3\n  }, {\n    id: 'col-4',\n    title: 'Done',\n    status: 'done',\n    order: 4\n  }]);\n  const [cards, setCards] = useState([{\n    id: 'card-1',\n    columnId: 'col-1',\n    title: 'Design user authentication flow',\n    description: 'Create wireframes and mockups for the login and registration process',\n    priority: 'high',\n    assignedTo: ['user-1', 'user-2'],\n    dueDate: '2025-02-05',\n    labels: [{\n      id: 'design',\n      name: 'Design',\n      color: '#3b82f6'\n    }, {\n      id: 'ux',\n      name: 'UX',\n      color: '#8b5cf6'\n    }],\n    checklist: [{\n      id: 'check-1',\n      text: 'Research competitor flows',\n      completed: true\n    }, {\n      id: 'check-2',\n      text: 'Create wireframes',\n      completed: false\n    }, {\n      id: 'check-3',\n      text: 'Design mockups',\n      completed: false\n    }],\n    comments: [{\n      id: 'comment-1',\n      author: 'user-2',\n      content: 'Should we include social login options?',\n      createdAt: '2025-01-27T14:30:00Z'\n    }],\n    attachments: [],\n    createdAt: '2025-01-25T09:00:00Z',\n    updatedAt: '2025-01-27T14:30:00Z'\n  }, {\n    id: 'card-2',\n    columnId: 'col-1',\n    title: 'Set up project repository',\n    description: 'Initialize Git repository with proper folder structure and documentation',\n    priority: 'medium',\n    assignedTo: ['user-3'],\n    dueDate: '2025-01-30',\n    labels: [{\n      id: 'development',\n      name: 'Development',\n      color: '#10b981'\n    }],\n    checklist: [],\n    comments: [],\n    attachments: [],\n    createdAt: '2025-01-26T11:00:00Z',\n    updatedAt: '2025-01-26T11:00:00Z'\n  }, {\n    id: 'card-3',\n    columnId: 'col-2',\n    title: 'Implement user registration API',\n    description: 'Build backend endpoints for user registration with validation and email verification',\n    priority: 'high',\n    assignedTo: ['user-2', 'user-5'],\n    dueDate: '2025-02-10',\n    labels: [{\n      id: 'backend',\n      name: 'Backend',\n      color: '#f59e0b'\n    }, {\n      id: 'api',\n      name: 'API',\n      color: '#ef4444'\n    }],\n    checklist: [{\n      id: 'check-4',\n      text: 'Design database schema',\n      completed: true\n    }, {\n      id: 'check-5',\n      text: 'Implement validation',\n      completed: true\n    }, {\n      id: 'check-6',\n      text: 'Add email verification',\n      completed: false\n    }, {\n      id: 'check-7',\n      text: 'Write unit tests',\n      completed: false\n    }],\n    comments: [{\n      id: 'comment-2',\n      author: 'user-1',\n      content: 'Make sure to include proper error handling',\n      createdAt: '2025-01-26T16:45:00Z'\n    }, {\n      id: 'comment-3',\n      author: 'user-5',\n      content: 'Working on the email service integration',\n      createdAt: '2025-01-27T10:15:00Z'\n    }],\n    attachments: [{\n      id: 'att-1',\n      name: 'api-spec.pdf',\n      size: '2.4 MB'\n    }],\n    createdAt: '2025-01-24T13:00:00Z',\n    updatedAt: '2025-01-27T10:15:00Z'\n  }, {\n    id: 'card-4',\n    columnId: 'col-3',\n    title: 'Review dashboard components',\n    description: 'Code review for the new dashboard UI components',\n    priority: 'medium',\n    assignedTo: ['user-1', 'user-4'],\n    dueDate: '2025-01-29',\n    labels: [{\n      id: 'review',\n      name: 'Review',\n      color: '#8b5cf6'\n    }, {\n      id: 'frontend',\n      name: 'Frontend',\n      color: '#06b6d4'\n    }],\n    checklist: [{\n      id: 'check-8',\n      text: 'Check code quality',\n      completed: true\n    }, {\n      id: 'check-9',\n      text: 'Test responsiveness',\n      completed: false\n    }, {\n      id: 'check-10',\n      text: 'Verify accessibility',\n      completed: false\n    }],\n    comments: [],\n    attachments: [],\n    createdAt: '2025-01-23T15:30:00Z',\n    updatedAt: '2025-01-27T09:20:00Z'\n  }, {\n    id: 'card-5',\n    columnId: 'col-4',\n    title: 'Update project documentation',\n    description: 'Refresh README and API documentation with latest changes',\n    priority: 'low',\n    assignedTo: ['user-3'],\n    dueDate: null,\n    labels: [{\n      id: 'documentation',\n      name: 'Documentation',\n      color: '#f59e0b'\n    }],\n    checklist: [{\n      id: 'check-11',\n      text: 'Update README',\n      completed: true\n    }, {\n      id: 'check-12',\n      text: 'Update API docs',\n      completed: true\n    }, {\n      id: 'check-13',\n      text: 'Add deployment guide',\n      completed: true\n    }],\n    comments: [{\n      id: 'comment-4',\n      author: 'user-1',\n      content: 'Great work on the documentation!',\n      createdAt: '2025-01-25T12:00:00Z'\n    }],\n    attachments: [],\n    createdAt: '2025-01-20T10:00:00Z',\n    updatedAt: '2025-01-25T12:00:00Z'\n  }]);\n\n  // Modal states\n  const [showAddCardModal, setShowAddCardModal] = useState(false);\n  const [showAddColumnModal, setShowAddColumnModal] = useState(false);\n  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);\n  const [selectedColumnId, setSelectedColumnId] = useState(null);\n\n  // Filter and search states\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilters, setActiveFilters] = useState({});\n\n  // Load user data and role\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n      }\n    };\n    loadUserData();\n  }, []);\n\n  // Load boards and columns for current project\n  useEffect(() => {\n    const loadBoardsAndColumns = async () => {\n      try {\n        const apiService = (await import('../../utils/realApiService')).default;\n\n        // Get current project ID from localStorage or URL\n        let currentProjectId = localStorage.getItem('currentProjectId');\n\n        // If no current project is set, try to get the first available project\n        if (!currentProjectId) {\n          console.log('No current project selected, trying to get first available project');\n          try {\n            const organizationId = localStorage.getItem('currentOrganizationId');\n            if (organizationId) {\n              const projects = await apiService.projects.getAll(organizationId);\n              if (projects && projects.length > 0) {\n                currentProjectId = projects[0].id;\n                localStorage.setItem('currentProjectId', currentProjectId);\n                console.log('Using first available project:', currentProjectId);\n              }\n            }\n          } catch (projectError) {\n            console.error('Failed to get projects:', projectError);\n          }\n        }\n        if (!currentProjectId) {\n          console.log('No project available, using default columns');\n          return;\n        }\n        console.log('Loading boards for project:', currentProjectId);\n\n        // Get boards for the project\n        const boards = await apiService.boards.getByProject(currentProjectId);\n        console.log('Loaded boards:', boards);\n        if (boards && boards.length > 0) {\n          const board = boards[0]; // Use first board\n\n          // Get columns for the board\n          const columns = await apiService.columns.getByBoard(board.id);\n          console.log('Loaded columns:', columns);\n          if (columns && columns.length > 0) {\n            // Map backend columns to frontend format\n            const mappedColumns = columns.map(col => ({\n              id: col.id,\n              title: col.name || col.title,\n              status: col.status || 'todo',\n              order: col.position || col.order || 1\n            }));\n\n            // Use API columns if they exist\n            setColumns(mappedColumns.sort((a, b) => a.order - b.order));\n            console.log('Using real columns from backend:', mappedColumns);\n            console.log('Column IDs available:', mappedColumns.map(col => col.id));\n          }\n          // If no columns from API, keep the default columns that were set in initial state\n        }\n      } catch (error) {\n        console.error('Failed to load boards and columns:', error);\n        // Keep default columns on error (they're already set in initial state)\n      }\n    };\n    loadBoardsAndColumns();\n  }, []);\n\n  // Role-based and project-based permission checks\n  const canCreateCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canEditCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canDeleteCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canCreateColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canEditColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canDeleteColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n  const canInviteMembers = () => {\n    return ['admin', 'owner'].includes(userRole);\n  };\n  const canDragCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  // Load cards from API when columns are loaded\n  useEffect(() => {\n    const loadCards = async () => {\n      if (columns.length === 0) return;\n      try {\n        const apiService = (await import('../../utils/realApiService')).default;\n        let allCards = [];\n\n        // Load cards for each column\n        for (const column of columns) {\n          try {\n            const result = await apiService.cards.getAll(column.id);\n            console.log(`Loading cards for column ${column.id}:`, result);\n            if (result.data && Array.isArray(result.data)) {\n              // Transform backend data to frontend format\n              const transformedCards = result.data.map(card => ({\n                id: card.id,\n                columnId: card.column_id,\n                title: card.title,\n                description: card.description || '',\n                priority: card.priority || 'medium',\n                assignedTo: card.assigned_to || [],\n                dueDate: card.due_date,\n                labels: card.labels || [],\n                createdAt: card.created_at,\n                updatedAt: card.updated_at,\n                checklist: card.checklist || [],\n                comments: card.comments || [],\n                attachments: card.attachments || []\n              }));\n              allCards = [...allCards, ...transformedCards];\n            }\n          } catch (columnError) {\n            console.error(`Error loading cards for column ${column.id}:`, columnError);\n          }\n        }\n        console.log('All loaded cards:', allCards);\n        setCards(allCards);\n      } catch (error) {\n        console.error('Error loading cards from API:', error);\n        // Keep existing mock data if API fails\n      }\n    };\n    loadCards();\n  }, [columns]);\n\n  // Filter cards based on search and filters\n  const filteredCards = cards.filter(card => {\n    var _activeFilters$priori, _activeFilters$assign, _activeFilters$dueDat;\n    // Search filter\n    if (searchQuery) {\n      var _card$labels;\n      const query = searchQuery.toLowerCase();\n      const matchesSearch = card.title.toLowerCase().includes(query) || card.description.toLowerCase().includes(query) || ((_card$labels = card.labels) === null || _card$labels === void 0 ? void 0 : _card$labels.some(label => label.name.toLowerCase().includes(query)));\n      if (!matchesSearch) return false;\n    }\n\n    // Priority filter\n    if (((_activeFilters$priori = activeFilters.priority) === null || _activeFilters$priori === void 0 ? void 0 : _activeFilters$priori.length) > 0) {\n      if (!activeFilters.priority.includes(card.priority)) return false;\n    }\n\n    // Assignee filter\n    if (((_activeFilters$assign = activeFilters.assignee) === null || _activeFilters$assign === void 0 ? void 0 : _activeFilters$assign.length) > 0) {\n      var _card$assignedTo;\n      const hasAssignee = (_card$assignedTo = card.assignedTo) === null || _card$assignedTo === void 0 ? void 0 : _card$assignedTo.some(assigneeId => activeFilters.assignee.includes(assigneeId));\n      if (!hasAssignee) return false;\n    }\n\n    // Due date filter\n    if (((_activeFilters$dueDat = activeFilters.dueDate) === null || _activeFilters$dueDat === void 0 ? void 0 : _activeFilters$dueDat.length) > 0) {\n      const today = new Date();\n      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;\n      const matchesDueDate = activeFilters.dueDate.some(filter => {\n        if (filter === 'overdue') {\n          return cardDueDate && cardDueDate < today;\n        }\n        if (filter === 'today') {\n          return cardDueDate && cardDueDate.toDateString() === today.toDateString();\n        }\n        if (filter === 'this-week') {\n          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;\n        }\n        if (filter === 'this-month') {\n          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());\n          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;\n        }\n        if (filter === 'custom' && activeFilters.customDateRange) {\n          const startDate = new Date(activeFilters.customDateRange.start);\n          const endDate = new Date(activeFilters.customDateRange.end);\n          return cardDueDate && cardDueDate >= startDate && cardDueDate <= endDate;\n        }\n        return false;\n      });\n      if (!matchesDueDate) return false;\n    }\n    return true;\n  });\n\n  // Handle card movement between columns\n  const handleCardMove = async (cardId, sourceColumnId, targetColumnId) => {\n    // Check if user can drag cards\n    if (!canDragCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot move cards');\n      } else {\n        console.log('You can only move cards in projects you are assigned to');\n      }\n      return;\n    }\n    try {\n      // Update card via API\n      const apiService = (await import('../../utils/realApiService')).default;\n      const updatedCard = {\n        columnId: targetColumnId,\n        updatedAt: new Date().toISOString()\n      };\n      await apiService.cards.update(cardId, updatedCard);\n\n      // Update local state\n      setCards(prevCards => prevCards.map(card => card.id === cardId ? {\n        ...card,\n        columnId: targetColumnId,\n        updatedAt: new Date().toISOString()\n      } : card));\n    } catch (error) {\n      console.error('Failed to move card:', error);\n      // Fallback to local state only\n      setCards(prevCards => prevCards.map(card => card.id === cardId ? {\n        ...card,\n        columnId: targetColumnId,\n        updatedAt: new Date().toISOString()\n      } : card));\n    }\n  };\n\n  // Handle card click - navigate to card details\n  const handleCardClick = card => {\n    console.log('Navigating to card details:', card);\n\n    // Navigate with card data in state and URL params\n    navigate(`/card-details?id=${card.id}`, {\n      state: {\n        card: card,\n        members: members,\n        returnPath: '/kanban-board'\n      }\n    });\n  };\n\n  // Handle adding new card\n  const handleAddCard = columnId => {\n    // Check authentication first\n    if (!isAuthenticated) {\n      console.log('Please log in to create cards');\n      // Optionally redirect to login page\n      navigate('/login');\n      return;\n    }\n    if (!canCreateCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create cards');\n      } else {\n        console.log('You can only create cards in projects you are assigned to');\n      }\n      return;\n    }\n    setSelectedColumnId(columnId);\n    setShowAddCardModal(true);\n  };\n  const handleSaveCard = async newCard => {\n    // Optimistic update - add to local state immediately for instant UI feedback\n    setCards(prevCards => [...prevCards, newCard]);\n    try {\n      // Create card via API\n      const apiService = (await import('../../utils/realApiService')).default;\n      const result = await apiService.cards.create(newCard);\n      console.log('Card created via API:', result);\n\n      // Update with server response if different (e.g., server-generated ID)\n      const createdCard = result.data || newCard;\n      if (createdCard.id !== newCard.id) {\n        setCards(prevCards => prevCards.map(card => card.id === newCard.id ? createdCard : card));\n      }\n\n      // Send notifications for task assignments\n      if (createdCard.assignedTo && createdCard.assignedTo.length > 0) {\n        try {\n          const notificationService = (await import('../../utils/notificationService')).default;\n          for (const assigneeId of createdCard.assignedTo) {\n            if (assigneeId !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id)) {\n              // Don't notify self\n              await notificationService.notifyTaskAssigned(createdCard, assigneeId, currentUser === null || currentUser === void 0 ? void 0 : currentUser.id);\n            }\n          }\n        } catch (notificationError) {\n          console.error('Failed to send task assignment notifications:', notificationError);\n        }\n      }\n      console.log('Card saved:', createdCard);\n    } catch (error) {\n      console.error('Failed to create card:', error);\n      // Card is already in local state from optimistic update\n      // Could add error indicator to the card if needed\n    }\n  };\n\n  // Handle adding new column\n  const handleSaveColumn = newColumn => {\n    if (!canCreateColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create columns');\n      } else {\n        console.log('You can only create columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns => [...prevColumns, newColumn]);\n  };\n\n  // Handle column operations\n  const handleEditColumn = (columnId, updates) => {\n    if (!canEditColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot edit columns');\n      } else {\n        console.log('You can only edit columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns => prevColumns.map(col => col.id === columnId ? {\n      ...col,\n      ...updates,\n      updatedAt: new Date().toISOString()\n    } : col));\n  };\n  const handleDeleteColumn = columnId => {\n    var _columns$;\n    if (!canDeleteColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot delete columns');\n      } else {\n        console.log('You can only delete columns in projects you are assigned to');\n      }\n      return;\n    }\n    // Move cards from deleted column to first column\n    const firstColumnId = (_columns$ = columns[0]) === null || _columns$ === void 0 ? void 0 : _columns$.id;\n    if (firstColumnId && firstColumnId !== columnId) {\n      setCards(prevCards => prevCards.map(card => card.columnId === columnId ? {\n        ...card,\n        columnId: firstColumnId\n      } : card));\n    }\n    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));\n  };\n\n  // Handle member invitation\n  const handleMemberInvite = () => {\n    if (!canInviteMembers()) {\n      console.log('Only admins and owners can invite members');\n      return;\n    }\n    setShowInviteMemberModal(true);\n  };\n  const handleSendInvitation = invitation => {\n    console.log('Invitation sent:', invitation);\n    // In real app, this would send the invitation via API\n  };\n\n  // Handle board updates\n  const handleBoardUpdate = updates => {\n    console.log('Board updated:', updates);\n    // In real app, this would update the board via API\n  };\n\n  // Get cards for a specific column\n  const getCardsForColumn = columnId => {\n    return filteredCards.filter(card => card.columnId === columnId);\n  };\n\n  // Show login prompt if user is not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Kanban\",\n            size: 32,\n            className: \"text-primary-foreground\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-foreground mb-2\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted-foreground mb-6\",\n          children: \"Please log in to access the project management board.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => navigate('/login'),\n          className: \"px-6 py-2\",\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DndProvider, {\n    backend: HTML5Backend,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"pt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full px-4 sm:px-6 lg:px-8 py-8\",\n          children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Kanban\",\n                  size: 20,\n                  className: \"text-primary-foreground\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold text-foreground\",\n                  children: \"Projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted-foreground\",\n                  children: \"Manage tasks and track project progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(BoardHeader, {\n            board: board,\n            members: members,\n            onBoardUpdate: handleBoardUpdate,\n            onMemberInvite: handleMemberInvite,\n            onFilterChange: setActiveFilters,\n            onSearchChange: setSearchQuery,\n            searchQuery: searchQuery,\n            activeFilters: activeFilters,\n            canInviteMembers: canInviteMembers()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-6 overflow-x-auto pb-6\",\n              children: [columns.sort((a, b) => a.order - b.order).map(column => /*#__PURE__*/_jsxDEV(BoardColumn, {\n                column: column,\n                cards: getCardsForColumn(column.id),\n                onCardMove: handleCardMove,\n                onCardClick: handleCardClick,\n                onAddCard: handleAddCard,\n                onEditColumn: handleEditColumn,\n                onDeleteColumn: handleDeleteColumn,\n                members: members,\n                canCreateCards: canCreateCards(),\n                canEditColumns: canEditColumns(),\n                canDeleteColumns: canDeleteColumns(),\n                canDragCards: canDragCards()\n              }, column.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this)), canCreateColumns() && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline\",\n                  onClick: () => setShowAddColumnModal(true),\n                  className: \"w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors\",\n                  iconName: \"Plus\",\n                  iconPosition: \"left\",\n                  children: \"Add Column\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 11\n            }, this), filteredCards.length === 0 && searchQuery && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Search\",\n                size: 48,\n                className: \"text-text-secondary mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-text-primary mb-2\",\n                children: \"No cards found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary text-center max-w-md\",\n                children: \"No cards match your search criteria. Try adjusting your search terms or filters.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                onClick: () => {\n                  setSearchQuery('');\n                  setActiveFilters({});\n                },\n                className: \"mt-4\",\n                children: \"Clear Search & Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(AddCardModal, {\n            isOpen: showAddCardModal,\n            onClose: () => {\n              setShowAddCardModal(false);\n              setSelectedColumnId(null);\n            },\n            onSave: handleSaveCard,\n            columnId: selectedColumnId,\n            members: members\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(AddColumnModal, {\n            isOpen: showAddColumnModal,\n            onClose: () => setShowAddColumnModal(false),\n            onSave: handleSaveColumn\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(InviteMemberModal, {\n            isOpen: showInviteMemberModal,\n            onClose: () => setShowInviteMemberModal(false),\n            onInvite: handleSendInvitation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 678,\n    columnNumber: 5\n  }, this);\n};\n_s(KanbanBoard, \"XF+QRF1TV+hvlXbOzWSYCRMumvo=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = KanbanBoard;\nexport default KanbanBoard;\nvar _c;\n$RefreshReg$(_c, \"KanbanBoard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "DndProvider", "HTML5Backend", "useNavigate", "useAuth", "authService", "RoleBasedHeader", "Breadcrumb", "Icon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "BoardColumn", "AddCardModal", "AddColumnModal", "InviteMemberModal", "jsxDEV", "_jsxDEV", "KanbanBoard", "_s", "navigate", "user", "isAuthenticated", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentProject", "locationState", "window", "location", "state", "projectId", "id", "name", "memberRole", "isUserAssignedToProject", "includes", "board", "title", "description", "isPrivate", "createdAt", "updatedAt", "members", "setMembers", "columns", "setColumns", "status", "order", "cards", "setCards", "columnId", "priority", "assignedTo", "dueDate", "labels", "color", "checklist", "text", "completed", "comments", "author", "content", "attachments", "size", "showAddCardModal", "setShowAddCardModal", "showAddColumnModal", "setShowAddColumnModal", "showInviteMemberModal", "setShowInviteMemberModal", "selectedColumnId", "setSelectedColumnId", "searchQuery", "setSearch<PERSON>uery", "activeFilters", "setActiveFilters", "loadUserData", "userResult", "getCurrentUser", "data", "role", "error", "console", "loadBoardsAndColumns", "apiService", "default", "currentProjectId", "localStorage", "getItem", "log", "organizationId", "projects", "getAll", "length", "setItem", "projectError", "boards", "getByProject", "getByBoard", "mappedColumns", "map", "col", "position", "sort", "a", "b", "canCreateCards", "canEditCards", "canDeleteCards", "canCreateColumns", "canEditColumns", "canDeleteColumns", "canInviteMembers", "canDragCards", "loadCards", "allCards", "column", "result", "Array", "isArray", "transformedCards", "card", "column_id", "assigned_to", "due_date", "created_at", "updated_at", "columnError", "filteredCards", "filter", "_activeFilters$priori", "_activeFilters$assign", "_activeFilters$dueDat", "_card$labels", "query", "toLowerCase", "matchesSearch", "some", "label", "assignee", "_card$assignedTo", "<PERSON>As<PERSON>ee", "assigneeId", "today", "Date", "cardDueDate", "matchesDueDate", "toDateString", "weekFromNow", "getTime", "monthFromNow", "getFullYear", "getMonth", "getDate", "customDateRange", "startDate", "start", "endDate", "end", "handleCardMove", "cardId", "sourceColumnId", "targetColumnId", "updatedCard", "toISOString", "update", "prevCards", "handleCardClick", "returnPath", "handleAddCard", "handleSaveCard", "newCard", "create", "createdCard", "notificationService", "notifyTaskAssigned", "notificationError", "handleSaveColumn", "newColumn", "prevColumns", "handleEditColumn", "updates", "handleDeleteColumn", "_columns$", "firstColumnId", "handleMemberInvite", "handleSendInvitation", "invitation", "handleBoardUpdate", "getCardsForColumn", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "backend", "firstName", "lastName", "email", "avatar", "onBoardUpdate", "onMemberInvite", "onFilterChange", "onSearchChange", "onCardMove", "onCardClick", "onAddCard", "onEditColumn", "onDeleteColumn", "variant", "iconName", "iconPosition", "isOpen", "onClose", "onSave", "onInvite", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/kanban-board/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../utils/authService';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport BoardHeader from './components/BoardHeader';\nimport BoardColumn from './components/BoardColumn';\n\nimport AddCardModal from './components/AddCardModal';\nimport AddColumnModal from './components/AddColumnModal';\nimport InviteMemberModal from './components/InviteMemberModal';\n\nconst KanbanBoard = () => {\n  const navigate = useNavigate();\n  const { user, isAuthenticated } = useAuth();\n\n  // User state and role\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n\n  // Project context - get from location state or default\n  const [currentProject] = useState(() => {\n    const locationState = window.location.state;\n    return locationState?.projectId ? {\n      id: locationState.projectId,\n      name: 'Current Project',\n      memberRole: 'assigned' // This would come from API\n    } : {\n      id: 1,\n      name: 'Website Redesign',\n      memberRole: 'assigned' // assigned, not-assigned\n    };\n  });\n\n  // Check if current user is assigned to this project\n  const isUserAssignedToProject = () => {\n    // For members, check if they're assigned to this specific project\n    if (userRole === 'member') {\n      return currentProject.memberRole === 'assigned';\n    }\n    // Admins and owners have access to all projects\n    return ['admin', 'owner'].includes(userRole);\n  };\n\n  // Mock data\n  const [board] = useState({\n    id: 'board-1',\n    title: 'Project Management Board',\n    description: 'Main project tracking board for Q4 initiatives',\n    isPrivate: false,\n    createdAt: '2025-01-15T10:00:00Z',\n    updatedAt: '2025-01-28T05:54:23Z'\n  });\n\n  // Real members data - will be loaded from team service\n  const [members, setMembers] = useState([]);\n\n  // Initialize with default columns to ensure they're always visible\n  const [columns, setColumns] = useState([\n    { id: 'col-1', title: 'To-Do', status: 'todo', order: 1 },\n    { id: 'col-2', title: 'In Progress', status: 'in-progress', order: 2 },\n    { id: 'col-3', title: 'Review', status: 'review', order: 3 },\n    { id: 'col-4', title: 'Done', status: 'done', order: 4 }\n  ]);\n\n  const [cards, setCards] = useState([\n    {\n      id: 'card-1',\n      columnId: 'col-1',\n      title: 'Design user authentication flow',\n      description: 'Create wireframes and mockups for the login and registration process',\n      priority: 'high',\n      assignedTo: ['user-1', 'user-2'],\n      dueDate: '2025-02-05',\n      labels: [\n        { id: 'design', name: 'Design', color: '#3b82f6' },\n        { id: 'ux', name: 'UX', color: '#8b5cf6' }\n      ],\n      checklist: [\n        { id: 'check-1', text: 'Research competitor flows', completed: true },\n        { id: 'check-2', text: 'Create wireframes', completed: false },\n        { id: 'check-3', text: 'Design mockups', completed: false }\n      ],\n      comments: [\n        {\n          id: 'comment-1',\n          author: 'user-2',\n          content: 'Should we include social login options?',\n          createdAt: '2025-01-27T14:30:00Z'\n        }\n      ],\n      attachments: [],\n      createdAt: '2025-01-25T09:00:00Z',\n      updatedAt: '2025-01-27T14:30:00Z'\n    },\n    {\n      id: 'card-2',\n      columnId: 'col-1',\n      title: 'Set up project repository',\n      description: 'Initialize Git repository with proper folder structure and documentation',\n      priority: 'medium',\n      assignedTo: ['user-3'],\n      dueDate: '2025-01-30',\n      labels: [\n        { id: 'development', name: 'Development', color: '#10b981' }\n      ],\n      checklist: [],\n      comments: [],\n      attachments: [],\n      createdAt: '2025-01-26T11:00:00Z',\n      updatedAt: '2025-01-26T11:00:00Z'\n    },\n    {\n      id: 'card-3',\n      columnId: 'col-2',\n      title: 'Implement user registration API',\n      description: 'Build backend endpoints for user registration with validation and email verification',\n      priority: 'high',\n      assignedTo: ['user-2', 'user-5'],\n      dueDate: '2025-02-10',\n      labels: [\n        { id: 'backend', name: 'Backend', color: '#f59e0b' },\n        { id: 'api', name: 'API', color: '#ef4444' }\n      ],\n      checklist: [\n        { id: 'check-4', text: 'Design database schema', completed: true },\n        { id: 'check-5', text: 'Implement validation', completed: true },\n        { id: 'check-6', text: 'Add email verification', completed: false },\n        { id: 'check-7', text: 'Write unit tests', completed: false }\n      ],\n      comments: [\n        {\n          id: 'comment-2',\n          author: 'user-1',\n          content: 'Make sure to include proper error handling',\n          createdAt: '2025-01-26T16:45:00Z'\n        },\n        {\n          id: 'comment-3',\n          author: 'user-5',\n          content: 'Working on the email service integration',\n          createdAt: '2025-01-27T10:15:00Z'\n        }\n      ],\n      attachments: [\n        { id: 'att-1', name: 'api-spec.pdf', size: '2.4 MB' }\n      ],\n      createdAt: '2025-01-24T13:00:00Z',\n      updatedAt: '2025-01-27T10:15:00Z'\n    },\n    {\n      id: 'card-4',\n      columnId: 'col-3',\n      title: 'Review dashboard components',\n      description: 'Code review for the new dashboard UI components',\n      priority: 'medium',\n      assignedTo: ['user-1', 'user-4'],\n      dueDate: '2025-01-29',\n      labels: [\n        { id: 'review', name: 'Review', color: '#8b5cf6' },\n        { id: 'frontend', name: 'Frontend', color: '#06b6d4' }\n      ],\n      checklist: [\n        { id: 'check-8', text: 'Check code quality', completed: true },\n        { id: 'check-9', text: 'Test responsiveness', completed: false },\n        { id: 'check-10', text: 'Verify accessibility', completed: false }\n      ],\n      comments: [],\n      attachments: [],\n      createdAt: '2025-01-23T15:30:00Z',\n      updatedAt: '2025-01-27T09:20:00Z'\n    },\n    {\n      id: 'card-5',\n      columnId: 'col-4',\n      title: 'Update project documentation',\n      description: 'Refresh README and API documentation with latest changes',\n      priority: 'low',\n      assignedTo: ['user-3'],\n      dueDate: null,\n      labels: [\n        { id: 'documentation', name: 'Documentation', color: '#f59e0b' }\n      ],\n      checklist: [\n        { id: 'check-11', text: 'Update README', completed: true },\n        { id: 'check-12', text: 'Update API docs', completed: true },\n        { id: 'check-13', text: 'Add deployment guide', completed: true }\n      ],\n      comments: [\n        {\n          id: 'comment-4',\n          author: 'user-1',\n          content: 'Great work on the documentation!',\n          createdAt: '2025-01-25T12:00:00Z'\n        }\n      ],\n      attachments: [],\n      createdAt: '2025-01-20T10:00:00Z',\n      updatedAt: '2025-01-25T12:00:00Z'\n    }\n  ]);\n\n  // Modal states\n  const [showAddCardModal, setShowAddCardModal] = useState(false);\n  const [showAddColumnModal, setShowAddColumnModal] = useState(false);\n  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);\n  const [selectedColumnId, setSelectedColumnId] = useState(null);\n\n  // Filter and search states\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilters, setActiveFilters] = useState({});\n\n  // Load user data and role\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  // Load boards and columns for current project\n  useEffect(() => {\n    const loadBoardsAndColumns = async () => {\n      try {\n        const apiService = (await import('../../utils/realApiService')).default;\n\n        // Get current project ID from localStorage or URL\n        let currentProjectId = localStorage.getItem('currentProjectId');\n\n        // If no current project is set, try to get the first available project\n        if (!currentProjectId) {\n          console.log('No current project selected, trying to get first available project');\n          try {\n            const organizationId = localStorage.getItem('currentOrganizationId');\n            if (organizationId) {\n              const projects = await apiService.projects.getAll(organizationId);\n              if (projects && projects.length > 0) {\n                currentProjectId = projects[0].id;\n                localStorage.setItem('currentProjectId', currentProjectId);\n                console.log('Using first available project:', currentProjectId);\n              }\n            }\n          } catch (projectError) {\n            console.error('Failed to get projects:', projectError);\n          }\n        }\n\n        if (!currentProjectId) {\n          console.log('No project available, using default columns');\n          return;\n        }\n\n        console.log('Loading boards for project:', currentProjectId);\n\n        // Get boards for the project\n        const boards = await apiService.boards.getByProject(currentProjectId);\n        console.log('Loaded boards:', boards);\n\n        if (boards && boards.length > 0) {\n          const board = boards[0]; // Use first board\n\n          // Get columns for the board\n          const columns = await apiService.columns.getByBoard(board.id);\n          console.log('Loaded columns:', columns);\n\n          if (columns && columns.length > 0) {\n            // Map backend columns to frontend format\n            const mappedColumns = columns.map(col => ({\n              id: col.id,\n              title: col.name || col.title,\n              status: col.status || 'todo',\n              order: col.position || col.order || 1\n            }));\n\n            // Use API columns if they exist\n            setColumns(mappedColumns.sort((a, b) => a.order - b.order));\n            console.log('Using real columns from backend:', mappedColumns);\n            console.log('Column IDs available:', mappedColumns.map(col => col.id));\n          }\n          // If no columns from API, keep the default columns that were set in initial state\n        }\n      } catch (error) {\n        console.error('Failed to load boards and columns:', error);\n        // Keep default columns on error (they're already set in initial state)\n      }\n    };\n\n    loadBoardsAndColumns();\n  }, []);\n\n  // Role-based and project-based permission checks\n  const canCreateCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canEditCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canDeleteCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canCreateColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canEditColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canDeleteColumns = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  const canInviteMembers = () => {\n    return ['admin', 'owner'].includes(userRole);\n  };\n\n  const canDragCards = () => {\n    if (userRole === 'viewer') return false;\n    return isUserAssignedToProject();\n  };\n\n  // Load cards from API when columns are loaded\n  useEffect(() => {\n    const loadCards = async () => {\n      if (columns.length === 0) return;\n\n      try {\n        const apiService = (await import('../../utils/realApiService')).default;\n        let allCards = [];\n\n        // Load cards for each column\n        for (const column of columns) {\n          try {\n            const result = await apiService.cards.getAll(column.id);\n            console.log(`Loading cards for column ${column.id}:`, result);\n            if (result.data && Array.isArray(result.data)) {\n              // Transform backend data to frontend format\n              const transformedCards = result.data.map(card => ({\n                id: card.id,\n                columnId: card.column_id,\n                title: card.title,\n                description: card.description || '',\n                priority: card.priority || 'medium',\n                assignedTo: card.assigned_to || [],\n                dueDate: card.due_date,\n                labels: card.labels || [],\n                createdAt: card.created_at,\n                updatedAt: card.updated_at,\n                checklist: card.checklist || [],\n                comments: card.comments || [],\n                attachments: card.attachments || []\n              }));\n              allCards = [...allCards, ...transformedCards];\n            }\n          } catch (columnError) {\n            console.error(`Error loading cards for column ${column.id}:`, columnError);\n          }\n        }\n\n        console.log('All loaded cards:', allCards);\n        setCards(allCards);\n      } catch (error) {\n        console.error('Error loading cards from API:', error);\n        // Keep existing mock data if API fails\n      }\n    };\n\n    loadCards();\n  }, [columns]);\n\n  // Filter cards based on search and filters\n  const filteredCards = cards.filter(card => {\n    // Search filter\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      const matchesSearch = \n        card.title.toLowerCase().includes(query) ||\n        card.description.toLowerCase().includes(query) ||\n        card.labels?.some(label => label.name.toLowerCase().includes(query));\n      \n      if (!matchesSearch) return false;\n    }\n\n    // Priority filter\n    if (activeFilters.priority?.length > 0) {\n      if (!activeFilters.priority.includes(card.priority)) return false;\n    }\n\n    // Assignee filter\n    if (activeFilters.assignee?.length > 0) {\n      const hasAssignee = card.assignedTo?.some(assigneeId => \n        activeFilters.assignee.includes(assigneeId)\n      );\n      if (!hasAssignee) return false;\n    }\n\n    // Due date filter\n    if (activeFilters.dueDate?.length > 0) {\n      const today = new Date();\n      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;\n\n      const matchesDueDate = activeFilters.dueDate.some(filter => {\n        if (filter === 'overdue') {\n          return cardDueDate && cardDueDate < today;\n        }\n        if (filter === 'today') {\n          return cardDueDate && cardDueDate.toDateString() === today.toDateString();\n        }\n        if (filter === 'this-week') {\n          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;\n        }\n        if (filter === 'this-month') {\n          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());\n          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;\n        }\n        if (filter === 'custom' && activeFilters.customDateRange) {\n          const startDate = new Date(activeFilters.customDateRange.start);\n          const endDate = new Date(activeFilters.customDateRange.end);\n          return cardDueDate && cardDueDate >= startDate && cardDueDate <= endDate;\n        }\n        return false;\n      });\n\n      if (!matchesDueDate) return false;\n    }\n\n    return true;\n  });\n\n  // Handle card movement between columns\n  const handleCardMove = async (cardId, sourceColumnId, targetColumnId) => {\n    // Check if user can drag cards\n    if (!canDragCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot move cards');\n      } else {\n        console.log('You can only move cards in projects you are assigned to');\n      }\n      return;\n    }\n\n    try {\n      // Update card via API\n      const apiService = (await import('../../utils/realApiService')).default;\n      const updatedCard = { columnId: targetColumnId, updatedAt: new Date().toISOString() };\n      await apiService.cards.update(cardId, updatedCard);\n\n      // Update local state\n      setCards(prevCards =>\n        prevCards.map(card =>\n          card.id === cardId\n            ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }\n            : card\n        )\n      );\n    } catch (error) {\n      console.error('Failed to move card:', error);\n      // Fallback to local state only\n      setCards(prevCards =>\n        prevCards.map(card =>\n          card.id === cardId\n            ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }\n            : card\n        )\n      );\n    }\n  };\n\n  // Handle card click - navigate to card details\n  const handleCardClick = (card) => {\n    console.log('Navigating to card details:', card);\n\n    // Navigate with card data in state and URL params\n    navigate(`/card-details?id=${card.id}`, {\n      state: {\n        card: card,\n        members: members,\n        returnPath: '/kanban-board'\n      }\n    });\n  };\n\n  // Handle adding new card\n  const handleAddCard = (columnId) => {\n    // Check authentication first\n    if (!isAuthenticated) {\n      console.log('Please log in to create cards');\n      // Optionally redirect to login page\n      navigate('/login');\n      return;\n    }\n\n    if (!canCreateCards()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create cards');\n      } else {\n        console.log('You can only create cards in projects you are assigned to');\n      }\n      return;\n    }\n    setSelectedColumnId(columnId);\n    setShowAddCardModal(true);\n  };\n\n  const handleSaveCard = async (newCard) => {\n    // Optimistic update - add to local state immediately for instant UI feedback\n    setCards(prevCards => [...prevCards, newCard]);\n\n    try {\n      // Create card via API\n      const apiService = (await import('../../utils/realApiService')).default;\n      const result = await apiService.cards.create(newCard);\n      console.log('Card created via API:', result);\n\n      // Update with server response if different (e.g., server-generated ID)\n      const createdCard = result.data || newCard;\n      if (createdCard.id !== newCard.id) {\n        setCards(prevCards =>\n          prevCards.map(card =>\n            card.id === newCard.id ? createdCard : card\n          )\n        );\n      }\n\n      // Send notifications for task assignments\n      if (createdCard.assignedTo && createdCard.assignedTo.length > 0) {\n        try {\n          const notificationService = (await import('../../utils/notificationService')).default;\n\n          for (const assigneeId of createdCard.assignedTo) {\n            if (assigneeId !== currentUser?.id) { // Don't notify self\n              await notificationService.notifyTaskAssigned(\n                createdCard,\n                assigneeId,\n                currentUser?.id\n              );\n            }\n          }\n        } catch (notificationError) {\n          console.error('Failed to send task assignment notifications:', notificationError);\n        }\n      }\n\n      console.log('Card saved:', createdCard);\n    } catch (error) {\n      console.error('Failed to create card:', error);\n      // Card is already in local state from optimistic update\n      // Could add error indicator to the card if needed\n    }\n  };\n\n  // Handle adding new column\n  const handleSaveColumn = (newColumn) => {\n    if (!canCreateColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot create columns');\n      } else {\n        console.log('You can only create columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns => [...prevColumns, newColumn]);\n  };\n\n  // Handle column operations\n  const handleEditColumn = (columnId, updates) => {\n    if (!canEditColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot edit columns');\n      } else {\n        console.log('You can only edit columns in projects you are assigned to');\n      }\n      return;\n    }\n    setColumns(prevColumns =>\n      prevColumns.map(col =>\n        col.id === columnId\n          ? { ...col, ...updates, updatedAt: new Date().toISOString() }\n          : col\n      )\n    );\n  };\n\n  const handleDeleteColumn = (columnId) => {\n    if (!canDeleteColumns()) {\n      if (userRole === 'viewer') {\n        console.log('Viewers cannot delete columns');\n      } else {\n        console.log('You can only delete columns in projects you are assigned to');\n      }\n      return;\n    }\n    // Move cards from deleted column to first column\n    const firstColumnId = columns[0]?.id;\n    if (firstColumnId && firstColumnId !== columnId) {\n      setCards(prevCards =>\n        prevCards.map(card =>\n          card.columnId === columnId\n            ? { ...card, columnId: firstColumnId }\n            : card\n        )\n      );\n    }\n    \n    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));\n  };\n\n  // Handle member invitation\n  const handleMemberInvite = () => {\n    if (!canInviteMembers()) {\n      console.log('Only admins and owners can invite members');\n      return;\n    }\n    setShowInviteMemberModal(true);\n  };\n\n  const handleSendInvitation = (invitation) => {\n    console.log('Invitation sent:', invitation);\n    // In real app, this would send the invitation via API\n  };\n\n  // Handle board updates\n  const handleBoardUpdate = (updates) => {\n    console.log('Board updated:', updates);\n    // In real app, this would update the board via API\n  };\n\n  // Get cards for a specific column\n  const getCardsForColumn = (columnId) => {\n    return filteredCards.filter(card => card.columnId === columnId);\n  };\n\n  // Show login prompt if user is not authenticated\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4\">\n            <Icon name=\"Kanban\" size={32} className=\"text-primary-foreground\" />\n          </div>\n          <h1 className=\"text-2xl font-bold text-foreground mb-2\">Authentication Required</h1>\n          <p className=\"text-muted-foreground mb-6\">\n            Please log in to access the project management board.\n          </p>\n          <Button onClick={() => navigate('/login')} className=\"px-6 py-2\">\n            Go to Login\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <DndProvider backend={HTML5Backend}>\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n        />\n\n        <main className=\"pt-16\">\n          <div className=\"max-w-full px-4 sm:px-6 lg:px-8 py-8\">\n            <Breadcrumb />\n\n            {/* Page Header */}\n            <div className=\"mb-6\">\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\">\n                  <Icon name=\"Kanban\" size={20} className=\"text-primary-foreground\" />\n                </div>\n                <div>\n                  <h1 className=\"text-3xl font-bold text-foreground\">Projects</h1>\n                  <p className=\"text-muted-foreground\">\n                    Manage tasks and track project progress\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Board Header */}\n            <BoardHeader\n              board={board}\n              members={members}\n              onBoardUpdate={handleBoardUpdate}\n              onMemberInvite={handleMemberInvite}\n              onFilterChange={setActiveFilters}\n              onSearchChange={setSearchQuery}\n              searchQuery={searchQuery}\n              activeFilters={activeFilters}\n              canInviteMembers={canInviteMembers()}\n            />\n\n        {/* Board Content */}\n        <div className=\"flex-1 p-6\">\n          <div className=\"flex space-x-6 overflow-x-auto pb-6\">\n            {/* Columns */}\n            {columns\n              .sort((a, b) => a.order - b.order)\n              .map(column => (\n                <BoardColumn\n                  key={column.id}\n                  column={column}\n                  cards={getCardsForColumn(column.id)}\n                  onCardMove={handleCardMove}\n                  onCardClick={handleCardClick}\n                  onAddCard={handleAddCard}\n                  onEditColumn={handleEditColumn}\n                  onDeleteColumn={handleDeleteColumn}\n                  members={members}\n                  canCreateCards={canCreateCards()}\n                  canEditColumns={canEditColumns()}\n                  canDeleteColumns={canDeleteColumns()}\n                  canDragCards={canDragCards()}\n                />\n              ))}\n\n            {/* Add Column Button - Only show for non-viewers */}\n            {canCreateColumns() && (\n              <div className=\"flex-shrink-0\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowAddColumnModal(true)}\n                  className=\"w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors\"\n                  iconName=\"Plus\"\n                  iconPosition=\"left\"\n                >\n                  Add Column\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Empty State */}\n          {filteredCards.length === 0 && searchQuery && (\n            <div className=\"flex flex-col items-center justify-center py-12\">\n              <Icon name=\"Search\" size={48} className=\"text-text-secondary mb-4\" />\n              <h3 className=\"text-lg font-medium text-text-primary mb-2\">No cards found</h3>\n              <p className=\"text-text-secondary text-center max-w-md\">\n                No cards match your search criteria. Try adjusting your search terms or filters.\n              </p>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSearchQuery('');\n                  setActiveFilters({});\n                }}\n                className=\"mt-4\"\n              >\n                Clear Search & Filters\n              </Button>\n            </div>\n          )}\n        </div>\n\n        {/* Modals */}\n        <AddCardModal\n          isOpen={showAddCardModal}\n          onClose={() => {\n            setShowAddCardModal(false);\n            setSelectedColumnId(null);\n          }}\n          onSave={handleSaveCard}\n          columnId={selectedColumnId}\n          members={members}\n        />\n\n        <AddColumnModal\n          isOpen={showAddColumnModal}\n          onClose={() => setShowAddColumnModal(false)}\n          onSave={handleSaveColumn}\n        />\n\n        <InviteMemberModal\n          isOpen={showInviteMemberModal}\n          onClose={() => setShowInviteMemberModal(false)}\n          onInvite={handleSendInvitation}\n        />\n          </div>\n        </main>\n      </div>\n    </DndProvider>\n  );\n};\n\nexport default KanbanBoard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAElD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,IAAI;IAAEC;EAAgB,CAAC,GAAGjB,OAAO,CAAC,CAAC;;EAE3C;EACA,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM,CAAC2B,cAAc,CAAC,GAAG3B,QAAQ,CAAC,MAAM;IACtC,MAAM4B,aAAa,GAAGC,MAAM,CAACC,QAAQ,CAACC,KAAK;IAC3C,OAAOH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEI,SAAS,GAAG;MAChCC,EAAE,EAAEL,aAAa,CAACI,SAAS;MAC3BE,IAAI,EAAE,iBAAiB;MACvBC,UAAU,EAAE,UAAU,CAAC;IACzB,CAAC,GAAG;MACFF,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kBAAkB;MACxBC,UAAU,EAAE,UAAU,CAAC;IACzB,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACA,IAAIX,QAAQ,KAAK,QAAQ,EAAE;MACzB,OAAOE,cAAc,CAACQ,UAAU,KAAK,UAAU;IACjD;IACA;IACA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAACE,QAAQ,CAACZ,QAAQ,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM,CAACa,KAAK,CAAC,GAAGtC,QAAQ,CAAC;IACvBiC,EAAE,EAAE,SAAS;IACbM,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,gDAAgD;IAC7DC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,CACrC;IAAEiC,EAAE,EAAE,OAAO;IAAEM,KAAK,EAAE,OAAO;IAAES,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAE,CAAC,EACzD;IAAEhB,EAAE,EAAE,OAAO;IAAEM,KAAK,EAAE,aAAa;IAAES,MAAM,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAE,CAAC,EACtE;IAAEhB,EAAE,EAAE,OAAO;IAAEM,KAAK,EAAE,QAAQ;IAAES,MAAM,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAE,CAAC,EAC5D;IAAEhB,EAAE,EAAE,OAAO;IAAEM,KAAK,EAAE,MAAM;IAAES,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAE,CAAC,CACzD,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,CACjC;IACEiC,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,iCAAiC;IACxCC,WAAW,EAAE,sEAAsE;IACnFa,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,QAAQ;MAAEuB,KAAK,EAAE;IAAU,CAAC,EAClD;MAAExB,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEuB,KAAK,EAAE;IAAU,CAAC,CAC3C;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EACrE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,mBAAmB;MAAEC,SAAS,EAAE;IAAM,CAAC,EAC9D;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,gBAAgB;MAAEC,SAAS,EAAE;IAAM,CAAC,CAC5D;IACDC,QAAQ,EAAE,CACR;MACE5B,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,yCAAyC;MAClDrB,SAAS,EAAE;IACb,CAAC,CACF;IACDsB,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,0EAA0E;IACvFa,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtBC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,aAAa;MAAEC,IAAI,EAAE,aAAa;MAAEuB,KAAK,EAAE;IAAU,CAAC,CAC7D;IACDC,SAAS,EAAE,EAAE;IACbG,QAAQ,EAAE,EAAE;IACZG,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,iCAAiC;IACxCC,WAAW,EAAE,sFAAsF;IACnGa,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEuB,KAAK,EAAE;IAAU,CAAC,EACpD;MAAExB,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEuB,KAAK,EAAE;IAAU,CAAC,CAC7C;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,wBAAwB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAClE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAChE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,wBAAwB;MAAEC,SAAS,EAAE;IAAM,CAAC,EACnE;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,kBAAkB;MAAEC,SAAS,EAAE;IAAM,CAAC,CAC9D;IACDC,QAAQ,EAAE,CACR;MACE5B,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,4CAA4C;MACrDrB,SAAS,EAAE;IACb,CAAC,EACD;MACET,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,0CAA0C;MACnDrB,SAAS,EAAE;IACb,CAAC,CACF;IACDsB,WAAW,EAAE,CACX;MAAE/B,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,cAAc;MAAE+B,IAAI,EAAE;IAAS,CAAC,CACtD;IACDvB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,iDAAiD;IAC9Da,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,QAAQ;MAAEuB,KAAK,EAAE;IAAU,CAAC,EAClD;MAAExB,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,UAAU;MAAEuB,KAAK,EAAE;IAAU,CAAC,CACvD;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,oBAAoB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC9D;MAAE3B,EAAE,EAAE,SAAS;MAAE0B,IAAI,EAAE,qBAAqB;MAAEC,SAAS,EAAE;IAAM,CAAC,EAChE;MAAE3B,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAM,CAAC,CACnE;IACDC,QAAQ,EAAE,EAAE;IACZG,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZmB,QAAQ,EAAE,OAAO;IACjBb,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,0DAA0D;IACvEa,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtBC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,CACN;MAAEvB,EAAE,EAAE,eAAe;MAAEC,IAAI,EAAE,eAAe;MAAEuB,KAAK,EAAE;IAAU,CAAC,CACjE;IACDC,SAAS,EAAE,CACT;MAAEzB,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC1D;MAAE3B,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,iBAAiB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC5D;MAAE3B,EAAE,EAAE,UAAU;MAAE0B,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAK,CAAC,CAClE;IACDC,QAAQ,EAAE,CACR;MACE5B,EAAE,EAAE,WAAW;MACf6B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,kCAAkC;MAC3CrB,SAAS,EAAE;IACb,CAAC,CACF;IACDsB,WAAW,EAAE,EAAE;IACftB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6E,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,UAAU,GAAG,MAAMzE,WAAW,CAAC0E,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAAC5D,IAAI,EAAE;UACxBG,cAAc,CAACuD,UAAU,CAACE,IAAI,CAAC5D,IAAI,CAAC;UACpCK,WAAW,CAACqD,UAAU,CAACE,IAAI,CAAC5D,IAAI,CAAC6D,IAAI,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAEDL,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7E,SAAS,CAAC,MAAM;IACd,MAAMoF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF,MAAMC,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;;QAEvE;QACA,IAAIC,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;;QAE/D;QACA,IAAI,CAACF,gBAAgB,EAAE;UACrBJ,OAAO,CAACO,GAAG,CAAC,oEAAoE,CAAC;UACjF,IAAI;YACF,MAAMC,cAAc,GAAGH,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;YACpE,IAAIE,cAAc,EAAE;cAClB,MAAMC,QAAQ,GAAG,MAAMP,UAAU,CAACO,QAAQ,CAACC,MAAM,CAACF,cAAc,CAAC;cACjE,IAAIC,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;gBACnCP,gBAAgB,GAAGK,QAAQ,CAAC,CAAC,CAAC,CAAC5D,EAAE;gBACjCwD,YAAY,CAACO,OAAO,CAAC,kBAAkB,EAAER,gBAAgB,CAAC;gBAC1DJ,OAAO,CAACO,GAAG,CAAC,gCAAgC,EAAEH,gBAAgB,CAAC;cACjE;YACF;UACF,CAAC,CAAC,OAAOS,YAAY,EAAE;YACrBb,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEc,YAAY,CAAC;UACxD;QACF;QAEA,IAAI,CAACT,gBAAgB,EAAE;UACrBJ,OAAO,CAACO,GAAG,CAAC,6CAA6C,CAAC;UAC1D;QACF;QAEAP,OAAO,CAACO,GAAG,CAAC,6BAA6B,EAAEH,gBAAgB,CAAC;;QAE5D;QACA,MAAMU,MAAM,GAAG,MAAMZ,UAAU,CAACY,MAAM,CAACC,YAAY,CAACX,gBAAgB,CAAC;QACrEJ,OAAO,CAACO,GAAG,CAAC,gBAAgB,EAAEO,MAAM,CAAC;QAErC,IAAIA,MAAM,IAAIA,MAAM,CAACH,MAAM,GAAG,CAAC,EAAE;UAC/B,MAAMzD,KAAK,GAAG4D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEzB;UACA,MAAMpD,OAAO,GAAG,MAAMwC,UAAU,CAACxC,OAAO,CAACsD,UAAU,CAAC9D,KAAK,CAACL,EAAE,CAAC;UAC7DmD,OAAO,CAACO,GAAG,CAAC,iBAAiB,EAAE7C,OAAO,CAAC;UAEvC,IAAIA,OAAO,IAAIA,OAAO,CAACiD,MAAM,GAAG,CAAC,EAAE;YACjC;YACA,MAAMM,aAAa,GAAGvD,OAAO,CAACwD,GAAG,CAACC,GAAG,KAAK;cACxCtE,EAAE,EAAEsE,GAAG,CAACtE,EAAE;cACVM,KAAK,EAAEgE,GAAG,CAACrE,IAAI,IAAIqE,GAAG,CAAChE,KAAK;cAC5BS,MAAM,EAAEuD,GAAG,CAACvD,MAAM,IAAI,MAAM;cAC5BC,KAAK,EAAEsD,GAAG,CAACC,QAAQ,IAAID,GAAG,CAACtD,KAAK,IAAI;YACtC,CAAC,CAAC,CAAC;;YAEH;YACAF,UAAU,CAACsD,aAAa,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC,CAAC;YAC3DmC,OAAO,CAACO,GAAG,CAAC,kCAAkC,EAAEU,aAAa,CAAC;YAC9DjB,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAEU,aAAa,CAACC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACtE,EAAE,CAAC,CAAC;UACxE;UACA;QACF;MACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D;MACF;IACF,CAAC;IAEDE,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAInF,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMyE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIpF,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAM0E,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrF,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAM2E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAItF,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAM4E,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvF,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAM6E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIxF,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAED,MAAM8E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC7E,QAAQ,CAACZ,QAAQ,CAAC;EAC9C,CAAC;EAED,MAAM0F,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1F,QAAQ,KAAK,QAAQ,EAAE,OAAO,KAAK;IACvC,OAAOW,uBAAuB,CAAC,CAAC;EAClC,CAAC;;EAED;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMmH,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAItE,OAAO,CAACiD,MAAM,KAAK,CAAC,EAAE;MAE1B,IAAI;QACF,MAAMT,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;QACvE,IAAI8B,QAAQ,GAAG,EAAE;;QAEjB;QACA,KAAK,MAAMC,MAAM,IAAIxE,OAAO,EAAE;UAC5B,IAAI;YACF,MAAMyE,MAAM,GAAG,MAAMjC,UAAU,CAACpC,KAAK,CAAC4C,MAAM,CAACwB,MAAM,CAACrF,EAAE,CAAC;YACvDmD,OAAO,CAACO,GAAG,CAAC,4BAA4B2B,MAAM,CAACrF,EAAE,GAAG,EAAEsF,MAAM,CAAC;YAC7D,IAAIA,MAAM,CAACtC,IAAI,IAAIuC,KAAK,CAACC,OAAO,CAACF,MAAM,CAACtC,IAAI,CAAC,EAAE;cAC7C;cACA,MAAMyC,gBAAgB,GAAGH,MAAM,CAACtC,IAAI,CAACqB,GAAG,CAACqB,IAAI,KAAK;gBAChD1F,EAAE,EAAE0F,IAAI,CAAC1F,EAAE;gBACXmB,QAAQ,EAAEuE,IAAI,CAACC,SAAS;gBACxBrF,KAAK,EAAEoF,IAAI,CAACpF,KAAK;gBACjBC,WAAW,EAAEmF,IAAI,CAACnF,WAAW,IAAI,EAAE;gBACnCa,QAAQ,EAAEsE,IAAI,CAACtE,QAAQ,IAAI,QAAQ;gBACnCC,UAAU,EAAEqE,IAAI,CAACE,WAAW,IAAI,EAAE;gBAClCtE,OAAO,EAAEoE,IAAI,CAACG,QAAQ;gBACtBtE,MAAM,EAAEmE,IAAI,CAACnE,MAAM,IAAI,EAAE;gBACzBd,SAAS,EAAEiF,IAAI,CAACI,UAAU;gBAC1BpF,SAAS,EAAEgF,IAAI,CAACK,UAAU;gBAC1BtE,SAAS,EAAEiE,IAAI,CAACjE,SAAS,IAAI,EAAE;gBAC/BG,QAAQ,EAAE8D,IAAI,CAAC9D,QAAQ,IAAI,EAAE;gBAC7BG,WAAW,EAAE2D,IAAI,CAAC3D,WAAW,IAAI;cACnC,CAAC,CAAC,CAAC;cACHqD,QAAQ,GAAG,CAAC,GAAGA,QAAQ,EAAE,GAAGK,gBAAgB,CAAC;YAC/C;UACF,CAAC,CAAC,OAAOO,WAAW,EAAE;YACpB7C,OAAO,CAACD,KAAK,CAAC,kCAAkCmC,MAAM,CAACrF,EAAE,GAAG,EAAEgG,WAAW,CAAC;UAC5E;QACF;QAEA7C,OAAO,CAACO,GAAG,CAAC,mBAAmB,EAAE0B,QAAQ,CAAC;QAC1ClE,QAAQ,CAACkE,QAAQ,CAAC;MACpB,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;MACF;IACF,CAAC;IAEDiC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACtE,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMoF,aAAa,GAAGhF,KAAK,CAACiF,MAAM,CAACR,IAAI,IAAI;IAAA,IAAAS,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACzC;IACA,IAAI5D,WAAW,EAAE;MAAA,IAAA6D,YAAA;MACf,MAAMC,KAAK,GAAG9D,WAAW,CAAC+D,WAAW,CAAC,CAAC;MACvC,MAAMC,aAAa,GACjBf,IAAI,CAACpF,KAAK,CAACkG,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAACmG,KAAK,CAAC,IACxCb,IAAI,CAACnF,WAAW,CAACiG,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAACmG,KAAK,CAAC,MAAAD,YAAA,GAC9CZ,IAAI,CAACnE,MAAM,cAAA+E,YAAA,uBAAXA,YAAA,CAAaI,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC1G,IAAI,CAACuG,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAACmG,KAAK,CAAC,CAAC;MAEtE,IAAI,CAACE,aAAa,EAAE,OAAO,KAAK;IAClC;;IAEA;IACA,IAAI,EAAAN,qBAAA,GAAAxD,aAAa,CAACvB,QAAQ,cAAA+E,qBAAA,uBAAtBA,qBAAA,CAAwBrC,MAAM,IAAG,CAAC,EAAE;MACtC,IAAI,CAACnB,aAAa,CAACvB,QAAQ,CAAChB,QAAQ,CAACsF,IAAI,CAACtE,QAAQ,CAAC,EAAE,OAAO,KAAK;IACnE;;IAEA;IACA,IAAI,EAAAgF,qBAAA,GAAAzD,aAAa,CAACiE,QAAQ,cAAAR,qBAAA,uBAAtBA,qBAAA,CAAwBtC,MAAM,IAAG,CAAC,EAAE;MAAA,IAAA+C,gBAAA;MACtC,MAAMC,WAAW,IAAAD,gBAAA,GAAGnB,IAAI,CAACrE,UAAU,cAAAwF,gBAAA,uBAAfA,gBAAA,CAAiBH,IAAI,CAACK,UAAU,IAClDpE,aAAa,CAACiE,QAAQ,CAACxG,QAAQ,CAAC2G,UAAU,CAC5C,CAAC;MACD,IAAI,CAACD,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAI,EAAAT,qBAAA,GAAA1D,aAAa,CAACrB,OAAO,cAAA+E,qBAAA,uBAArBA,qBAAA,CAAuBvC,MAAM,IAAG,CAAC,EAAE;MACrC,MAAMkD,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,WAAW,GAAGxB,IAAI,CAACpE,OAAO,GAAG,IAAI2F,IAAI,CAACvB,IAAI,CAACpE,OAAO,CAAC,GAAG,IAAI;MAEhE,MAAM6F,cAAc,GAAGxE,aAAa,CAACrB,OAAO,CAACoF,IAAI,CAACR,MAAM,IAAI;QAC1D,IAAIA,MAAM,KAAK,SAAS,EAAE;UACxB,OAAOgB,WAAW,IAAIA,WAAW,GAAGF,KAAK;QAC3C;QACA,IAAId,MAAM,KAAK,OAAO,EAAE;UACtB,OAAOgB,WAAW,IAAIA,WAAW,CAACE,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC;QAC3E;QACA,IAAIlB,MAAM,KAAK,WAAW,EAAE;UAC1B,MAAMmB,WAAW,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAACM,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;UACvE,OAAOJ,WAAW,IAAIA,WAAW,IAAIF,KAAK,IAAIE,WAAW,IAAIG,WAAW;QAC1E;QACA,IAAInB,MAAM,KAAK,YAAY,EAAE;UAC3B,MAAMqB,YAAY,GAAG,IAAIN,IAAI,CAACD,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAET,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC;UACzF,OAAOR,WAAW,IAAIA,WAAW,IAAIF,KAAK,IAAIE,WAAW,IAAIK,YAAY;QAC3E;QACA,IAAIrB,MAAM,KAAK,QAAQ,IAAIvD,aAAa,CAACgF,eAAe,EAAE;UACxD,MAAMC,SAAS,GAAG,IAAIX,IAAI,CAACtE,aAAa,CAACgF,eAAe,CAACE,KAAK,CAAC;UAC/D,MAAMC,OAAO,GAAG,IAAIb,IAAI,CAACtE,aAAa,CAACgF,eAAe,CAACI,GAAG,CAAC;UAC3D,OAAOb,WAAW,IAAIA,WAAW,IAAIU,SAAS,IAAIV,WAAW,IAAIY,OAAO;QAC1E;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MAEF,IAAI,CAACX,cAAc,EAAE,OAAO,KAAK;IACnC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMa,cAAc,GAAG,MAAAA,CAAOC,MAAM,EAAEC,cAAc,EAAEC,cAAc,KAAK;IACvE;IACA,IAAI,CAACjD,YAAY,CAAC,CAAC,EAAE;MACnB,IAAI1F,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACO,GAAG,CAAC,2BAA2B,CAAC;MAC1C,CAAC,MAAM;QACLP,OAAO,CAACO,GAAG,CAAC,yDAAyD,CAAC;MACxE;MACA;IACF;IAEA,IAAI;MACF;MACA,MAAML,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;MACvE,MAAM8E,WAAW,GAAG;QAAEjH,QAAQ,EAAEgH,cAAc;QAAEzH,SAAS,EAAE,IAAIuG,IAAI,CAAC,CAAC,CAACoB,WAAW,CAAC;MAAE,CAAC;MACrF,MAAMhF,UAAU,CAACpC,KAAK,CAACqH,MAAM,CAACL,MAAM,EAAEG,WAAW,CAAC;;MAElD;MACAlH,QAAQ,CAACqH,SAAS,IAChBA,SAAS,CAAClE,GAAG,CAACqB,IAAI,IAChBA,IAAI,CAAC1F,EAAE,KAAKiI,MAAM,GACd;QAAE,GAAGvC,IAAI;QAAEvE,QAAQ,EAAEgH,cAAc;QAAEzH,SAAS,EAAE,IAAIuG,IAAI,CAAC,CAAC,CAACoB,WAAW,CAAC;MAAE,CAAC,GAC1E3C,IACN,CACF,CAAC;IACH,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAhC,QAAQ,CAACqH,SAAS,IAChBA,SAAS,CAAClE,GAAG,CAACqB,IAAI,IAChBA,IAAI,CAAC1F,EAAE,KAAKiI,MAAM,GACd;QAAE,GAAGvC,IAAI;QAAEvE,QAAQ,EAAEgH,cAAc;QAAEzH,SAAS,EAAE,IAAIuG,IAAI,CAAC,CAAC,CAACoB,WAAW,CAAC;MAAE,CAAC,GAC1E3C,IACN,CACF,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAM8C,eAAe,GAAI9C,IAAI,IAAK;IAChCvC,OAAO,CAACO,GAAG,CAAC,6BAA6B,EAAEgC,IAAI,CAAC;;IAEhD;IACAvG,QAAQ,CAAC,oBAAoBuG,IAAI,CAAC1F,EAAE,EAAE,EAAE;MACtCF,KAAK,EAAE;QACL4F,IAAI,EAAEA,IAAI;QACV/E,OAAO,EAAEA,OAAO;QAChB8H,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIvH,QAAQ,IAAK;IAClC;IACA,IAAI,CAAC9B,eAAe,EAAE;MACpB8D,OAAO,CAACO,GAAG,CAAC,+BAA+B,CAAC;MAC5C;MACAvE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI,CAACwF,cAAc,CAAC,CAAC,EAAE;MACrB,IAAInF,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACO,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLP,OAAO,CAACO,GAAG,CAAC,2DAA2D,CAAC;MAC1E;MACA;IACF;IACAlB,mBAAmB,CAACrB,QAAQ,CAAC;IAC7Be,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMyG,cAAc,GAAG,MAAOC,OAAO,IAAK;IACxC;IACA1H,QAAQ,CAACqH,SAAS,IAAI,CAAC,GAAGA,SAAS,EAAEK,OAAO,CAAC,CAAC;IAE9C,IAAI;MACF;MACA,MAAMvF,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;MACvE,MAAMgC,MAAM,GAAG,MAAMjC,UAAU,CAACpC,KAAK,CAAC4H,MAAM,CAACD,OAAO,CAAC;MACrDzF,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAE4B,MAAM,CAAC;;MAE5C;MACA,MAAMwD,WAAW,GAAGxD,MAAM,CAACtC,IAAI,IAAI4F,OAAO;MAC1C,IAAIE,WAAW,CAAC9I,EAAE,KAAK4I,OAAO,CAAC5I,EAAE,EAAE;QACjCkB,QAAQ,CAACqH,SAAS,IAChBA,SAAS,CAAClE,GAAG,CAACqB,IAAI,IAChBA,IAAI,CAAC1F,EAAE,KAAK4I,OAAO,CAAC5I,EAAE,GAAG8I,WAAW,GAAGpD,IACzC,CACF,CAAC;MACH;;MAEA;MACA,IAAIoD,WAAW,CAACzH,UAAU,IAAIyH,WAAW,CAACzH,UAAU,CAACyC,MAAM,GAAG,CAAC,EAAE;QAC/D,IAAI;UACF,MAAMiF,mBAAmB,GAAG,CAAC,MAAM,MAAM,CAAC,iCAAiC,CAAC,EAAEzF,OAAO;UAErF,KAAK,MAAMyD,UAAU,IAAI+B,WAAW,CAACzH,UAAU,EAAE;YAC/C,IAAI0F,UAAU,MAAKzH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,EAAE,GAAE;cAAE;cACpC,MAAM+I,mBAAmB,CAACC,kBAAkB,CAC1CF,WAAW,EACX/B,UAAU,EACVzH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,EACf,CAAC;YACH;UACF;QACF,CAAC,CAAC,OAAOiJ,iBAAiB,EAAE;UAC1B9F,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAE+F,iBAAiB,CAAC;QACnF;MACF;MAEA9F,OAAO,CAACO,GAAG,CAAC,aAAa,EAAEoF,WAAW,CAAC;IACzC,CAAC,CAAC,OAAO5F,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACA;IACF;EACF,CAAC;;EAED;EACA,MAAMgG,gBAAgB,GAAIC,SAAS,IAAK;IACtC,IAAI,CAACrE,gBAAgB,CAAC,CAAC,EAAE;MACvB,IAAItF,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACO,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLP,OAAO,CAACO,GAAG,CAAC,6DAA6D,CAAC;MAC5E;MACA;IACF;IACA5C,UAAU,CAACsI,WAAW,IAAI,CAAC,GAAGA,WAAW,EAAED,SAAS,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAClI,QAAQ,EAAEmI,OAAO,KAAK;IAC9C,IAAI,CAACvE,cAAc,CAAC,CAAC,EAAE;MACrB,IAAIvF,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACO,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLP,OAAO,CAACO,GAAG,CAAC,2DAA2D,CAAC;MAC1E;MACA;IACF;IACA5C,UAAU,CAACsI,WAAW,IACpBA,WAAW,CAAC/E,GAAG,CAACC,GAAG,IACjBA,GAAG,CAACtE,EAAE,KAAKmB,QAAQ,GACf;MAAE,GAAGmD,GAAG;MAAE,GAAGgF,OAAO;MAAE5I,SAAS,EAAE,IAAIuG,IAAI,CAAC,CAAC,CAACoB,WAAW,CAAC;IAAE,CAAC,GAC3D/D,GACN,CACF,CAAC;EACH,CAAC;EAED,MAAMiF,kBAAkB,GAAIpI,QAAQ,IAAK;IAAA,IAAAqI,SAAA;IACvC,IAAI,CAACxE,gBAAgB,CAAC,CAAC,EAAE;MACvB,IAAIxF,QAAQ,KAAK,QAAQ,EAAE;QACzB2D,OAAO,CAACO,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLP,OAAO,CAACO,GAAG,CAAC,6DAA6D,CAAC;MAC5E;MACA;IACF;IACA;IACA,MAAM+F,aAAa,IAAAD,SAAA,GAAG3I,OAAO,CAAC,CAAC,CAAC,cAAA2I,SAAA,uBAAVA,SAAA,CAAYxJ,EAAE;IACpC,IAAIyJ,aAAa,IAAIA,aAAa,KAAKtI,QAAQ,EAAE;MAC/CD,QAAQ,CAACqH,SAAS,IAChBA,SAAS,CAAClE,GAAG,CAACqB,IAAI,IAChBA,IAAI,CAACvE,QAAQ,KAAKA,QAAQ,GACtB;QAAE,GAAGuE,IAAI;QAAEvE,QAAQ,EAAEsI;MAAc,CAAC,GACpC/D,IACN,CACF,CAAC;IACH;IAEA5E,UAAU,CAACsI,WAAW,IAAIA,WAAW,CAAClD,MAAM,CAAC5B,GAAG,IAAIA,GAAG,CAACtE,EAAE,KAAKmB,QAAQ,CAAC,CAAC;EAC3E,CAAC;;EAED;EACA,MAAMuI,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACzE,gBAAgB,CAAC,CAAC,EAAE;MACvB9B,OAAO,CAACO,GAAG,CAAC,2CAA2C,CAAC;MACxD;IACF;IACApB,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMqH,oBAAoB,GAAIC,UAAU,IAAK;IAC3CzG,OAAO,CAACO,GAAG,CAAC,kBAAkB,EAAEkG,UAAU,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIP,OAAO,IAAK;IACrCnG,OAAO,CAACO,GAAG,CAAC,gBAAgB,EAAE4F,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAI3I,QAAQ,IAAK;IACtC,OAAO8E,aAAa,CAACC,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACvE,QAAQ,KAAKA,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,IAAI,CAAC9B,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAK+K,SAAS,EAAC,6DAA6D;MAAAC,QAAA,eAC1EhL,OAAA;QAAK+K,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhL,OAAA;UAAK+K,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC5FhL,OAAA,CAACR,IAAI;YAACyB,IAAI,EAAC,QAAQ;YAAC+B,IAAI,EAAE,EAAG;YAAC+H,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACNpL,OAAA;UAAI+K,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFpL,OAAA;UAAG+K,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpL,OAAA,CAACP,MAAM;UAAC4L,OAAO,EAAEA,CAAA,KAAMlL,QAAQ,CAAC,QAAQ,CAAE;UAAC4K,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpL,OAAA,CAACf,WAAW;IAACqM,OAAO,EAAEpM,YAAa;IAAA8L,QAAA,eACjChL,OAAA;MAAK+K,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzChL,OAAA,CAACV,eAAe;QACdkB,QAAQ,EAAEA,QAAQ,CAACgH,WAAW,CAAC,CAAE;QACjClH,WAAW,EAAEA,WAAW,GAAG;UACzBW,IAAI,EAAE,GAAGX,WAAW,CAACiL,SAAS,IAAIjL,WAAW,CAACkL,QAAQ,EAAE;UACxDC,KAAK,EAAEnL,WAAW,CAACmL,KAAK;UACxBC,MAAM,EAAEpL,WAAW,CAACoL,MAAM,IAAI,2BAA2B;UACzDzH,IAAI,EAAEzD;QACR,CAAC,GAAG;UACFS,IAAI,EAAE,YAAY;UAClBwK,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCzH,IAAI,EAAEzD;QACR;MAAE;QAAAyK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFpL,OAAA;QAAM+K,SAAS,EAAC,OAAO;QAAAC,QAAA,eACrBhL,OAAA;UAAK+K,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnDhL,OAAA,CAACT,UAAU;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGdpL,OAAA;YAAK+K,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBhL,OAAA;cAAK+K,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/ChL,OAAA;gBAAK+K,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,eAC/EhL,OAAA,CAACR,IAAI;kBAACyB,IAAI,EAAC,QAAQ;kBAAC+B,IAAI,EAAE,EAAG;kBAAC+H,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNpL,OAAA;gBAAAgL,QAAA,gBACEhL,OAAA;kBAAI+K,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEpL,OAAA;kBAAG+K,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpL,OAAA,CAACN,WAAW;YACV2B,KAAK,EAAEA,KAAM;YACbM,OAAO,EAAEA,OAAQ;YACjBgK,aAAa,EAAEd,iBAAkB;YACjCe,cAAc,EAAElB,kBAAmB;YACnCmB,cAAc,EAAEjI,gBAAiB;YACjCkI,cAAc,EAAEpI,cAAe;YAC/BD,WAAW,EAAEA,WAAY;YACzBE,aAAa,EAAEA,aAAc;YAC7BsC,gBAAgB,EAAEA,gBAAgB,CAAC;UAAE;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAGNpL,OAAA;YAAK+K,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhL,OAAA;cAAK+K,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAEjDnJ,OAAO,CACL2D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC,CACjCqD,GAAG,CAACgB,MAAM,iBACTrG,OAAA,CAACL,WAAW;gBAEV0G,MAAM,EAAEA,MAAO;gBACfpE,KAAK,EAAE6I,iBAAiB,CAACzE,MAAM,CAACrF,EAAE,CAAE;gBACpC+K,UAAU,EAAE/C,cAAe;gBAC3BgD,WAAW,EAAExC,eAAgB;gBAC7ByC,SAAS,EAAEvC,aAAc;gBACzBwC,YAAY,EAAE7B,gBAAiB;gBAC/B8B,cAAc,EAAE5B,kBAAmB;gBACnC5I,OAAO,EAAEA,OAAQ;gBACjBgE,cAAc,EAAEA,cAAc,CAAC,CAAE;gBACjCI,cAAc,EAAEA,cAAc,CAAC,CAAE;gBACjCC,gBAAgB,EAAEA,gBAAgB,CAAC,CAAE;gBACrCE,YAAY,EAAEA,YAAY,CAAC;cAAE,GAZxBG,MAAM,CAACrF,EAAE;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACF,CAAC,EAGHtF,gBAAgB,CAAC,CAAC,iBACjB9F,OAAA;gBAAK+K,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BhL,OAAA,CAACP,MAAM;kBACL2M,OAAO,EAAC,SAAS;kBACjBf,OAAO,EAAEA,CAAA,KAAMjI,qBAAqB,CAAC,IAAI,CAAE;kBAC3C2H,SAAS,EAAC,0GAA0G;kBACpHsB,QAAQ,EAAC,MAAM;kBACfC,YAAY,EAAC,MAAM;kBAAAtB,QAAA,EACpB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGLnE,aAAa,CAACnC,MAAM,KAAK,CAAC,IAAIrB,WAAW,iBACxCzD,OAAA;cAAK+K,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAC9DhL,OAAA,CAACR,IAAI;gBAACyB,IAAI,EAAC,QAAQ;gBAAC+B,IAAI,EAAE,EAAG;gBAAC+H,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrEpL,OAAA;gBAAI+K,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9EpL,OAAA;gBAAG+K,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAExD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJpL,OAAA,CAACP,MAAM;gBACL2M,OAAO,EAAC,SAAS;gBACjBf,OAAO,EAAEA,CAAA,KAAM;kBACb3H,cAAc,CAAC,EAAE,CAAC;kBAClBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAE;gBACFmH,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACjB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNpL,OAAA,CAACJ,YAAY;YACX2M,MAAM,EAAEtJ,gBAAiB;YACzBuJ,OAAO,EAAEA,CAAA,KAAM;cACbtJ,mBAAmB,CAAC,KAAK,CAAC;cAC1BM,mBAAmB,CAAC,IAAI,CAAC;YAC3B,CAAE;YACFiJ,MAAM,EAAE9C,cAAe;YACvBxH,QAAQ,EAAEoB,gBAAiB;YAC3B5B,OAAO,EAAEA;UAAQ;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEFpL,OAAA,CAACH,cAAc;YACb0M,MAAM,EAAEpJ,kBAAmB;YAC3BqJ,OAAO,EAAEA,CAAA,KAAMpJ,qBAAqB,CAAC,KAAK,CAAE;YAC5CqJ,MAAM,EAAEvC;UAAiB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEFpL,OAAA,CAACF,iBAAiB;YAChByM,MAAM,EAAElJ,qBAAsB;YAC9BmJ,OAAO,EAAEA,CAAA,KAAMlJ,wBAAwB,CAAC,KAAK,CAAE;YAC/CoJ,QAAQ,EAAE/B;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAAClL,EAAA,CA/xBID,WAAW;EAAA,QACEd,WAAW,EACMC,OAAO;AAAA;AAAAuN,EAAA,GAFrC1M,WAAW;AAiyBjB,eAAeA,WAAW;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}