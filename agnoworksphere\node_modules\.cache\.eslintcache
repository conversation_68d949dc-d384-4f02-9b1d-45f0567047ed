[{"C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx": "1", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx": "2", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "8", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "9", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "15", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "16", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "17", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "18", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "19", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "20", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "21", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "22", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "23", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "24", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "25", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "26", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "27", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "28", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "29", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "30", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "31", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "32", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "33", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "34", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "35", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "36", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "37", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "38", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "39", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "40", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "41", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "42", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "43", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "44", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "46", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "47", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "48", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "49", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "50", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "51", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "52", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "53", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "54", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "55", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "56", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "57", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "58", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "59", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "60", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "61", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "62", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "63", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "64", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "65", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "66", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "67", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "68", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "69", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "70", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "71", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "72", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js": "73", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "74", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "75", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "76", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "77", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "78", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js": "80", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx": "81", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx": "82", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx": "83", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx": "84", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx": "85", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx": "86", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx": "87", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js": "88", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js": "89", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js": "90", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx": "91", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js": "92", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js": "93", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js": "94", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx": "95", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx": "96", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realAuthService.js": "97", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ProtectedRoute.jsx": "98", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\hooks\\useUserProfile.js": "99", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\NotificationDropdown.jsx": "100", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateTaskModal.jsx": "101", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\AIReportModal.jsx": "102", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiReportService.js": "103", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\projectEventService.js": "104", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\charts\\ReportCharts.jsx": "105", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateAIProjectModal.jsx": "106", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\EnhancedCreateAIProjectModal.jsx": "107", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectExportButton.jsx": "108", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\EnhancedProjectCreationWizard.jsx": "109", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfigurationInterface.jsx": "110", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectOverviewEditor.jsx": "111", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TechStackDisplay.jsx": "112", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\WorkflowManagement.jsx": "113", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfirmationSummary.jsx": "114", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TaskChecklistSystem.jsx": "115", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Slider.jsx": "116", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RichTextEditor.jsx": "117", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\DatePicker.jsx": "118", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Toggle.jsx": "119", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Modal.jsx": "120", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\openaiService.js": "121", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectSelector.jsx": "122", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ProjectContext.jsx": "123"}, {"size": 1396, "mtime": 1754034229833, "results": "124", "hashOfConfig": "125"}, {"size": 1113, "mtime": 1754377048939, "results": "126", "hashOfConfig": "125"}, {"size": 2842, "mtime": 1754286869549, "results": "127", "hashOfConfig": "125"}, {"size": 2932, "mtime": 1754398555389, "results": "128", "hashOfConfig": "125"}, {"size": 263, "mtime": 1753662156000, "results": "129", "hashOfConfig": "125"}, {"size": 2597, "mtime": 1753662156000, "results": "130", "hashOfConfig": "125"}, {"size": 1443, "mtime": 1753896368000, "results": "131", "hashOfConfig": "125"}, {"size": 1851, "mtime": 1753662960000, "results": "132", "hashOfConfig": "125"}, {"size": 2221, "mtime": 1754033188241, "results": "133", "hashOfConfig": "125"}, {"size": 28900, "mtime": 1754399591300, "results": "134", "hashOfConfig": "125"}, {"size": 26652, "mtime": 1754397369173, "results": "135", "hashOfConfig": "125"}, {"size": 19565, "mtime": 1754126131895, "results": "136", "hashOfConfig": "125"}, {"size": 8178, "mtime": 1754043014618, "results": "137", "hashOfConfig": "125"}, {"size": 12073, "mtime": 1754036144487, "results": "138", "hashOfConfig": "125"}, {"size": 13803, "mtime": 1754377122989, "results": "139", "hashOfConfig": "125"}, {"size": 9015, "mtime": 1754287684311, "results": "140", "hashOfConfig": "125"}, {"size": 33701, "mtime": 1754390203657, "results": "141", "hashOfConfig": "125"}, {"size": 619, "mtime": 1753662156000, "results": "142", "hashOfConfig": "125"}, {"size": 3229, "mtime": 1753931434000, "results": "143", "hashOfConfig": "125"}, {"size": 15516, "mtime": 1754037223798, "results": "144", "hashOfConfig": "125"}, {"size": 3119, "mtime": 1753662156000, "results": "145", "hashOfConfig": "125"}, {"size": 9775, "mtime": 1753662156000, "results": "146", "hashOfConfig": "125"}, {"size": 1661, "mtime": 1753662960000, "results": "147", "hashOfConfig": "125"}, {"size": 8345, "mtime": 1754040316710, "results": "148", "hashOfConfig": "125"}, {"size": 2069, "mtime": 1753662960000, "results": "149", "hashOfConfig": "125"}, {"size": 4544, "mtime": 1754316602745, "results": "150", "hashOfConfig": "125"}, {"size": 1270, "mtime": 1753662960000, "results": "151", "hashOfConfig": "125"}, {"size": 12175, "mtime": 1754056135125, "results": "152", "hashOfConfig": "125"}, {"size": 14625, "mtime": 1754121145710, "results": "153", "hashOfConfig": "125"}, {"size": 3061, "mtime": 1753662960000, "results": "154", "hashOfConfig": "125"}, {"size": 15972, "mtime": 1754399497114, "results": "155", "hashOfConfig": "125"}, {"size": 11396, "mtime": 1754289682949, "results": "156", "hashOfConfig": "125"}, {"size": 7293, "mtime": 1754121209630, "results": "157", "hashOfConfig": "125"}, {"size": 4957, "mtime": 1754399566193, "results": "158", "hashOfConfig": "125"}, {"size": 4319, "mtime": 1753662960000, "results": "159", "hashOfConfig": "125"}, {"size": 3173, "mtime": 1754300540067, "results": "160", "hashOfConfig": "125"}, {"size": 12247, "mtime": 1754392334339, "results": "161", "hashOfConfig": "125"}, {"size": 8725, "mtime": 1754391573431, "results": "162", "hashOfConfig": "125"}, {"size": 4128, "mtime": 1754391431906, "results": "163", "hashOfConfig": "125"}, {"size": 4478, "mtime": 1754391531777, "results": "164", "hashOfConfig": "125"}, {"size": 3187, "mtime": 1754391493181, "results": "165", "hashOfConfig": "125"}, {"size": 6524, "mtime": 1754123731298, "results": "166", "hashOfConfig": "125"}, {"size": 5297, "mtime": 1754391605228, "results": "167", "hashOfConfig": "125"}, {"size": 5667, "mtime": 1753662960000, "results": "168", "hashOfConfig": "125"}, {"size": 6178, "mtime": 1753662960000, "results": "169", "hashOfConfig": "125"}, {"size": 3646, "mtime": 1754043560517, "results": "170", "hashOfConfig": "125"}, {"size": 8113, "mtime": 1754043500588, "results": "171", "hashOfConfig": "125"}, {"size": 6935, "mtime": 1753662960000, "results": "172", "hashOfConfig": "125"}, {"size": 5068, "mtime": 1753662960000, "results": "173", "hashOfConfig": "125"}, {"size": 3503, "mtime": 1754043686958, "results": "174", "hashOfConfig": "125"}, {"size": 11618, "mtime": 1754120839603, "results": "175", "hashOfConfig": "125"}, {"size": 12779, "mtime": 1753660406000, "results": "176", "hashOfConfig": "125"}, {"size": 18772, "mtime": 1753660406000, "results": "177", "hashOfConfig": "125"}, {"size": 16373, "mtime": 1753660406000, "results": "178", "hashOfConfig": "125"}, {"size": 20926, "mtime": 1754121648073, "results": "179", "hashOfConfig": "125"}, {"size": 40244, "mtime": 1754379687644, "results": "180", "hashOfConfig": "125"}, {"size": 5370, "mtime": 1753663554000, "results": "181", "hashOfConfig": "125"}, {"size": 5637, "mtime": 1753663554000, "results": "182", "hashOfConfig": "125"}, {"size": 1689, "mtime": 1753663554000, "results": "183", "hashOfConfig": "125"}, {"size": 5556, "mtime": 1753663554000, "results": "184", "hashOfConfig": "125"}, {"size": 21051, "mtime": 1754377313180, "results": "185", "hashOfConfig": "125"}, {"size": 18499, "mtime": 1754377225508, "results": "186", "hashOfConfig": "125"}, {"size": 19464, "mtime": 1753667086000, "results": "187", "hashOfConfig": "125"}, {"size": 9333, "mtime": 1754120749717, "results": "188", "hashOfConfig": "125"}, {"size": 13135, "mtime": 1754121313818, "results": "189", "hashOfConfig": "125"}, {"size": 13718, "mtime": 1753660406000, "results": "190", "hashOfConfig": "125"}, {"size": 2946, "mtime": 1753916458000, "results": "191", "hashOfConfig": "125"}, {"size": 5117, "mtime": 1753916458000, "results": "192", "hashOfConfig": "125"}, {"size": 3744, "mtime": 1753916090000, "results": "193", "hashOfConfig": "125"}, {"size": 9103, "mtime": 1754360825970, "results": "194", "hashOfConfig": "125"}, {"size": 7239, "mtime": 1754324730283, "results": "195", "hashOfConfig": "125"}, {"size": 5908, "mtime": 1754286269451, "results": "196", "hashOfConfig": "125"}, {"size": 139, "mtime": 1753662156000, "results": "197", "hashOfConfig": "125"}, {"size": 8156, "mtime": 1754123202462, "results": "198", "hashOfConfig": "125"}, {"size": 5802, "mtime": 1753916090000, "results": "199", "hashOfConfig": "125"}, {"size": 4753, "mtime": 1753662156000, "results": "200", "hashOfConfig": "125"}, {"size": 329, "mtime": 1754389946682, "results": "201", "hashOfConfig": "125"}, {"size": 6210, "mtime": 1754041438145, "results": "202", "hashOfConfig": "125"}, {"size": 7754, "mtime": 1754324345552, "results": "203", "hashOfConfig": "125"}, {"size": 13056, "mtime": 1754196793734, "results": "204", "hashOfConfig": "125"}, {"size": 10086, "mtime": 1754363362126, "results": "205", "hashOfConfig": "125"}, {"size": 623, "mtime": 1753979082425, "results": "206", "hashOfConfig": "125"}, {"size": 6960, "mtime": 1754290875021, "results": "207", "hashOfConfig": "125"}, {"size": 8078, "mtime": 1754026444720, "results": "208", "hashOfConfig": "125"}, {"size": 2645, "mtime": 1754026405389, "results": "209", "hashOfConfig": "125"}, {"size": 8796, "mtime": 1754030789819, "results": "210", "hashOfConfig": "125"}, {"size": 27243, "mtime": 1754290751151, "results": "211", "hashOfConfig": "125"}, {"size": 5668, "mtime": 1754050276943, "results": "212", "hashOfConfig": "125"}, {"size": 10660, "mtime": 1754318186933, "results": "213", "hashOfConfig": "125"}, {"size": 8055, "mtime": 1754050911094, "results": "214", "hashOfConfig": "125"}, {"size": 24107, "mtime": 1754055910235, "results": "215", "hashOfConfig": "125"}, {"size": 26673, "mtime": 1754398938902, "results": "216", "hashOfConfig": "125"}, {"size": 4832, "mtime": 1754284927004, "results": "217", "hashOfConfig": "125"}, {"size": 11155, "mtime": 1754289886219, "results": "218", "hashOfConfig": "125"}, {"size": 15845, "mtime": 1754195084404, "results": "219", "hashOfConfig": "125"}, {"size": 15642, "mtime": 1754195149865, "results": "220", "hashOfConfig": "125"}, {"size": 6775, "mtime": 1754324325311, "results": "221", "hashOfConfig": "125"}, {"size": 1009, "mtime": 1754286836782, "results": "222", "hashOfConfig": "125"}, {"size": 5813, "mtime": 1754390440705, "results": "223", "hashOfConfig": "125"}, {"size": 8994, "mtime": 1754289962090, "results": "224", "hashOfConfig": "125"}, {"size": 14588, "mtime": 1754318206343, "results": "225", "hashOfConfig": "125"}, {"size": 20788, "mtime": 1754301009875, "results": "226", "hashOfConfig": "125"}, {"size": 13482, "mtime": 1754300981568, "results": "227", "hashOfConfig": "125"}, {"size": 2628, "mtime": 1754298424105, "results": "228", "hashOfConfig": "125"}, {"size": 7571, "mtime": 1754301031662, "results": "229", "hashOfConfig": "125"}, {"size": 15107, "mtime": 1754326339751, "results": "230", "hashOfConfig": "125"}, {"size": 35278, "mtime": 1754362856087, "results": "231", "hashOfConfig": "125"}, {"size": 6593, "mtime": 1754326501301, "results": "232", "hashOfConfig": "125"}, {"size": 10902, "mtime": 1754370545307, "results": "233", "hashOfConfig": "125"}, {"size": 11962, "mtime": 1754371238037, "results": "234", "hashOfConfig": "125"}, {"size": 20724, "mtime": 1754371917454, "results": "235", "hashOfConfig": "125"}, {"size": 23740, "mtime": 1754331781381, "results": "236", "hashOfConfig": "125"}, {"size": 22414, "mtime": 1754379286255, "results": "237", "hashOfConfig": "125"}, {"size": 21639, "mtime": 1754360445977, "results": "238", "hashOfConfig": "125"}, {"size": 22675, "mtime": 1754379254860, "results": "239", "hashOfConfig": "125"}, {"size": 2312, "mtime": 1754331472937, "results": "240", "hashOfConfig": "125"}, {"size": 8044, "mtime": 1754331598347, "results": "241", "hashOfConfig": "125"}, {"size": 2581, "mtime": 1754331498421, "results": "242", "hashOfConfig": "125"}, {"size": 2129, "mtime": 1754331485404, "results": "243", "hashOfConfig": "125"}, {"size": 2383, "mtime": 1754331687394, "results": "244", "hashOfConfig": "125"}, {"size": 14316, "mtime": 1754362343814, "results": "245", "hashOfConfig": "125"}, {"size": 6769, "mtime": 1754376934870, "results": "246", "hashOfConfig": "125"}, {"size": 6224, "mtime": 1754398494806, "results": "247", "hashOfConfig": "125"}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7s4ywu", {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["617", "618", "619", "620", "621"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", ["622"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", ["623", "624"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["625", "626"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", ["627", "628", "629", "630"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", ["631", "632", "633", "634", "635", "636", "637"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", ["638"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", ["639", "640", "641"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["642", "643", "644", "645"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["646", "647", "648"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", ["649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["664", "665", "666", "667"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", ["668", "669", "670"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", ["671"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx", ["672"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx", ["673", "674"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js", ["675", "676"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js", ["677"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js", ["678"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js", ["679"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx", ["680"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx", ["681"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realAuthService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\hooks\\useUserProfile.js", ["682"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\NotificationDropdown.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateTaskModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\AIReportModal.jsx", ["683"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiReportService.js", ["684"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\projectEventService.js", ["685"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\charts\\ReportCharts.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateAIProjectModal.jsx", ["686", "687", "688", "689", "690"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\EnhancedCreateAIProjectModal.jsx", ["691"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectExportButton.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\EnhancedProjectCreationWizard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfigurationInterface.jsx", ["692", "693"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectOverviewEditor.jsx", ["694", "695", "696"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TechStackDisplay.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\WorkflowManagement.jsx", ["697", "698", "699", "700"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfirmationSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TaskChecklistSystem.jsx", ["701", "702", "703", "704", "705", "706", "707", "708"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Slider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RichTextEditor.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\DatePicker.jsx", ["709", "710", "711"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Toggle.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Modal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\openaiService.js", ["712"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectSelector.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ProjectContext.jsx", ["713"], [], {"ruleId": "714", "severity": 1, "message": "715", "line": 20, "column": 11, "nodeType": "716", "messageId": "717", "endLine": 20, "endColumn": 15}, {"ruleId": "714", "severity": 1, "message": "718", "line": 61, "column": 19, "nodeType": "716", "messageId": "717", "endLine": 61, "endColumn": 29}, {"ruleId": "719", "severity": 1, "message": "720", "line": 326, "column": 6, "nodeType": "721", "endLine": 326, "endColumn": 8, "suggestions": "722"}, {"ruleId": "714", "severity": 1, "message": "723", "line": 334, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 334, "endColumn": 21}, {"ruleId": "714", "severity": 1, "message": "724", "line": 339, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 339, "endColumn": 23}, {"ruleId": "719", "severity": 1, "message": "725", "line": 327, "column": 9, "nodeType": "726", "endLine": 369, "endColumn": 4, "suggestions": "727"}, {"ruleId": "714", "severity": 1, "message": "728", "line": 69, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 69, "endColumn": 17}, {"ruleId": "719", "severity": 1, "message": "729", "line": 191, "column": 6, "nodeType": "721", "endLine": 191, "endColumn": 52, "suggestions": "730"}, {"ruleId": "714", "severity": 1, "message": "731", "line": 19, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 19, "endColumn": 17}, {"ruleId": "714", "severity": 1, "message": "732", "line": 23, "column": 27, "nodeType": "716", "messageId": "717", "endLine": 23, "endColumn": 45}, {"ruleId": "714", "severity": 1, "message": "733", "line": 8, "column": 8, "nodeType": "716", "messageId": "717", "endLine": 8, "endColumn": 19}, {"ruleId": "714", "severity": 1, "message": "734", "line": 19, "column": 5, "nodeType": "716", "messageId": "717", "endLine": 19, "endColumn": 22}, {"ruleId": "714", "severity": 1, "message": "735", "line": 23, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 23, "endColumn": 21}, {"ruleId": "714", "severity": 1, "message": "728", "line": 25, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 25, "endColumn": 17}, {"ruleId": "714", "severity": 1, "message": "736", "line": 28, "column": 5, "nodeType": "716", "messageId": "717", "endLine": 28, "endColumn": 16}, {"ruleId": "714", "severity": 1, "message": "737", "line": 30, "column": 5, "nodeType": "716", "messageId": "717", "endLine": 30, "endColumn": 27}, {"ruleId": "714", "severity": 1, "message": "738", "line": 31, "column": 14, "nodeType": "716", "messageId": "717", "endLine": 31, "endColumn": 28}, {"ruleId": "714", "severity": 1, "message": "739", "line": 35, "column": 23, "nodeType": "716", "messageId": "717", "endLine": 35, "endColumn": 37}, {"ruleId": "714", "severity": 1, "message": "740", "line": 36, "column": 23, "nodeType": "716", "messageId": "717", "endLine": 36, "endColumn": 37}, {"ruleId": "719", "severity": 1, "message": "741", "line": 192, "column": 6, "nodeType": "721", "endLine": 192, "endColumn": 22, "suggestions": "742"}, {"ruleId": "743", "severity": 1, "message": "744", "line": 299, "column": 56, "nodeType": "716", "messageId": "745", "endLine": 299, "endColumn": 70}, {"ruleId": "714", "severity": 1, "message": "746", "line": 8, "column": 49, "nodeType": "716", "messageId": "717", "endLine": 8, "endColumn": 60}, {"ruleId": "714", "severity": 1, "message": "747", "line": 4, "column": 31, "nodeType": "716", "messageId": "717", "endLine": 4, "endColumn": 48}, {"ruleId": "714", "severity": 1, "message": "748", "line": 12, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 12, "endColumn": 25}, {"ruleId": "714", "severity": 1, "message": "749", "line": 66, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 66, "endColumn": 31}, {"ruleId": "714", "severity": 1, "message": "728", "line": 11, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 11, "endColumn": 17}, {"ruleId": "714", "severity": 1, "message": "750", "line": 12, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 12, "endColumn": 16}, {"ruleId": "714", "severity": 1, "message": "751", "line": 12, "column": 18, "nodeType": "716", "messageId": "717", "endLine": 12, "endColumn": 27}, {"ruleId": "714", "severity": 1, "message": "752", "line": 57, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 57, "endColumn": 18}, {"ruleId": "714", "severity": 1, "message": "753", "line": 9, "column": 8, "nodeType": "716", "messageId": "717", "endLine": 9, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "755", "line": 151, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 169, "endColumn": 6}, {"ruleId": "714", "severity": 1, "message": "758", "line": 181, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 181, "endColumn": 21}, {"ruleId": "714", "severity": 1, "message": "759", "line": 11, "column": 34, "nodeType": "716", "messageId": "717", "endLine": 11, "endColumn": 57}, {"ruleId": "714", "severity": 1, "message": "760", "line": 18, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 18, "endColumn": 36}, {"ruleId": "719", "severity": 1, "message": "761", "line": 102, "column": 6, "nodeType": "721", "endLine": 102, "endColumn": 24, "suggestions": "762"}, {"ruleId": "719", "severity": 1, "message": "761", "line": 116, "column": 6, "nodeType": "721", "endLine": 116, "endColumn": 24, "suggestions": "763"}, {"ruleId": "714", "severity": 1, "message": "764", "line": 217, "column": 30, "nodeType": "716", "messageId": "717", "endLine": 217, "endColumn": 51}, {"ruleId": "714", "severity": 1, "message": "765", "line": 218, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 218, "endColumn": 19}, {"ruleId": "714", "severity": 1, "message": "766", "line": 223, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 223, "endColumn": 20}, {"ruleId": "714", "severity": 1, "message": "767", "line": 224, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 224, "endColumn": 26}, {"ruleId": "714", "severity": 1, "message": "768", "line": 227, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 227, "endColumn": 30}, {"ruleId": "714", "severity": 1, "message": "769", "line": 248, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 248, "endColumn": 22}, {"ruleId": "714", "severity": 1, "message": "770", "line": 254, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 254, "endColumn": 28}, {"ruleId": "714", "severity": 1, "message": "771", "line": 265, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 265, "endColumn": 25}, {"ruleId": "714", "severity": 1, "message": "772", "line": 274, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 274, "endColumn": 24}, {"ruleId": "714", "severity": 1, "message": "773", "line": 289, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 289, "endColumn": 34}, {"ruleId": "714", "severity": 1, "message": "774", "line": 312, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 312, "endColumn": 33}, {"ruleId": "714", "severity": 1, "message": "728", "line": 20, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 20, "endColumn": 17}, {"ruleId": "719", "severity": 1, "message": "775", "line": 27, "column": 6, "nodeType": "721", "endLine": 27, "endColumn": 15, "suggestions": "776"}, {"ruleId": "714", "severity": 1, "message": "777", "line": 192, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 192, "endColumn": 24}, {"ruleId": "714", "severity": 1, "message": "778", "line": 198, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 198, "endColumn": 23}, {"ruleId": "714", "severity": 1, "message": "779", "line": 5, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 5, "endColumn": 20}, {"ruleId": "719", "severity": 1, "message": "780", "line": 35, "column": 6, "nodeType": "721", "endLine": 35, "endColumn": 15, "suggestions": "781"}, {"ruleId": "719", "severity": 1, "message": "780", "line": 49, "column": 6, "nodeType": "721", "endLine": 49, "endColumn": 15, "suggestions": "782"}, {"ruleId": "714", "severity": 1, "message": "753", "line": 6, "column": 8, "nodeType": "716", "messageId": "717", "endLine": 6, "endColumn": 22}, {"ruleId": "719", "severity": 1, "message": "783", "line": 30, "column": 6, "nodeType": "721", "endLine": 30, "endColumn": 30, "suggestions": "784"}, {"ruleId": "719", "severity": 1, "message": "785", "line": 135, "column": 6, "nodeType": "721", "endLine": 135, "endColumn": 62, "suggestions": "786"}, {"ruleId": "719", "severity": 1, "message": "787", "line": 151, "column": 6, "nodeType": "721", "endLine": 151, "endColumn": 8, "suggestions": "788"}, {"ruleId": "714", "severity": 1, "message": "789", "line": 124, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 124, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "791", "line": 185, "column": 1, "nodeType": "792", "endLine": 193, "endColumn": 3}, {"ruleId": "790", "severity": 1, "message": "791", "line": 338, "column": 1, "nodeType": "792", "endLine": 342, "endColumn": 3}, {"ruleId": "790", "severity": 1, "message": "791", "line": 250, "column": 1, "nodeType": "792", "endLine": 260, "endColumn": 3}, {"ruleId": "714", "severity": 1, "message": "753", "line": 1, "column": 8, "nodeType": "716", "messageId": "717", "endLine": 1, "endColumn": 22}, {"ruleId": "719", "severity": 1, "message": "793", "line": 34, "column": 6, "nodeType": "721", "endLine": 34, "endColumn": 18, "suggestions": "794"}, {"ruleId": "719", "severity": 1, "message": "795", "line": 36, "column": 6, "nodeType": "721", "endLine": 36, "endColumn": 8, "suggestions": "796"}, {"ruleId": "719", "severity": 1, "message": "797", "line": 166, "column": 6, "nodeType": "721", "endLine": 166, "endColumn": 33, "suggestions": "798"}, {"ruleId": "719", "severity": 1, "message": "799", "line": 23, "column": 6, "nodeType": "721", "endLine": 23, "endColumn": 23, "suggestions": "800"}, {"ruleId": "790", "severity": 1, "message": "791", "line": 467, "column": 1, "nodeType": "792", "endLine": 471, "endColumn": 3}, {"ruleId": "790", "severity": 1, "message": "791", "line": 82, "column": 1, "nodeType": "792", "endLine": 89, "endColumn": 3}, {"ruleId": "714", "severity": 1, "message": "801", "line": 1, "column": 27, "nodeType": "716", "messageId": "717", "endLine": 1, "endColumn": 36}, {"ruleId": "714", "severity": 1, "message": "802", "line": 17, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 17, "endColumn": 24}, {"ruleId": "714", "severity": 1, "message": "803", "line": 33, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 33, "endColumn": 24}, {"ruleId": "714", "severity": 1, "message": "804", "line": 41, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 41, "endColumn": 26}, {"ruleId": "714", "severity": 1, "message": "805", "line": 217, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 217, "endColumn": 19}, {"ruleId": "719", "severity": 1, "message": "806", "line": 57, "column": 6, "nodeType": "721", "endLine": 57, "endColumn": 87, "suggestions": "807"}, {"ruleId": "714", "severity": 1, "message": "808", "line": 28, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 28, "endColumn": 25}, {"ruleId": "719", "severity": 1, "message": "809", "line": 33, "column": 6, "nodeType": "721", "endLine": 33, "endColumn": 14, "suggestions": "810"}, {"ruleId": "714", "severity": 1, "message": "811", "line": 27, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 27, "endColumn": 19}, {"ruleId": "719", "severity": 1, "message": "812", "line": 43, "column": 6, "nodeType": "721", "endLine": 43, "endColumn": 35, "suggestions": "813"}, {"ruleId": "714", "severity": 1, "message": "814", "line": 206, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 206, "endColumn": 19}, {"ruleId": "714", "severity": 1, "message": "815", "line": 1, "column": 27, "nodeType": "716", "messageId": "717", "endLine": 1, "endColumn": 33}, {"ruleId": "714", "severity": 1, "message": "801", "line": 1, "column": 35, "nodeType": "716", "messageId": "717", "endLine": 1, "endColumn": 44}, {"ruleId": "714", "severity": 1, "message": "816", "line": 76, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 76, "endColumn": 22}, {"ruleId": "714", "severity": 1, "message": "817", "line": 76, "column": 24, "nodeType": "716", "messageId": "717", "endLine": 76, "endColumn": 39}, {"ruleId": "714", "severity": 1, "message": "801", "line": 1, "column": 27, "nodeType": "716", "messageId": "717", "endLine": 1, "endColumn": 36}, {"ruleId": "714", "severity": 1, "message": "818", "line": 6, "column": 8, "nodeType": "716", "messageId": "717", "endLine": 6, "endColumn": 18}, {"ruleId": "714", "severity": 1, "message": "819", "line": 8, "column": 8, "nodeType": "716", "messageId": "717", "endLine": 8, "endColumn": 13}, {"ruleId": "714", "severity": 1, "message": "820", "line": 92, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 92, "endColumn": 25}, {"ruleId": "714", "severity": 1, "message": "821", "line": 112, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 112, "endColumn": 24}, {"ruleId": "714", "severity": 1, "message": "822", "line": 122, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 122, "endColumn": 24}, {"ruleId": "714", "severity": 1, "message": "823", "line": 196, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 196, "endColumn": 25}, {"ruleId": "714", "severity": 1, "message": "824", "line": 259, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 259, "endColumn": 17}, {"ruleId": "714", "severity": 1, "message": "825", "line": 19, "column": 10, "nodeType": "716", "messageId": "717", "endLine": 19, "endColumn": 16}, {"ruleId": "714", "severity": 1, "message": "826", "line": 19, "column": 18, "nodeType": "716", "messageId": "717", "endLine": 19, "endColumn": 27}, {"ruleId": "714", "severity": 1, "message": "827", "line": 28, "column": 9, "nodeType": "716", "messageId": "717", "endLine": 28, "endColumn": 26}, {"ruleId": "790", "severity": 1, "message": "828", "line": 343, "column": 1, "nodeType": "792", "endLine": 343, "endColumn": 36}, {"ruleId": "719", "severity": 1, "message": "761", "line": 168, "column": 6, "nodeType": "721", "endLine": 168, "endColumn": 36, "suggestions": "829"}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'setMembers' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'columns' and 'isAuthenticated'. Either include them or remove the dependency array.", "ArrayExpression", ["830"], "'canEditCards' is assigned a value but never used.", "'canDeleteCards' is assigned a value but never used.", "The 'handleSaveChanges' function makes the dependencies of useEffect Hook (at line 401) change on every render. To fix this, wrap the definition of 'handleSaveChanges' in its own useCallback() Hook.", "VariableDeclarator", ["831"], "'loading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentUser?.email', 'currentUser?.firstName', 'currentUser?.lastName', and 'userRole'. Either include them or remove the dependency array.", ["832"], "'location' is assigned a value but never used.", "'setSidebarExpanded' is assigned a value but never used.", "'authService' is defined but never used.", "'updateUserProfile' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "'userProfile' is assigned a value but never used.", "'availableOrganizations' is assigned a value but never used.", "'profileLoading' is assigned a value but never used.", "'setSearchValue' is assigned a value but never used.", "'setFilterValue' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentUser' and 'organizations.length'. Either include them or remove the dependency array.", ["833"], "no-use-before-define", "'organizationId' was used before it was defined.", "usedBeforeDefined", "'handleError' is defined but never used.", "'getSuggestedItems' is defined but never used.", "'showSuggestions' is assigned a value but never used.", "'handleAddSuggestedItem' is assigned a value but never used.", "'saving' is assigned a value but never used.", "'setSaving' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "'realApiService' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'refreshProjectsGlobally' is defined but never used.", "'isNotificationDropdownOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["834"], ["835"], "'setNotificationFilter' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'unreadCount' is assigned a value but never used.", "'highPriorityCount' is assigned a value but never used.", "'filteredNotifications' is assigned a value but never used.", "'markAllAsRead' is assigned a value but never used.", "'getNotificationIcon' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'formatTimestamp' is assigned a value but never used.", "'getRoleBasedNotifications' is assigned a value but never used.", "'handleNotificationAction' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["836"], "'priorityOptions' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'useProject' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProjectData'. Either include it or remove the dependency array.", ["837"], ["838"], "React Hook useEffect has a missing dependency: 'loadTeamMembers'. Either include it or remove the dependency array.", ["839"], "React Hook useCallback has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["840"], "React Hook useEffect has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["841"], "'permissions' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'loadAnalyticsData'. Either include it or remove the dependency array.", ["842"], "React Hook useEffect has a missing dependency: 'loadBillingData'. Either include it or remove the dependency array.", ["843"], "React Hook useEffect has a missing dependency: 'loadUserProfile'. Either include it or remove the dependency array.", ["844"], "React Hook useEffect has a missing dependency: 'generateReport'. Either include it or remove the dependency array.", ["845"], "'useEffect' is defined but never used.", "'projectPreview' is assigned a value but never used.", "'teamSizeOptions' is assigned a value but never used.", "'experienceOptions' is assigned a value but never used.", "'handleBack' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleAutoGenerateAI' and 'hasAutoGenerated'. Either include them or remove the dependency array.", ["846"], "'aiGeneratedData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'validateConfiguration'. Either include it or remove the dependency array.", ["847"], "'isEditing' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'generateAIOverview' and 'overview.title'. Either include them or remove the dependency array.", ["848"], "'handleSave' is assigned a value but never used.", "'useRef' is defined but never used.", "'draggedPhase' is assigned a value but never used.", "'setDraggedPhase' is assigned a value but never used.", "'DatePicker' is defined but never used.", "'Modal' is defined but never used.", "'isTaskModalOpen' is assigned a value but never used.", "'assigneeOptions' is assigned a value but never used.", "'categoryOptions' is assigned a value but never used.", "'toggleTaskStatus' is assigned a value but never used.", "'saveTask' is assigned a value but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'formatDisplayDate' is assigned a value but never used.", "Assign instance to a variable before exporting as module default", ["849"], {"desc": "850", "fix": "851"}, {"desc": "852", "fix": "853"}, {"desc": "854", "fix": "855"}, {"desc": "856", "fix": "857"}, {"desc": "858", "fix": "859"}, {"desc": "860", "fix": "861"}, {"desc": "862", "fix": "863"}, {"desc": "864", "fix": "865"}, {"desc": "864", "fix": "866"}, {"desc": "867", "fix": "868"}, {"desc": "869", "fix": "870"}, {"desc": "871", "fix": "872"}, {"desc": "873", "fix": "874"}, {"desc": "875", "fix": "876"}, {"desc": "877", "fix": "878"}, {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, "Update the dependencies array to be: [columns, isAuthenticated]", {"range": "889", "text": "890"}, "Wrap the definition of 'handleSaveChanges' in its own useCallback() Hook.", {"range": "891", "text": "892"}, "Update the dependencies array to be: [currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", {"range": "893", "text": "894"}, "Update the dependencies array to be: [currentUser, location.state, organizations.length]", {"range": "895", "text": "896"}, "Update the dependencies array to be: [loadProjects, organization.id]", {"range": "897", "text": "898"}, "Update the dependencies array to be: [loadProjects, organization?.id]", {"range": "899", "text": "900"}, "Update the dependencies array to be: [loadTasks, project]", {"range": "901", "text": "902"}, "Update the dependencies array to be: [loadProjectData, project]", {"range": "903", "text": "904"}, {"range": "905", "text": "904"}, "Update the dependencies array to be: [isOpen, loadTeamMembers, organizationId]", {"range": "906", "text": "907"}, "Update the dependencies array to be: [defaultShortcuts, sequenceTimeout, keySequence]", {"range": "908", "text": "909"}, "Update the dependencies array to be: [defaultShortcuts]", {"range": "910", "text": "911"}, "Update the dependencies array to be: [loadAnalyticsData, timePeriod]", {"range": "912", "text": "913"}, "Update the dependencies array to be: [loadBillingData]", {"range": "914", "text": "915"}, "Update the dependencies array to be: [isAuthenticated, authUser, loadUserProfile]", {"range": "916", "text": "917"}, "Update the dependencies array to be: [generateReport, isOpen, project]", {"range": "918", "text": "919"}, "Update the dependencies array to be: [formData.name, formData.projectType, formData.teamSize, formData.teamExperience, hasAutoGenerated, handleAutoGenerateAI]", {"range": "920", "text": "921"}, "Update the dependencies array to be: [config, validateConfiguration]", {"range": "922", "text": "923"}, "Update the dependencies array to be: [projectData, hasGeneratedAI, overview.title, generateAIOverview]", {"range": "924", "text": "925"}, "Update the dependencies array to be: [isAuthenticated, authLoading, loadProjects]", {"range": "926", "text": "927"}, [11839, 11841], "[columns, isAuthenticated]", [11743, 13072], "useCallback(async () => {\n    if (!hasUnsavedChanges || !cardData?.id) return;\n\n    setIsSaving(true);\n    try {\n      await updateCardInAPI(pendingChanges);\n\n      // Reload card data to get the latest state from the backend\n      if (cardData?.id) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardData.id);\n          if (result.data) {\n            // Normalize the card data\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n\n            setCardData(normalizedCardData);\n          }\n        } catch (error) {\n          console.error('Error reloading card data after save:', error);\n        }\n      }\n\n      // Clear pending changes\n      setPendingChanges({});\n      setHasUnsavedChanges(false);\n\n      console.log('All changes saved successfully');\n    } catch (error) {\n      console.error('Error saving changes:', error);\n      // You could show a toast notification here\n    } finally {\n      setIsSaving(false);\n    }\n  })", [6367, 6413], "[currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", [7765, 7781], "[currentUser, location.state, organizations.length]", [3945, 3963], "[loadProjects, organization.id]", [4488, 4506], "[loadProjects, organization?.id]", [1142, 1151], "[loadTasks, project]", [1123, 1132], "[loadProjectData, project]", [1609, 1618], [974, 998], "[isO<PERSON>, loadTeamMembers, organizationId]", [4586, 4642], "[defaultShortcuts, sequenceTimeout, keySequence]", [5058, 5060], "[defaultShortcuts]", [1168, 1180], "[loadAnalyticsData, timePeriod]", [1271, 1273], "[loadBillingData]", [5586, 5613], "[isAuthenticated, authUser, loadUserProfile]", [696, 713], "[generateReport, isOpen, project]", [2819, 2900], "[formData.name, formData.projectType, formData.teamSize, formData.teamExperience, hasAutoGenerated, handleAutoGenerateAI]", [929, 937], "[config, validateConfiguration]", [1243, 1272], "[projectData, hasGeneratedAI, overview.title, generateAIOverview]", [5303, 5333], "[isAuthenticated, authLoading, loadProjects]"]