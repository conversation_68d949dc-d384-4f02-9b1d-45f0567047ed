{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\kanban-board\\\\components\\\\AddCardModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Icon from '../../../components/AppIcon';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport Select from '../../../components/ui/Select';\nimport { getAssignableMembers, getAssignmentRestrictionMessage, getRolePermissions } from '../../../utils/rolePermissions';\nimport { generateAIChecklist } from '../../../utils/aiChecklistService';\nimport { validateTaskAssignment, handleAIError, handleError, displayError } from '../../../utils/errorHandling';\nimport authService from '../../../utils/authService';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddCardModal = ({\n  isOpen,\n  onClose,\n  onSave,\n  columnId,\n  members\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    priority: 'medium',\n    assignedTo: [],\n    dueDate: '',\n    labels: [],\n    checklist: []\n  });\n  const [errors, setErrors] = useState({});\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [isGeneratingChecklist, setIsGeneratingChecklist] = useState(false);\n  const [aiChecklistGenerated, setAiChecklistGenerated] = useState(false);\n\n  // Load user data for role-based permissions\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n        setUserRole('member');\n      }\n    };\n    if (isOpen) {\n      loadUserData();\n    }\n  }, [isOpen]);\n  const priorityOptions = [{\n    value: 'low',\n    label: 'Low Priority'\n  }, {\n    value: 'medium',\n    label: 'Medium Priority'\n  }, {\n    value: 'high',\n    label: 'High Priority'\n  }];\n\n  // Filter members based on role permissions\n  const assignableMembers = currentUser ? getAssignableMembers(members, userRole, currentUser.id) : members;\n  const memberOptions = assignableMembers.map(member => ({\n    value: member.id,\n    label: member.name,\n    description: member.role\n  }));\n  const rolePermissions = getRolePermissions(userRole);\n  const assignmentRestrictionMessage = getAssignmentRestrictionMessage(userRole);\n  const labelOptions = [{\n    value: 'bug',\n    label: 'Bug',\n    color: '#ef4444'\n  }, {\n    value: 'feature',\n    label: 'Feature',\n    color: '#3b82f6'\n  }, {\n    value: 'improvement',\n    label: 'Improvement',\n    color: '#10b981'\n  }, {\n    value: 'documentation',\n    label: 'Documentation',\n    color: '#f59e0b'\n  }, {\n    value: 'testing',\n    label: 'Testing',\n    color: '#8b5cf6'\n  }].map(label => ({\n    value: label.value,\n    label: label.label\n  }));\n  const handleInputChange = (field, value) => {\n    // Validate task assignments in real-time\n    if (field === 'assignedTo' && currentUser) {\n      const invalidAssignments = value.filter(userId => {\n        const validationError = validateTaskAssignment(userRole, userId, currentUser.id);\n        return validationError !== null;\n      });\n      if (invalidAssignments.length > 0) {\n        const error = validateTaskAssignment(userRole, invalidAssignments[0], currentUser.id);\n        displayError(error);\n        return; // Don't update the field if assignment is invalid\n      }\n    }\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleAISuggestion = async () => {\n    if (!formData.title.trim()) {\n      alert(\"Please enter a card title first.\");\n      return;\n    }\n    setIsGeneratingChecklist(true);\n    try {\n      // Generate AI checklist\n      const checklistResult = await generateAIChecklist(formData.title, formData.description, formData.priority, 'general' // Could be determined from project context\n      );\n      if (checklistResult.success) {\n        // Enhanced AI suggestions with checklist\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n          .toISOString().split('T')[0],\n          checklist: checklistResult.items\n        };\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate,\n          checklist: mockAI.checklist\n        }));\n        setAiChecklistGenerated(true);\n      } else {\n        const aiError = handleAIError(new Error(checklistResult.error), 'checklist generation');\n        displayError(aiError);\n\n        // Fallback to basic AI suggestion\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]\n        };\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate\n        }));\n      }\n    } catch (error) {\n      const aiError = handleAIError(error, 'AI suggestion');\n      displayError(aiError);\n    } finally {\n      setIsGeneratingChecklist(false);\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.title.trim()) {\n      newErrors.title = 'Card title is required';\n    }\n    if (formData.title.length > 100) {\n      newErrors.title = 'Title must be less than 100 characters';\n    }\n    if (formData.description.length > 500) {\n      newErrors.description = 'Description must be less than 500 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    const newCard = {\n      id: Date.now().toString(),\n      columnId,\n      // For frontend use\n      column_id: columnId,\n      // For backend API\n      title: formData.title.trim(),\n      description: formData.description.trim(),\n      priority: formData.priority,\n      position: 0,\n      // Add position for backend\n      assignedTo: formData.assignedTo,\n      assigned_to: formData.assignedTo,\n      // For backend API\n      dueDate: formData.dueDate || null,\n      due_date: formData.dueDate || null,\n      // For backend API\n      labels: formData.labels.map(labelValue => {\n        const labelData = [{\n          value: 'bug',\n          label: 'Bug',\n          color: '#ef4444'\n        }, {\n          value: 'feature',\n          label: 'Feature',\n          color: '#3b82f6'\n        }, {\n          value: 'improvement',\n          label: 'Improvement',\n          color: '#10b981'\n        }, {\n          value: 'documentation',\n          label: 'Documentation',\n          color: '#f59e0b'\n        }, {\n          value: 'testing',\n          label: 'Testing',\n          color: '#8b5cf6'\n        }].find(l => l.value === labelValue);\n        return {\n          id: labelValue,\n          name: labelData.label,\n          color: labelData.color\n        };\n      }),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      checklist: formData.checklist || [],\n      comments: [],\n      attachments: []\n    };\n    onSave(newCard);\n    handleClose();\n  };\n  const handleClose = () => {\n    setFormData({\n      title: '',\n      description: '',\n      priority: 'medium',\n      assignedTo: [],\n      dueDate: '',\n      labels: [],\n      checklist: []\n    });\n    setErrors({});\n    setAiChecklistGenerated(false);\n    setIsGeneratingChecklist(false);\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black/50 backdrop-blur-sm\",\n      onClick: handleClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-surface rounded-lg shadow-focused w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-text-primary\",\n          children: \"Add New Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"icon\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: \"X\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Card Title\",\n          type: \"text\",\n          placeholder: \"Enter card title...\",\n          value: formData.title,\n          onChange: e => handleInputChange('title', e.target.value),\n          error: errors.title,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end -mt-2 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleAISuggestion,\n            disabled: isGeneratingChecklist,\n            className: \"text-sm text-primary underline hover:no-underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1\",\n            children: isGeneratingChecklist ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Loader2\",\n                size: 14,\n                className: \"animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Generating...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Zap\",\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Generate with AI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-text-primary mb-2\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: \"Enter card description...\",\n            value: formData.description,\n            onChange: e => handleInputChange('description', e.target.value),\n            className: \"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none\",\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-destructive\",\n            children: errors.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Priority\",\n          options: priorityOptions,\n          value: formData.priority,\n          onChange: value => handleInputChange('priority', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            label: \"Assign Members\",\n            options: memberOptions,\n            value: formData.assignedTo,\n            onChange: value => handleInputChange('assignedTo', value),\n            multiple: true,\n            searchable: true,\n            placeholder: rolePermissions.canAssignTasksToOthers ? \"Select team members...\" : \"You can only assign to yourself\",\n            disabled: !rolePermissions.canAssignTasksToSelf && !rolePermissions.canAssignTasksToOthers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), !rolePermissions.canAssignTasksToOthers && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-xs text-text-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Info\",\n              size: 12,\n              className: \"inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), assignmentRestrictionMessage]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Due Date\",\n          type: \"date\",\n          value: formData.dueDate,\n          onChange: e => handleInputChange('dueDate', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Labels\",\n          options: labelOptions,\n          value: formData.labels,\n          onChange: value => handleInputChange('labels', value),\n          multiple: true,\n          placeholder: \"Select labels...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), aiChecklistGenerated && formData.checklist.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-text-primary\",\n              children: \"AI Generated Checklist\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 text-xs text-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Zap\",\n                size: 12,\n                className: \"text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [formData.checklist.length, \" items\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-muted/30 rounded-md p-3 max-h-32 overflow-y-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: [formData.checklist.slice(0, 5).map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 border border-border rounded-sm mt-0.5 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-text-secondary line-clamp-1\",\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this)), formData.checklist.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-text-secondary pl-5\",\n                children: [\"+\", formData.checklist.length - 5, \" more items...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-text-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Info\",\n              size: 12,\n              className: \"inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), \"You can edit these checklist items after creating the card\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-3 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            variant: \"outline\",\n            onClick: handleClose,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"default\",\n            children: \"Create Card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 5\n  }, this);\n};\n_s(AddCardModal, \"PP55s9Y02n2FcnpEOLdDtsIxGGo=\");\n_c = AddCardModal;\nexport default AddCardModal;\nvar _c;\n$RefreshReg$(_c, \"AddCardModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Icon", "<PERSON><PERSON>", "Input", "Select", "getAssignableMembers", "getAssignmentRestrictionMessage", "getRolePermissions", "generateAIChecklist", "validateTaskAssignment", "handleAIError", "handleError", "displayError", "authService", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddCardModal", "isOpen", "onClose", "onSave", "columnId", "members", "_s", "formData", "setFormData", "title", "description", "priority", "assignedTo", "dueDate", "labels", "checklist", "errors", "setErrors", "currentUser", "setCurrentUser", "userRole", "setUserRole", "isGeneratingChecklist", "setIsGeneratingChecklist", "aiChecklistGenerated", "setAiChecklistGenerated", "loadUserData", "userResult", "getCurrentUser", "data", "user", "role", "error", "console", "priorityOptions", "value", "label", "assignableMembers", "id", "memberOptions", "map", "member", "name", "rolePermissions", "assignmentRestrictionMessage", "labelOptions", "color", "handleInputChange", "field", "invalidAssignments", "filter", "userId", "validationError", "length", "prev", "handleAISuggestion", "trim", "alert", "checklistResult", "success", "mockAI", "Date", "now", "toISOString", "split", "items", "aiError", "Error", "validateForm", "newErrors", "Object", "keys", "handleSubmit", "e", "preventDefault", "newCard", "toString", "column_id", "position", "assigned_to", "due_date", "labelValue", "labelData", "find", "l", "createdAt", "updatedAt", "comments", "attachments", "handleClose", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "size", "onSubmit", "type", "placeholder", "onChange", "target", "required", "disabled", "rows", "options", "multiple", "searchable", "canAssignTasksToOthers", "canAssignTasksToSelf", "slice", "item", "index", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/kanban-board/components/AddCardModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Icon from '../../../components/AppIcon';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport Select from '../../../components/ui/Select';\nimport { getAssignableMembers, getAssignmentRestrictionMessage, getRolePermissions } from '../../../utils/rolePermissions';\nimport { generateAIChecklist } from '../../../utils/aiChecklistService';\nimport { validateTaskAssignment, handleAIError, handleError, displayError } from '../../../utils/errorHandling';\nimport authService from '../../../utils/authService';\nimport { useAuth } from '../../../contexts/AuthContext';\n\nconst AddCardModal = ({ isOpen, onClose, onSave, columnId, members }) => {\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    priority: 'medium',\n    assignedTo: [],\n    dueDate: '',\n    labels: [],\n    checklist: []\n  });\n\n  const [errors, setErrors] = useState({});\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [isGeneratingChecklist, setIsGeneratingChecklist] = useState(false);\n  const [aiChecklistGenerated, setAiChecklistGenerated] = useState(false);\n\n  // Load user data for role-based permissions\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || 'member');\n        }\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n        setUserRole('member');\n      }\n    };\n\n    if (isOpen) {\n      loadUserData();\n    }\n  }, [isOpen]);\n\n  const priorityOptions = [\n    { value: 'low', label: 'Low Priority' },\n    { value: 'medium', label: 'Medium Priority' },\n    { value: 'high', label: 'High Priority' }\n  ];\n\n  // Filter members based on role permissions\n  const assignableMembers = currentUser\n    ? getAssignableMembers(members, userRole, currentUser.id)\n    : members;\n\n  const memberOptions = assignableMembers.map(member => ({\n    value: member.id,\n    label: member.name,\n    description: member.role\n  }));\n\n  const rolePermissions = getRolePermissions(userRole);\n  const assignmentRestrictionMessage = getAssignmentRestrictionMessage(userRole);\n\n  const labelOptions = [\n    { value: 'bug', label: 'Bug', color: '#ef4444' },\n    { value: 'feature', label: 'Feature', color: '#3b82f6' },\n    { value: 'improvement', label: 'Improvement', color: '#10b981' },\n    { value: 'documentation', label: 'Documentation', color: '#f59e0b' },\n    { value: 'testing', label: 'Testing', color: '#8b5cf6' }\n  ].map(label => ({\n    value: label.value,\n    label: label.label\n  }));\n\n  const handleInputChange = (field, value) => {\n    // Validate task assignments in real-time\n    if (field === 'assignedTo' && currentUser) {\n      const invalidAssignments = value.filter(userId => {\n        const validationError = validateTaskAssignment(userRole, userId, currentUser.id);\n        return validationError !== null;\n      });\n\n      if (invalidAssignments.length > 0) {\n        const error = validateTaskAssignment(userRole, invalidAssignments[0], currentUser.id);\n        displayError(error);\n        return; // Don't update the field if assignment is invalid\n      }\n    }\n\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleAISuggestion = async () => {\n    if (!formData.title.trim()) {\n      alert(\"Please enter a card title first.\");\n      return;\n    }\n\n    setIsGeneratingChecklist(true);\n\n    try {\n      // Generate AI checklist\n      const checklistResult = await generateAIChecklist(\n        formData.title,\n        formData.description,\n        formData.priority,\n        'general' // Could be determined from project context\n      );\n\n      if (checklistResult.success) {\n        // Enhanced AI suggestions with checklist\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n            .toISOString()\n            .split('T')[0],\n          checklist: checklistResult.items\n        };\n\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate,\n          checklist: mockAI.checklist\n        }));\n\n        setAiChecklistGenerated(true);\n      } else {\n        const aiError = handleAIError(new Error(checklistResult.error), 'checklist generation');\n        displayError(aiError);\n\n        // Fallback to basic AI suggestion\n        const mockAI = {\n          description: formData.description || `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,\n          labels: ['feature', 'testing'],\n          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n            .toISOString()\n            .split('T')[0]\n        };\n\n        setFormData(prev => ({\n          ...prev,\n          description: mockAI.description,\n          labels: mockAI.labels,\n          dueDate: mockAI.dueDate\n        }));\n      }\n    } catch (error) {\n      const aiError = handleAIError(error, 'AI suggestion');\n      displayError(aiError);\n    } finally {\n      setIsGeneratingChecklist(false);\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Card title is required';\n    }\n\n    if (formData.title.length > 100) {\n      newErrors.title = 'Title must be less than 100 characters';\n    }\n\n    if (formData.description.length > 500) {\n      newErrors.description = 'Description must be less than 500 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    const newCard = {\n      id: Date.now().toString(),\n      columnId, // For frontend use\n      column_id: columnId, // For backend API\n      title: formData.title.trim(),\n      description: formData.description.trim(),\n      priority: formData.priority,\n      position: 0, // Add position for backend\n      assignedTo: formData.assignedTo,\n      assigned_to: formData.assignedTo, // For backend API\n      dueDate: formData.dueDate || null,\n      due_date: formData.dueDate || null, // For backend API\n      labels: formData.labels.map(labelValue => {\n        const labelData = [\n          { value: 'bug', label: 'Bug', color: '#ef4444' },\n          { value: 'feature', label: 'Feature', color: '#3b82f6' },\n          { value: 'improvement', label: 'Improvement', color: '#10b981' },\n          { value: 'documentation', label: 'Documentation', color: '#f59e0b' },\n          { value: 'testing', label: 'Testing', color: '#8b5cf6' }\n        ].find(l => l.value === labelValue);\n        return {\n          id: labelValue,\n          name: labelData.label,\n          color: labelData.color\n        };\n      }),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      checklist: formData.checklist || [],\n      comments: [],\n      attachments: []\n    };\n\n    onSave(newCard);\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setFormData({\n      title: '',\n      description: '',\n      priority: 'medium',\n      assignedTo: [],\n      dueDate: '',\n      labels: [],\n      checklist: []\n    });\n    setErrors({});\n    setAiChecklistGenerated(false);\n    setIsGeneratingChecklist(false);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n        onClick={handleClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-surface rounded-lg shadow-focused w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-border\">\n          <h2 className=\"text-lg font-semibold text-text-primary\">Add New Card</h2>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleClose}\n          >\n            <Icon name=\"X\" size={20} />\n          </Button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n          <Input\n            label=\"Card Title\"\n            type=\"text\"\n            placeholder=\"Enter card title...\"\n            value={formData.title}\n            onChange={(e) => handleInputChange('title', e.target.value)}\n            error={errors.title}\n            required\n          />\n\n          {/* ✨ AI Suggestion Button */}\n          <div className=\"flex justify-end -mt-2 mb-2\">\n            <button\n              type=\"button\"\n              onClick={handleAISuggestion}\n              disabled={isGeneratingChecklist}\n              className=\"text-sm text-primary underline hover:no-underline disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1\"\n            >\n              {isGeneratingChecklist ? (\n                <>\n                  <Icon name=\"Loader2\" size={14} className=\"animate-spin\" />\n                  <span>Generating...</span>\n                </>\n              ) : (\n                <>\n                  <Icon name=\"Zap\" size={14} />\n                  <span>Generate with AI</span>\n                </>\n              )}\n            </button>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-text-primary mb-2\">\n              Description\n            </label>\n            <textarea\n              placeholder=\"Enter card description...\"\n              value={formData.description}\n              onChange={(e) => handleInputChange('description', e.target.value)}\n              className=\"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none\"\n              rows={3}\n            />\n            {errors.description && (\n              <p className=\"mt-1 text-sm text-destructive\">{errors.description}</p>\n            )}\n          </div>\n\n          <Select\n            label=\"Priority\"\n            options={priorityOptions}\n            value={formData.priority}\n            onChange={(value) => handleInputChange('priority', value)}\n          />\n\n          <div>\n            <Select\n              label=\"Assign Members\"\n              options={memberOptions}\n              value={formData.assignedTo}\n              onChange={(value) => handleInputChange('assignedTo', value)}\n              multiple\n              searchable\n              placeholder={rolePermissions.canAssignTasksToOthers ? \"Select team members...\" : \"You can only assign to yourself\"}\n              disabled={!rolePermissions.canAssignTasksToSelf && !rolePermissions.canAssignTasksToOthers}\n            />\n            {!rolePermissions.canAssignTasksToOthers && (\n              <p className=\"mt-1 text-xs text-text-secondary\">\n                <Icon name=\"Info\" size={12} className=\"inline mr-1\" />\n                {assignmentRestrictionMessage}\n              </p>\n            )}\n          </div>\n\n          <Input\n            label=\"Due Date\"\n            type=\"date\"\n            value={formData.dueDate}\n            onChange={(e) => handleInputChange('dueDate', e.target.value)}\n          />\n\n          <Select\n            label=\"Labels\"\n            options={labelOptions}\n            value={formData.labels}\n            onChange={(value) => handleInputChange('labels', value)}\n            multiple\n            placeholder=\"Select labels...\"\n          />\n\n          {/* AI Generated Checklist Preview */}\n          {aiChecklistGenerated && formData.checklist.length > 0 && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <label className=\"block text-sm font-medium text-text-primary\">\n                  AI Generated Checklist\n                </label>\n                <div className=\"flex items-center space-x-1 text-xs text-text-secondary\">\n                  <Icon name=\"Zap\" size={12} className=\"text-primary\" />\n                  <span>{formData.checklist.length} items</span>\n                </div>\n              </div>\n              <div className=\"bg-muted/30 rounded-md p-3 max-h-32 overflow-y-auto\">\n                <div className=\"space-y-1\">\n                  {formData.checklist.slice(0, 5).map((item, index) => (\n                    <div key={item.id} className=\"flex items-start space-x-2 text-sm\">\n                      <div className=\"w-3 h-3 border border-border rounded-sm mt-0.5 flex-shrink-0\"></div>\n                      <span className=\"text-text-secondary line-clamp-1\">{item.text}</span>\n                    </div>\n                  ))}\n                  {formData.checklist.length > 5 && (\n                    <div className=\"text-xs text-text-secondary pl-5\">\n                      +{formData.checklist.length - 5} more items...\n                    </div>\n                  )}\n                </div>\n              </div>\n              <p className=\"text-xs text-text-secondary\">\n                <Icon name=\"Info\" size={12} className=\"inline mr-1\" />\n                You can edit these checklist items after creating the card\n              </p>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex items-center justify-end space-x-3 pt-4\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              variant=\"default\"\n            >\n              Create Card\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddCardModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,oBAAoB,EAAEC,+BAA+B,EAAEC,kBAAkB,QAAQ,gCAAgC;AAC1H,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,sBAAsB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,YAAY,QAAQ,8BAA8B;AAC/G,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC;IACvC6B,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC0C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,UAAU,GAAG,MAAMjC,WAAW,CAACkC,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAACC,IAAI,EAAE;UACxBX,cAAc,CAACQ,UAAU,CAACE,IAAI,CAACC,IAAI,CAAC;UACpCT,WAAW,CAACM,UAAU,CAACE,IAAI,CAACC,IAAI,CAACC,IAAI,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDX,WAAW,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC;IAED,IAAIpB,MAAM,EAAE;MACVyB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACzB,MAAM,CAAC,CAAC;EAEZ,MAAMiC,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC7C;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,CAC1C;;EAED;EACA,MAAMC,iBAAiB,GAAGnB,WAAW,GACjChC,oBAAoB,CAACmB,OAAO,EAAEe,QAAQ,EAAEF,WAAW,CAACoB,EAAE,CAAC,GACvDjC,OAAO;EAEX,MAAMkC,aAAa,GAAGF,iBAAiB,CAACG,GAAG,CAACC,MAAM,KAAK;IACrDN,KAAK,EAAEM,MAAM,CAACH,EAAE;IAChBF,KAAK,EAAEK,MAAM,CAACC,IAAI;IAClBhC,WAAW,EAAE+B,MAAM,CAACV;EACtB,CAAC,CAAC,CAAC;EAEH,MAAMY,eAAe,GAAGvD,kBAAkB,CAACgC,QAAQ,CAAC;EACpD,MAAMwB,4BAA4B,GAAGzD,+BAA+B,CAACiC,QAAQ,CAAC;EAE9E,MAAMyB,YAAY,GAAG,CACnB;IAAEV,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEU,KAAK,EAAE;EAAU,CAAC,EAChD;IAAEX,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEU,KAAK,EAAE;EAAU,CAAC,EACxD;IAAEX,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEU,KAAK,EAAE;EAAU,CAAC,EAChE;IAAEX,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEU,KAAK,EAAE;EAAU,CAAC,EACpE;IAAEX,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEU,KAAK,EAAE;EAAU,CAAC,CACzD,CAACN,GAAG,CAACJ,KAAK,KAAK;IACdD,KAAK,EAAEC,KAAK,CAACD,KAAK;IAClBC,KAAK,EAAEA,KAAK,CAACA;EACf,CAAC,CAAC,CAAC;EAEH,MAAMW,iBAAiB,GAAGA,CAACC,KAAK,EAAEb,KAAK,KAAK;IAC1C;IACA,IAAIa,KAAK,KAAK,YAAY,IAAI9B,WAAW,EAAE;MACzC,MAAM+B,kBAAkB,GAAGd,KAAK,CAACe,MAAM,CAACC,MAAM,IAAI;QAChD,MAAMC,eAAe,GAAG9D,sBAAsB,CAAC8B,QAAQ,EAAE+B,MAAM,EAAEjC,WAAW,CAACoB,EAAE,CAAC;QAChF,OAAOc,eAAe,KAAK,IAAI;MACjC,CAAC,CAAC;MAEF,IAAIH,kBAAkB,CAACI,MAAM,GAAG,CAAC,EAAE;QACjC,MAAMrB,KAAK,GAAG1C,sBAAsB,CAAC8B,QAAQ,EAAE6B,kBAAkB,CAAC,CAAC,CAAC,EAAE/B,WAAW,CAACoB,EAAE,CAAC;QACrF7C,YAAY,CAACuC,KAAK,CAAC;QACnB,OAAO,CAAC;MACV;IACF;IAEAxB,WAAW,CAAC8C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACN,KAAK,GAAGb;IAAM,CAAC,CAAC,CAAC;IAClD,IAAInB,MAAM,CAACgC,KAAK,CAAC,EAAE;MACjB/B,SAAS,CAACqC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACN,KAAK,GAAG;MAAK,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChD,QAAQ,CAACE,KAAK,CAAC+C,IAAI,CAAC,CAAC,EAAE;MAC1BC,KAAK,CAAC,kCAAkC,CAAC;MACzC;IACF;IAEAlC,wBAAwB,CAAC,IAAI,CAAC;IAE9B,IAAI;MACF;MACA,MAAMmC,eAAe,GAAG,MAAMrE,mBAAmB,CAC/CkB,QAAQ,CAACE,KAAK,EACdF,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,QAAQ,EACjB,SAAS,CAAC;MACZ,CAAC;MAED,IAAI+C,eAAe,CAACC,OAAO,EAAE;QAC3B;QACA,MAAMC,MAAM,GAAG;UACblD,WAAW,EAAEH,QAAQ,CAACG,WAAW,IAAI,uBAAuBH,QAAQ,CAACE,KAAK,sEAAsE;UAChJK,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UAC9BD,OAAO,EAAE,IAAIgD,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;UAAA,CACrDC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAChBjD,SAAS,EAAE2C,eAAe,CAACO;QAC7B,CAAC;QAEDzD,WAAW,CAAC8C,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP5C,WAAW,EAAEkD,MAAM,CAAClD,WAAW;UAC/BI,MAAM,EAAE8C,MAAM,CAAC9C,MAAM;UACrBD,OAAO,EAAE+C,MAAM,CAAC/C,OAAO;UACvBE,SAAS,EAAE6C,MAAM,CAAC7C;QACpB,CAAC,CAAC,CAAC;QAEHU,uBAAuB,CAAC,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL,MAAMyC,OAAO,GAAG3E,aAAa,CAAC,IAAI4E,KAAK,CAACT,eAAe,CAAC1B,KAAK,CAAC,EAAE,sBAAsB,CAAC;QACvFvC,YAAY,CAACyE,OAAO,CAAC;;QAErB;QACA,MAAMN,MAAM,GAAG;UACblD,WAAW,EAAEH,QAAQ,CAACG,WAAW,IAAI,uBAAuBH,QAAQ,CAACE,KAAK,sEAAsE;UAChJK,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;UAC9BD,OAAO,EAAE,IAAIgD,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CACpDC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACjB,CAAC;QAEDxD,WAAW,CAAC8C,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP5C,WAAW,EAAEkD,MAAM,CAAClD,WAAW;UAC/BI,MAAM,EAAE8C,MAAM,CAAC9C,MAAM;UACrBD,OAAO,EAAE+C,MAAM,CAAC/C;QAClB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACd,MAAMkC,OAAO,GAAG3E,aAAa,CAACyC,KAAK,EAAE,eAAe,CAAC;MACrDvC,YAAY,CAACyE,OAAO,CAAC;IACvB,CAAC,SAAS;MACR3C,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC9D,QAAQ,CAACE,KAAK,CAAC+C,IAAI,CAAC,CAAC,EAAE;MAC1Ba,SAAS,CAAC5D,KAAK,GAAG,wBAAwB;IAC5C;IAEA,IAAIF,QAAQ,CAACE,KAAK,CAAC4C,MAAM,GAAG,GAAG,EAAE;MAC/BgB,SAAS,CAAC5D,KAAK,GAAG,wCAAwC;IAC5D;IAEA,IAAIF,QAAQ,CAACG,WAAW,CAAC2C,MAAM,GAAG,GAAG,EAAE;MACrCgB,SAAS,CAAC3D,WAAW,GAAG,8CAA8C;IACxE;IAEAO,SAAS,CAACoD,SAAS,CAAC;IACpB,OAAOC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAAChB,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,MAAMO,OAAO,GAAG;MACdrC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC;MACzBxE,QAAQ;MAAE;MACVyE,SAAS,EAAEzE,QAAQ;MAAE;MACrBK,KAAK,EAAEF,QAAQ,CAACE,KAAK,CAAC+C,IAAI,CAAC,CAAC;MAC5B9C,WAAW,EAAEH,QAAQ,CAACG,WAAW,CAAC8C,IAAI,CAAC,CAAC;MACxC7C,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;MAC3BmE,QAAQ,EAAE,CAAC;MAAE;MACblE,UAAU,EAAEL,QAAQ,CAACK,UAAU;MAC/BmE,WAAW,EAAExE,QAAQ,CAACK,UAAU;MAAE;MAClCC,OAAO,EAAEN,QAAQ,CAACM,OAAO,IAAI,IAAI;MACjCmE,QAAQ,EAAEzE,QAAQ,CAACM,OAAO,IAAI,IAAI;MAAE;MACpCC,MAAM,EAAEP,QAAQ,CAACO,MAAM,CAAC0B,GAAG,CAACyC,UAAU,IAAI;QACxC,MAAMC,SAAS,GAAG,CAChB;UAAE/C,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE,KAAK;UAAEU,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEX,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEU,KAAK,EAAE;QAAU,CAAC,EACxD;UAAEX,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE,aAAa;UAAEU,KAAK,EAAE;QAAU,CAAC,EAChE;UAAEX,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAE,eAAe;UAAEU,KAAK,EAAE;QAAU,CAAC,EACpE;UAAEX,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE,SAAS;UAAEU,KAAK,EAAE;QAAU,CAAC,CACzD,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,KAAK,KAAK8C,UAAU,CAAC;QACnC,OAAO;UACL3C,EAAE,EAAE2C,UAAU;UACdvC,IAAI,EAAEwC,SAAS,CAAC9C,KAAK;UACrBU,KAAK,EAAEoC,SAAS,CAACpC;QACnB,CAAC;MACH,CAAC,CAAC;MACFuC,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnCuB,SAAS,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnChD,SAAS,EAAER,QAAQ,CAACQ,SAAS,IAAI,EAAE;MACnCwE,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;IACf,CAAC;IAEDrF,MAAM,CAACwE,OAAO,CAAC;IACfc,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMA,WAAW,GAAGA,CAAA,KAAM;IACxBjF,WAAW,CAAC;MACVC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbQ,uBAAuB,CAAC,KAAK,CAAC;IAC9BF,wBAAwB,CAAC,KAAK,CAAC;IAC/BrB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK6F,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElE9F,OAAA;MACE6F,SAAS,EAAC,+CAA+C;MACzDE,OAAO,EAAEH;IAAY;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGFnG,OAAA;MAAK6F,SAAS,EAAC,iGAAiG;MAAAC,QAAA,gBAE9G9F,OAAA;QAAK6F,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E9F,OAAA;UAAI6F,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEnG,OAAA,CAACd,MAAM;UACLkH,OAAO,EAAC,OAAO;UACfC,IAAI,EAAC,MAAM;UACXN,OAAO,EAAEH,WAAY;UAAAE,QAAA,eAErB9F,OAAA,CAACf,IAAI;YAAC4D,IAAI,EAAC,GAAG;YAACwD,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNnG,OAAA;QAAMsG,QAAQ,EAAE3B,YAAa;QAACkB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBACrD9F,OAAA,CAACb,KAAK;UACJoD,KAAK,EAAC,YAAY;UAClBgE,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,qBAAqB;UACjClE,KAAK,EAAE5B,QAAQ,CAACE,KAAM;UACtB6F,QAAQ,EAAG7B,CAAC,IAAK1B,iBAAiB,CAAC,OAAO,EAAE0B,CAAC,CAAC8B,MAAM,CAACpE,KAAK,CAAE;UAC5DH,KAAK,EAAEhB,MAAM,CAACP,KAAM;UACpB+F,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGFnG,OAAA;UAAK6F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C9F,OAAA;YACEuG,IAAI,EAAC,QAAQ;YACbR,OAAO,EAAErC,kBAAmB;YAC5BkD,QAAQ,EAAEnF,qBAAsB;YAChCoE,SAAS,EAAC,+HAA+H;YAAAC,QAAA,EAExIrE,qBAAqB,gBACpBzB,OAAA,CAAAE,SAAA;cAAA4F,QAAA,gBACE9F,OAAA,CAACf,IAAI;gBAAC4D,IAAI,EAAC,SAAS;gBAACwD,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DnG,OAAA;gBAAA8F,QAAA,EAAM;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC1B,CAAC,gBAEHnG,OAAA,CAAAE,SAAA;cAAA4F,QAAA,gBACE9F,OAAA,CAACf,IAAI;gBAAC4D,IAAI,EAAC,KAAK;gBAACwD,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7BnG,OAAA;gBAAA8F,QAAA,EAAM;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC7B;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEpE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnG,OAAA;YACEwG,WAAW,EAAC,2BAA2B;YACvClE,KAAK,EAAE5B,QAAQ,CAACG,WAAY;YAC5B4F,QAAQ,EAAG7B,CAAC,IAAK1B,iBAAiB,CAAC,aAAa,EAAE0B,CAAC,CAAC8B,MAAM,CAACpE,KAAK,CAAE;YAClEuD,SAAS,EAAC,iHAAiH;YAC3HgB,IAAI,EAAE;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACDhF,MAAM,CAACN,WAAW,iBACjBb,OAAA;YAAG6F,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAE3E,MAAM,CAACN;UAAW;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnG,OAAA,CAACZ,MAAM;UACLmD,KAAK,EAAC,UAAU;UAChBuE,OAAO,EAAEzE,eAAgB;UACzBC,KAAK,EAAE5B,QAAQ,CAACI,QAAS;UACzB2F,QAAQ,EAAGnE,KAAK,IAAKY,iBAAiB,CAAC,UAAU,EAAEZ,KAAK;QAAE;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAEFnG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA,CAACZ,MAAM;YACLmD,KAAK,EAAC,gBAAgB;YACtBuE,OAAO,EAAEpE,aAAc;YACvBJ,KAAK,EAAE5B,QAAQ,CAACK,UAAW;YAC3B0F,QAAQ,EAAGnE,KAAK,IAAKY,iBAAiB,CAAC,YAAY,EAAEZ,KAAK,CAAE;YAC5DyE,QAAQ;YACRC,UAAU;YACVR,WAAW,EAAE1D,eAAe,CAACmE,sBAAsB,GAAG,wBAAwB,GAAG,iCAAkC;YACnHL,QAAQ,EAAE,CAAC9D,eAAe,CAACoE,oBAAoB,IAAI,CAACpE,eAAe,CAACmE;UAAuB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,EACD,CAACrD,eAAe,CAACmE,sBAAsB,iBACtCjH,OAAA;YAAG6F,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC7C9F,OAAA,CAACf,IAAI;cAAC4D,IAAI,EAAC,MAAM;cAACwD,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACrDpD,4BAA4B;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnG,OAAA,CAACb,KAAK;UACJoD,KAAK,EAAC,UAAU;UAChBgE,IAAI,EAAC,MAAM;UACXjE,KAAK,EAAE5B,QAAQ,CAACM,OAAQ;UACxByF,QAAQ,EAAG7B,CAAC,IAAK1B,iBAAiB,CAAC,SAAS,EAAE0B,CAAC,CAAC8B,MAAM,CAACpE,KAAK;QAAE;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEFnG,OAAA,CAACZ,MAAM;UACLmD,KAAK,EAAC,QAAQ;UACduE,OAAO,EAAE9D,YAAa;UACtBV,KAAK,EAAE5B,QAAQ,CAACO,MAAO;UACvBwF,QAAQ,EAAGnE,KAAK,IAAKY,iBAAiB,CAAC,QAAQ,EAAEZ,KAAK,CAAE;UACxDyE,QAAQ;UACRP,WAAW,EAAC;QAAkB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAGDxE,oBAAoB,IAAIjB,QAAQ,CAACQ,SAAS,CAACsC,MAAM,GAAG,CAAC,iBACpDxD,OAAA;UAAK6F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9F,OAAA;YAAK6F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9F,OAAA;cAAO6F,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE/D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnG,OAAA;cAAK6F,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtE9F,OAAA,CAACf,IAAI;gBAAC4D,IAAI,EAAC,KAAK;gBAACwD,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDnG,OAAA;gBAAA8F,QAAA,GAAOpF,QAAQ,CAACQ,SAAS,CAACsC,MAAM,EAAC,QAAM;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnG,OAAA;YAAK6F,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClE9F,OAAA;cAAK6F,SAAS,EAAC,WAAW;cAAAC,QAAA,GACvBpF,QAAQ,CAACQ,SAAS,CAACiG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACxE,GAAG,CAAC,CAACyE,IAAI,EAAEC,KAAK,kBAC9CrH,OAAA;gBAAmB6F,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBAC/D9F,OAAA;kBAAK6F,SAAS,EAAC;gBAA8D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpFnG,OAAA;kBAAM6F,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEsB,IAAI,CAACE;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAF7DiB,IAAI,CAAC3E,EAAE;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGZ,CACN,CAAC,EACDzF,QAAQ,CAACQ,SAAS,CAACsC,MAAM,GAAG,CAAC,iBAC5BxD,OAAA;gBAAK6F,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,GAC/C,EAACpF,QAAQ,CAACQ,SAAS,CAACsC,MAAM,GAAG,CAAC,EAAC,gBAClC;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnG,OAAA;YAAG6F,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACxC9F,OAAA,CAACf,IAAI;cAAC4D,IAAI,EAAC,MAAM;cAACwD,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8DAExD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAGDnG,OAAA;UAAK6F,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3D9F,OAAA,CAACd,MAAM;YACLqH,IAAI,EAAC,QAAQ;YACbH,OAAO,EAAC,SAAS;YACjBL,OAAO,EAAEH,WAAY;YAAAE,QAAA,EACtB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnG,OAAA,CAACd,MAAM;YACLqH,IAAI,EAAC,QAAQ;YACbH,OAAO,EAAC,SAAS;YAAAN,QAAA,EAClB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1F,EAAA,CAnZIN,YAAY;AAAAoH,EAAA,GAAZpH,YAAY;AAqZlB,eAAeA,YAAY;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}