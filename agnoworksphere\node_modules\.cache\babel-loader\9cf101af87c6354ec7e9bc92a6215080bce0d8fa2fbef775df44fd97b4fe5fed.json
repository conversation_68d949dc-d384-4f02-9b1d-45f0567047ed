{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\card-details\\\\index.jsx\",\n  _s = $RefreshSig$();\n// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Icon from '../../components/AppIcon';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CardDetails = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Track pending changes\n  const [pendingChanges, setPendingChanges] = useState({});\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Warn user about unsaved changes when leaving\n  useEffect(() => {\n    const handleBeforeUnload = e => {\n      if (hasUnsavedChanges) {\n        e.preventDefault();\n        e.returnValue = '';\n      }\n    };\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload);\n  }, [hasUnsavedChanges]);\n\n  // Load card data from location state or API\n  useEffect(() => {\n    const loadCardData = async () => {\n      var _location$state;\n      setIsLoading(true);\n\n      // First try to get card data from location state (when navigated from kanban board)\n      if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.card) {\n        console.log('Loading card from location state:', location.state.card);\n\n        // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n        const normalizedCardData = {\n          ...location.state.card,\n          checklist: location.state.card.checklist_items || location.state.card.checklist || []\n        };\n\n        // Remove checklist_items to avoid confusion\n        if (normalizedCardData.checklist_items) {\n          delete normalizedCardData.checklist_items;\n        }\n        setCardData(normalizedCardData);\n        setIsLoading(false);\n        return;\n      }\n\n      // If no state data, try to load from API\n      if (cardId) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardId);\n          console.log('Loading card from API:', result);\n          if (result.data) {\n            // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n            setCardData(normalizedCardData);\n            setIsLoading(false);\n            return;\n          }\n        } catch (error) {\n          console.error('Error loading card from API:', error);\n        }\n      }\n\n      // Fallback: use mock data if no card found\n      console.log('No card data found, using fallback data');\n      setCardData({\n        id: cardId || '1',\n        title: 'Card Not Found',\n        description: 'This card could not be loaded. Please return to the board and try again.',\n        columnTitle: 'Unknown',\n        assignedMembers: [],\n        dueDate: null,\n        labels: [],\n        checklist: [],\n        completed: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      });\n      setIsLoading(false);\n    };\n    loadCardData();\n  }, [cardId, location.state]);\n\n  // Helper function to track changes\n  const trackChange = (field, value) => {\n    const updatedData = {\n      [field]: value,\n      updatedAt: new Date().toISOString()\n    };\n\n    // Update local card data immediately for UI\n    setCardData(prev => ({\n      ...prev,\n      ...updatedData\n    }));\n\n    // Track pending changes\n    setPendingChanges(prev => ({\n      ...prev,\n      ...updatedData\n    }));\n    setHasUnsavedChanges(true);\n  };\n\n  // Helper function to check if a field has pending changes\n  const hasFieldChanged = field => {\n    return pendingChanges.hasOwnProperty(field);\n  };\n  const handleTitleChange = newTitle => {\n    trackChange('title', newTitle);\n  };\n  const handleDescriptionChange = newDescription => {\n    trackChange('description', newDescription);\n  };\n\n  // Helper function to update card via API\n  const updateCardInAPI = async updatedData => {\n    try {\n      if (!(cardData !== null && cardData !== void 0 && cardData.id)) return;\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Handle checklist separately if it's being updated\n      if (updatedData.checklist) {\n        await updateChecklistInAPI(updatedData.checklist);\n        // Remove checklist from card update data since it's handled separately\n        const {\n          checklist,\n          ...cardUpdateData\n        } = updatedData;\n        if (Object.keys(cardUpdateData).length > 0) {\n          await apiService.cards.update(cardData.id, cardUpdateData);\n        }\n      } else {\n        await apiService.cards.update(cardData.id, updatedData);\n      }\n      console.log('Card updated via API:', updatedData);\n    } catch (error) {\n      console.error('Error updating card via API:', error);\n    }\n  };\n\n  // Helper function to check if a string is a valid UUID\n  const isValidUUID = str => {\n    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n    return uuidRegex.test(str);\n  };\n\n  // Helper function to update checklist via dedicated API\n  const updateChecklistInAPI = async checklistItems => {\n    try {\n      if (!(cardData !== null && cardData !== void 0 && cardData.id) || !checklistItems) return;\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Get current checklist items from the card\n      const currentItems = cardData.checklist || [];\n\n      // Create a map of current items by ID for easy lookup\n      // Only include items with real backend IDs (not temporary ones)\n      const currentItemsMap = new Map();\n      currentItems.forEach(item => {\n        if (item.id) {\n          const itemId = item.id.toString();\n          const isTemporary = !itemId || itemId === '' || itemId.startsWith('temp-') || itemId.startsWith('ai-') || !isValidUUID(itemId);\n\n          // Only add items with real backend IDs to the map\n          if (!isTemporary) {\n            currentItemsMap.set(item.id, item);\n          }\n        }\n      });\n\n      // Create a map of new items by ID\n      const newItemsMap = new Map();\n      const itemsToCreate = [];\n      checklistItems.forEach((item, index) => {\n        const itemId = item.id ? item.id.toString() : '';\n\n        // Check if this is a temporary ID (frontend-generated) vs a real backend UUID\n        const isTemporary = !itemId || itemId === '' || itemId.startsWith('temp-') || itemId.startsWith('ai-') || !isValidUUID(itemId); // Check if it's a valid UUID\n\n        if (item.id && !isTemporary) {\n          // Existing item with real backend ID (UUID)\n          newItemsMap.set(item.id, {\n            ...item,\n            position: item.position !== undefined ? item.position : index\n          });\n        } else {\n          // New item to create (temporary ID or no ID)\n          itemsToCreate.push({\n            text: item.text || item.title || '',\n            completed: item.completed || false,\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          });\n        }\n      });\n\n      // Delete items that are no longer present\n      // Only try to delete items that actually exist in the backend (have real IDs)\n      for (const [itemId] of currentItemsMap) {\n        if (!newItemsMap.has(itemId)) {\n          try {\n            // Double-check that this is a valid UUID before attempting deletion\n            const itemIdStr = itemId.toString();\n            if (isValidUUID(itemIdStr)) {\n              await apiService.checklist.deleteItem(itemId);\n              console.log('Deleted checklist item:', itemId);\n            } else {\n              console.log('Skipping deletion of non-UUID item:', itemId);\n            }\n          } catch (error) {\n            console.warn('Failed to delete checklist item:', itemId, error);\n          }\n        }\n      }\n\n      // Update existing items that have changed\n      for (const [itemId, newItem] of newItemsMap) {\n        const currentItem = currentItemsMap.get(itemId);\n        if (currentItem) {\n          // Check if the item has changed\n          const hasChanged = currentItem.text !== newItem.text || currentItem.completed !== newItem.completed || currentItem.position !== newItem.position;\n          if (hasChanged) {\n            try {\n              // Double-check that this is a valid UUID before attempting update\n              const itemIdStr = itemId.toString();\n              if (isValidUUID(itemIdStr)) {\n                await apiService.checklist.updateItem(itemId, {\n                  text: newItem.text,\n                  completed: newItem.completed,\n                  position: newItem.position\n                });\n                console.log('Updated checklist item:', itemId);\n              } else {\n                console.log('Skipping update of non-UUID item (will be created instead):', itemId);\n              }\n            } catch (error) {\n              console.warn('Failed to update checklist item:', itemId, error);\n            }\n          }\n        }\n      }\n\n      // Create new items\n      if (itemsToCreate.length > 0) {\n        try {\n          await apiService.checklist.createBulk(cardData.id, {\n            items: itemsToCreate\n          });\n          console.log('Created new checklist items:', itemsToCreate.length);\n        } catch (error) {\n          console.error('Failed to create new checklist items:', error);\n        }\n      }\n      console.log('Checklist updated via API successfully');\n    } catch (error) {\n      console.error('Error updating checklist via API:', error);\n    }\n  };\n  const handleMembersChange = newMembers => {\n    trackChange('assignedMembers', newMembers);\n  };\n  const handleDueDateChange = newDueDate => {\n    trackChange('dueDate', newDueDate);\n  };\n  const handleLabelsChange = newLabels => {\n    trackChange('labels', newLabels);\n  };\n  const handleChecklistChange = newChecklist => {\n    trackChange('checklist', newChecklist);\n  };\n\n  // Save all pending changes\n  const handleSaveChanges = async () => {\n    if (!hasUnsavedChanges || !(cardData !== null && cardData !== void 0 && cardData.id)) return;\n    setIsSaving(true);\n    try {\n      await updateCardInAPI(pendingChanges);\n\n      // Reload card data to get the latest state from the backend\n      if (cardData !== null && cardData !== void 0 && cardData.id) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardData.id);\n          if (result.data) {\n            // Normalize the card data\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n            setCardData(normalizedCardData);\n          }\n        } catch (error) {\n          console.error('Error reloading card data after save:', error);\n        }\n      }\n\n      // Clear pending changes\n      setPendingChanges({});\n      setHasUnsavedChanges(false);\n      console.log('All changes saved successfully');\n    } catch (error) {\n      console.error('Error saving changes:', error);\n      // You could show a toast notification here\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Discard all pending changes\n  const handleDiscardChanges = () => {\n    if (!hasUnsavedChanges) return;\n\n    // Revert cardData to original state (you might want to store original data)\n    // For now, we'll just clear pending changes and reload\n    setPendingChanges({});\n    setHasUnsavedChanges(false);\n\n    // Reload card data to revert changes\n    window.location.reload();\n  };\n  const handleAddComment = comment => {\n    console.log('New comment added:', comment);\n  };\n\n  // Keyboard shortcut for saving\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        if (hasUnsavedChanges) {\n          handleSaveChanges();\n        }\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [hasUnsavedChanges, handleSaveChanges]);\n  const handleClose = () => {\n    if (hasUnsavedChanges) {\n      const confirmLeave = window.confirm('You have unsaved changes. Are you sure you want to leave without saving?');\n      if (!confirmLeave) return;\n    }\n    navigate('/kanban-board');\n  };\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n    loadUserData();\n  }, []);\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Loading card...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary\",\n            children: \"Please wait while we load the card details.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state if card not found\n  if (!cardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Card not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary mb-4\",\n            children: \"The requested card could not be found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"text-primary hover:underline\",\n            children: \"Return to Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-1000 pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-center min-h-full p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-5xl bg-surface rounded-xl shadow-2xl my-8 max-h-screen overflow-hidden border border-border/20\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            card: cardData,\n            onTitleChange: handleTitleChange,\n            onClose: handleClose,\n            onDelete: handleDelete,\n            canEdit: canEdit,\n            canDelete: canDelete,\n            hasChanged: hasFieldChanged('title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), hasUnsavedChanges && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-warning/10 border-b border-warning/20 px-8 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"AlertCircle\",\n                  size: 20,\n                  className: \"text-warning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-warning\",\n                    children: \"You have unsaved changes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-warning/80\",\n                    children: \"Save your changes to avoid losing them\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleDiscardChanges,\n                  disabled: isSaving,\n                  className: \"px-4 py-2 text-sm font-medium text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-muted transition-colors disabled:opacity-50\",\n                  children: \"Discard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSaveChanges,\n                  disabled: isSaving,\n                  className: \"px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50 flex items-center space-x-2\",\n                  children: isSaving ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Loader2\",\n                      size: 16,\n                      className: \"animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Saving...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Save\",\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Save Changes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row overflow-y-auto max-h-[calc(100vh-8rem)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 lg:w-3/5 p-8 space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(CardDescription, {\n                card: cardData,\n                onDescriptionChange: handleDescriptionChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('description')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ChecklistManager, {\n                card: cardData,\n                onChecklistChange: handleChecklistChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('checklist')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ActivityTimeline, {\n                card: cardData,\n                onAddComment: handleAddComment,\n                canComment: canComment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:w-2/5 p-8 bg-gradient-to-b from-muted/20 to-muted/40 border-l border-border/50 space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(MemberAssignment, {\n                card: cardData,\n                onMembersChange: handleMembersChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('assignedMembers')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DueDatePicker, {\n                card: cardData,\n                onDueDateChange: handleDueDateChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('dueDate')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(LabelManager, {\n                card: cardData,\n                onLabelsChange: handleLabelsChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('labels')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-text-primary flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Info\",\n                    size: 16,\n                    className: \"text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 21\n                  }, this), \"Card Information\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center py-2 border-b border-border/20\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary font-medium\",\n                      children: \"Created:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.createdAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center py-2 border-b border-border/20\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary font-medium\",\n                      children: \"Last updated:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.updatedAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center py-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary font-medium\",\n                      children: \"Card ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary font-mono text-xs bg-muted px-2 py-1 rounded\",\n                      children: [\"#\", cardData.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this), (canEdit || canDelete) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-text-primary flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Settings\",\n                    size: 16,\n                    className: \"text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this), \"Actions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Archive\",\n                      size: 16,\n                      className: \"text-text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 661,\n                      columnNumber: 27\n                    }, this), \"Archive Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 25\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Copy\",\n                      size: 16,\n                      className: \"text-text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 27\n                    }, this), \"Copy Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 25\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Move\",\n                      size: 16,\n                      className: \"text-text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 27\n                    }, this), \"Move Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 25\n                  }, this), canDelete && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleDelete,\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors border border-transparent hover:border-destructive/20\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Trash2\",\n                      size: 16,\n                      className: \"text-destructive\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 27\n                    }, this), \"Delete Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 515,\n    columnNumber: 5\n  }, this);\n};\n_s(CardDetails, \"Y5Pu+OHCBXJD4H3CKdNy9UL7vv0=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams];\n});\n_c = CardDetails;\nexport default CardDetails;\nvar _c;\n$RefreshReg$(_c, \"CardDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSearchParams", "useLocation", "RoleBasedHeader", "Icon", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "MemberAssignment", "DueDatePicker", "LabelManager", "ChecklistManager", "ActivityTimeline", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CardDetails", "_s", "navigate", "location", "searchParams", "cardId", "get", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "canEdit", "includes", "toLowerCase", "canDelete", "canComment", "cardData", "setCardData", "isLoading", "setIsLoading", "pendingChanges", "setPendingChanges", "hasUnsavedChanges", "setHasUnsavedChanges", "isSaving", "setIsSaving", "handleBeforeUnload", "e", "preventDefault", "returnValue", "window", "addEventListener", "removeEventListener", "loadCardData", "_location$state", "state", "card", "console", "log", "normalizedCardData", "checklist", "checklist_items", "apiService", "default", "result", "cards", "getById", "data", "error", "id", "title", "description", "columnTitle", "assignedMembers", "dueDate", "labels", "completed", "createdAt", "Date", "toISOString", "updatedAt", "trackChange", "field", "value", "updatedData", "prev", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "handleTitleChange", "newTitle", "handleDescriptionChange", "newDescription", "updateCardInAPI", "updateChecklistInAPI", "cardUpdateData", "Object", "keys", "length", "update", "isValidUUID", "str", "uuidRegex", "test", "checklistItems", "currentItems", "currentItemsMap", "Map", "for<PERSON>ach", "item", "itemId", "toString", "isTemporary", "startsWith", "set", "newItemsMap", "itemsToCreate", "index", "position", "undefined", "push", "text", "ai_generated", "aiGenerated", "confidence", "metadata", "has", "itemIdStr", "deleteItem", "warn", "newItem", "currentItem", "has<PERSON><PERSON>ed", "updateItem", "createBulk", "items", "handleMembersChange", "newMembers", "handleDueDateChange", "newDueDate", "handleLabelsChange", "<PERSON><PERSON><PERSON><PERSON>", "handleChecklistChange", "newChecklist", "handleSaveChanges", "handleDiscardChanges", "reload", "handleAddComment", "comment", "handleKeyDown", "ctrl<PERSON>ey", "metaKey", "key", "document", "handleClose", "confirmLeave", "confirm", "handleDelete", "loadUserData", "userResponse", "getCurrentUser", "orgResponse", "getCurrentOrganization", "user", "role", "organization", "body", "style", "overflow", "className", "children", "name", "firstName", "lastName", "email", "avatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onTitleChange", "onClose", "onDelete", "size", "disabled", "onDescriptionChange", "onChecklistChange", "onAddComment", "onMembersChange", "onDueDateChange", "onLabelsChange", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/card-details/index.jsx"], "sourcesContent": ["// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Icon from '../../components/AppIcon';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\n\nconst CardDetails = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Track pending changes\n  const [pendingChanges, setPendingChanges] = useState({});\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n\n\n\n  // Warn user about unsaved changes when leaving\n  useEffect(() => {\n    const handleBeforeUnload = (e) => {\n      if (hasUnsavedChanges) {\n        e.preventDefault();\n        e.returnValue = '';\n      }\n    };\n\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload);\n  }, [hasUnsavedChanges]);\n\n  // Load card data from location state or API\n  useEffect(() => {\n    const loadCardData = async () => {\n      setIsLoading(true);\n\n      // First try to get card data from location state (when navigated from kanban board)\n      if (location.state?.card) {\n        console.log('Loading card from location state:', location.state.card);\n\n        // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n        const normalizedCardData = {\n          ...location.state.card,\n          checklist: location.state.card.checklist_items || location.state.card.checklist || []\n        };\n\n        // Remove checklist_items to avoid confusion\n        if (normalizedCardData.checklist_items) {\n          delete normalizedCardData.checklist_items;\n        }\n\n        setCardData(normalizedCardData);\n        setIsLoading(false);\n        return;\n      }\n\n      // If no state data, try to load from API\n      if (cardId) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardId);\n          console.log('Loading card from API:', result);\n          if (result.data) {\n            // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n\n            setCardData(normalizedCardData);\n            setIsLoading(false);\n            return;\n          }\n        } catch (error) {\n          console.error('Error loading card from API:', error);\n        }\n      }\n\n      // Fallback: use mock data if no card found\n      console.log('No card data found, using fallback data');\n      setCardData({\n        id: cardId || '1',\n        title: 'Card Not Found',\n        description: 'This card could not be loaded. Please return to the board and try again.',\n        columnTitle: 'Unknown',\n        assignedMembers: [],\n        dueDate: null,\n        labels: [],\n        checklist: [],\n        completed: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      });\n      setIsLoading(false);\n    };\n\n    loadCardData();\n  }, [cardId, location.state]);\n\n  // Helper function to track changes\n  const trackChange = (field, value) => {\n    const updatedData = { [field]: value, updatedAt: new Date().toISOString() };\n\n    // Update local card data immediately for UI\n    setCardData(prev => ({ ...prev, ...updatedData }));\n\n    // Track pending changes\n    setPendingChanges(prev => ({ ...prev, ...updatedData }));\n    setHasUnsavedChanges(true);\n  };\n\n  // Helper function to check if a field has pending changes\n  const hasFieldChanged = (field) => {\n    return pendingChanges.hasOwnProperty(field);\n  };\n\n  const handleTitleChange = (newTitle) => {\n    trackChange('title', newTitle);\n  };\n\n  const handleDescriptionChange = (newDescription) => {\n    trackChange('description', newDescription);\n  };\n\n  // Helper function to update card via API\n  const updateCardInAPI = async (updatedData) => {\n    try {\n      if (!cardData?.id) return;\n\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Handle checklist separately if it's being updated\n      if (updatedData.checklist) {\n        await updateChecklistInAPI(updatedData.checklist);\n        // Remove checklist from card update data since it's handled separately\n        const { checklist, ...cardUpdateData } = updatedData;\n        if (Object.keys(cardUpdateData).length > 0) {\n          await apiService.cards.update(cardData.id, cardUpdateData);\n        }\n      } else {\n        await apiService.cards.update(cardData.id, updatedData);\n      }\n\n      console.log('Card updated via API:', updatedData);\n    } catch (error) {\n      console.error('Error updating card via API:', error);\n    }\n  };\n\n  // Helper function to check if a string is a valid UUID\n  const isValidUUID = (str) => {\n    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n    return uuidRegex.test(str);\n  };\n\n  // Helper function to update checklist via dedicated API\n  const updateChecklistInAPI = async (checklistItems) => {\n    try {\n      if (!cardData?.id || !checklistItems) return;\n\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Get current checklist items from the card\n      const currentItems = cardData.checklist || [];\n\n      // Create a map of current items by ID for easy lookup\n      // Only include items with real backend IDs (not temporary ones)\n      const currentItemsMap = new Map();\n      currentItems.forEach(item => {\n        if (item.id) {\n          const itemId = item.id.toString();\n          const isTemporary = !itemId ||\n                             itemId === '' ||\n                             itemId.startsWith('temp-') ||\n                             itemId.startsWith('ai-') ||\n                             !isValidUUID(itemId);\n\n          // Only add items with real backend IDs to the map\n          if (!isTemporary) {\n            currentItemsMap.set(item.id, item);\n          }\n        }\n      });\n\n      // Create a map of new items by ID\n      const newItemsMap = new Map();\n      const itemsToCreate = [];\n\n      checklistItems.forEach((item, index) => {\n        const itemId = item.id ? item.id.toString() : '';\n\n        // Check if this is a temporary ID (frontend-generated) vs a real backend UUID\n        const isTemporary = !itemId ||\n                           itemId === '' ||\n                           itemId.startsWith('temp-') ||\n                           itemId.startsWith('ai-') ||\n                           !isValidUUID(itemId); // Check if it's a valid UUID\n\n        if (item.id && !isTemporary) {\n          // Existing item with real backend ID (UUID)\n          newItemsMap.set(item.id, {\n            ...item,\n            position: item.position !== undefined ? item.position : index\n          });\n        } else {\n          // New item to create (temporary ID or no ID)\n          itemsToCreate.push({\n            text: item.text || item.title || '',\n            completed: item.completed || false,\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          });\n        }\n      });\n\n      // Delete items that are no longer present\n      // Only try to delete items that actually exist in the backend (have real IDs)\n      for (const [itemId] of currentItemsMap) {\n        if (!newItemsMap.has(itemId)) {\n          try {\n            // Double-check that this is a valid UUID before attempting deletion\n            const itemIdStr = itemId.toString();\n            if (isValidUUID(itemIdStr)) {\n              await apiService.checklist.deleteItem(itemId);\n              console.log('Deleted checklist item:', itemId);\n            } else {\n              console.log('Skipping deletion of non-UUID item:', itemId);\n            }\n          } catch (error) {\n            console.warn('Failed to delete checklist item:', itemId, error);\n          }\n        }\n      }\n\n      // Update existing items that have changed\n      for (const [itemId, newItem] of newItemsMap) {\n        const currentItem = currentItemsMap.get(itemId);\n        if (currentItem) {\n          // Check if the item has changed\n          const hasChanged =\n            currentItem.text !== newItem.text ||\n            currentItem.completed !== newItem.completed ||\n            currentItem.position !== newItem.position;\n\n          if (hasChanged) {\n            try {\n              // Double-check that this is a valid UUID before attempting update\n              const itemIdStr = itemId.toString();\n              if (isValidUUID(itemIdStr)) {\n                await apiService.checklist.updateItem(itemId, {\n                  text: newItem.text,\n                  completed: newItem.completed,\n                  position: newItem.position\n                });\n                console.log('Updated checklist item:', itemId);\n              } else {\n                console.log('Skipping update of non-UUID item (will be created instead):', itemId);\n              }\n            } catch (error) {\n              console.warn('Failed to update checklist item:', itemId, error);\n            }\n          }\n        }\n      }\n\n      // Create new items\n      if (itemsToCreate.length > 0) {\n        try {\n          await apiService.checklist.createBulk(cardData.id, { items: itemsToCreate });\n          console.log('Created new checklist items:', itemsToCreate.length);\n        } catch (error) {\n          console.error('Failed to create new checklist items:', error);\n        }\n      }\n\n      console.log('Checklist updated via API successfully');\n    } catch (error) {\n      console.error('Error updating checklist via API:', error);\n    }\n  };\n\n  const handleMembersChange = (newMembers) => {\n    trackChange('assignedMembers', newMembers);\n  };\n\n  const handleDueDateChange = (newDueDate) => {\n    trackChange('dueDate', newDueDate);\n  };\n\n  const handleLabelsChange = (newLabels) => {\n    trackChange('labels', newLabels);\n  };\n\n  const handleChecklistChange = (newChecklist) => {\n    trackChange('checklist', newChecklist);\n  };\n\n  // Save all pending changes\n  const handleSaveChanges = async () => {\n    if (!hasUnsavedChanges || !cardData?.id) return;\n\n    setIsSaving(true);\n    try {\n      await updateCardInAPI(pendingChanges);\n\n      // Reload card data to get the latest state from the backend\n      if (cardData?.id) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardData.id);\n          if (result.data) {\n            // Normalize the card data\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n\n            setCardData(normalizedCardData);\n          }\n        } catch (error) {\n          console.error('Error reloading card data after save:', error);\n        }\n      }\n\n      // Clear pending changes\n      setPendingChanges({});\n      setHasUnsavedChanges(false);\n\n      console.log('All changes saved successfully');\n    } catch (error) {\n      console.error('Error saving changes:', error);\n      // You could show a toast notification here\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Discard all pending changes\n  const handleDiscardChanges = () => {\n    if (!hasUnsavedChanges) return;\n\n    // Revert cardData to original state (you might want to store original data)\n    // For now, we'll just clear pending changes and reload\n    setPendingChanges({});\n    setHasUnsavedChanges(false);\n\n    // Reload card data to revert changes\n    window.location.reload();\n  };\n\n  const handleAddComment = (comment) => {\n    console.log('New comment added:', comment);\n  };\n\n  // Keyboard shortcut for saving\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        if (hasUnsavedChanges) {\n          handleSaveChanges();\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [hasUnsavedChanges, handleSaveChanges]);\n\n  const handleClose = () => {\n    if (hasUnsavedChanges) {\n      const confirmLeave = window.confirm(\n        'You have unsaved changes. Are you sure you want to leave without saving?'\n      );\n      if (!confirmLeave) return;\n    }\n    navigate('/kanban-board');\n  };\n\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Loading card...</div>\n            <div className=\"text-text-secondary\">Please wait while we load the card details.</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state if card not found\n  if (!cardData) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Card not found</div>\n            <div className=\"text-text-secondary mb-4\">The requested card could not be found.</div>\n            <button onClick={handleClose} className=\"text-primary hover:underline\">\n              Return to Board\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Modal Overlay */}\n      <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm z-1000 pt-16\">\n        <div className=\"flex items-start justify-center min-h-full p-4 overflow-y-auto\">\n          {/* Modal Content */}\n          <div className=\"w-full max-w-5xl bg-surface rounded-xl shadow-2xl my-8 max-h-screen overflow-hidden border border-border/20\">\n            {/* Card Header */}\n            <CardHeader\n              card={cardData}\n              onTitleChange={handleTitleChange}\n              onClose={handleClose}\n              onDelete={handleDelete}\n              canEdit={canEdit}\n              canDelete={canDelete}\n              hasChanged={hasFieldChanged('title')}\n            />\n\n            {/* Save Changes Bar */}\n            {hasUnsavedChanges && (\n              <div className=\"bg-warning/10 border-b border-warning/20 px-8 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Icon name=\"AlertCircle\" size={20} className=\"text-warning\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-warning\">You have unsaved changes</p>\n                      <p className=\"text-xs text-warning/80\">Save your changes to avoid losing them</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={handleDiscardChanges}\n                      disabled={isSaving}\n                      className=\"px-4 py-2 text-sm font-medium text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-muted transition-colors disabled:opacity-50\"\n                    >\n                      Discard\n                    </button>\n                    <button\n                      onClick={handleSaveChanges}\n                      disabled={isSaving}\n                      className=\"px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50 flex items-center space-x-2\"\n                    >\n                      {isSaving ? (\n                        <>\n                          <Icon name=\"Loader2\" size={16} className=\"animate-spin\" />\n                          <span>Saving...</span>\n                        </>\n                      ) : (\n                        <>\n                          <Icon name=\"Save\" size={16} />\n                          <span>Save Changes</span>\n                        </>\n                      )}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Main Content */}\n            <div className=\"flex flex-col lg:flex-row overflow-y-auto max-h-[calc(100vh-8rem)]\">\n              {/* Left Column - Main Content */}\n              <div className=\"flex-1 lg:w-3/5 p-8 space-y-8\">\n                <CardDescription\n                  card={cardData}\n                  onDescriptionChange={handleDescriptionChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('description')}\n                />\n                <ChecklistManager\n                  card={cardData}\n                  onChecklistChange={handleChecklistChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('checklist')}\n                />\n                <ActivityTimeline card={cardData} onAddComment={handleAddComment} canComment={canComment} />\n              </div>\n\n              {/* Right Column - Sidebar */}\n              <div className=\"lg:w-2/5 p-8 bg-gradient-to-b from-muted/20 to-muted/40 border-l border-border/50 space-y-8\">\n                <MemberAssignment\n                  card={cardData}\n                  onMembersChange={handleMembersChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('assignedMembers')}\n                />\n                <DueDatePicker\n                  card={cardData}\n                  onDueDateChange={handleDueDateChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('dueDate')}\n                />\n                <LabelManager\n                  card={cardData}\n                  onLabelsChange={handleLabelsChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('labels')}\n                />\n\n                {/* Card Information Section */}\n                <div className=\"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\">\n                  <h4 className=\"font-semibold text-text-primary flex items-center gap-2\">\n                    <Icon name=\"Info\" size={16} className=\"text-primary\" />\n                    Card Information\n                  </h4>\n                  <div className=\"space-y-3 text-sm\">\n                    <div className=\"flex justify-between items-center py-2 border-b border-border/20\">\n                      <span className=\"text-text-secondary font-medium\">Created:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.createdAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center py-2 border-b border-border/20\">\n                      <span className=\"text-text-secondary font-medium\">Last updated:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.updatedAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center py-2\">\n                      <span className=\"text-text-secondary font-medium\">Card ID:</span>\n                      <span className=\"text-text-primary font-mono text-xs bg-muted px-2 py-1 rounded\">#{cardData.id}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Actions Section */}\n                {(canEdit || canDelete) && (\n                  <div className=\"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\">\n                    <h4 className=\"font-semibold text-text-primary flex items-center gap-2\">\n                      <Icon name=\"Settings\" size={16} className=\"text-primary\" />\n                      Actions\n                    </h4>\n                    <div className=\"space-y-2\">\n                      {canEdit && (\n                        <button className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\">\n                          <Icon name=\"Archive\" size={16} className=\"text-text-secondary\" />\n                          Archive Card\n                        </button>\n                      )}\n                      {canEdit && (\n                        <button className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\">\n                          <Icon name=\"Copy\" size={16} className=\"text-text-secondary\" />\n                          Copy Card\n                        </button>\n                      )}\n                      {canEdit && (\n                        <button className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\">\n                          <Icon name=\"Move\" size={16} className=\"text-text-secondary\" />\n                          Move Card\n                        </button>\n                      )}\n                      {canDelete && (\n                        <button\n                          onClick={handleDelete}\n                          className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors border border-transparent hover:border-destructive/20\"\n                        >\n                          <Icon name=\"Trash2\" size={16} className=\"text-destructive\" />\n                          Delete Card\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CardDetails;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC5E,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,YAAY,CAAC,GAAGpB,eAAe,CAAC,CAAC;EACxC,MAAMqB,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAMgC,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAC7E,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAACF,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EACrE,MAAME,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACH,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAEhF,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAI/C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8C,kBAAkB,GAAIC,CAAC,IAAK;MAChC,IAAIL,iBAAiB,EAAE;QACrBK,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACE,WAAW,GAAG,EAAE;MACpB;IACF,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEL,kBAAkB,CAAC;IAC3D,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEN,kBAAkB,CAAC;EAC7E,CAAC,EAAE,CAACJ,iBAAiB,CAAC,CAAC;;EAEvB;EACA1C,SAAS,CAAC,MAAM;IACd,MAAMqD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAAA,IAAAC,eAAA;MAC/Bf,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,KAAAe,eAAA,GAAIjC,QAAQ,CAACkC,KAAK,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,IAAI,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAErC,QAAQ,CAACkC,KAAK,CAACC,IAAI,CAAC;;QAErE;QACA,MAAMG,kBAAkB,GAAG;UACzB,GAAGtC,QAAQ,CAACkC,KAAK,CAACC,IAAI;UACtBI,SAAS,EAAEvC,QAAQ,CAACkC,KAAK,CAACC,IAAI,CAACK,eAAe,IAAIxC,QAAQ,CAACkC,KAAK,CAACC,IAAI,CAACI,SAAS,IAAI;QACrF,CAAC;;QAED;QACA,IAAID,kBAAkB,CAACE,eAAe,EAAE;UACtC,OAAOF,kBAAkB,CAACE,eAAe;QAC3C;QAEAxB,WAAW,CAACsB,kBAAkB,CAAC;QAC/BpB,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACA,IAAIhB,MAAM,EAAE;QACV,IAAI;UACF,MAAMuC,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;UACvE,MAAMC,MAAM,GAAG,MAAMF,UAAU,CAACG,KAAK,CAACC,OAAO,CAAC3C,MAAM,CAAC;UACrDkC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEM,MAAM,CAAC;UAC7C,IAAIA,MAAM,CAACG,IAAI,EAAE;YACf;YACA,MAAMR,kBAAkB,GAAG;cACzB,GAAGK,MAAM,CAACG,IAAI;cACdP,SAAS,EAAEI,MAAM,CAACG,IAAI,CAACN,eAAe,IAAIG,MAAM,CAACG,IAAI,CAACP,SAAS,IAAI;YACrE,CAAC;;YAED;YACA,IAAID,kBAAkB,CAACE,eAAe,EAAE;cACtC,OAAOF,kBAAkB,CAACE,eAAe;YAC3C;YAEAxB,WAAW,CAACsB,kBAAkB,CAAC;YAC/BpB,YAAY,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;MACF;;MAEA;MACAX,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDrB,WAAW,CAAC;QACVgC,EAAE,EAAE9C,MAAM,IAAI,GAAG;QACjB+C,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,0EAA0E;QACvFC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,EAAE;QACVf,SAAS,EAAE,EAAE;QACbgB,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;MACFxC,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDc,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC9B,MAAM,EAAEF,QAAQ,CAACkC,KAAK,CAAC,CAAC;;EAE5B;EACA,MAAM0B,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACpC,MAAMC,WAAW,GAAG;MAAE,CAACF,KAAK,GAAGC,KAAK;MAAEH,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC;;IAE3E;IACA1C,WAAW,CAACgD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAY,CAAC,CAAC,CAAC;;IAElD;IACA3C,iBAAiB,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAY,CAAC,CAAC,CAAC;IACxDzC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM2C,eAAe,GAAIJ,KAAK,IAAK;IACjC,OAAO1C,cAAc,CAAC+C,cAAc,CAACL,KAAK,CAAC;EAC7C,CAAC;EAED,MAAMM,iBAAiB,GAAIC,QAAQ,IAAK;IACtCR,WAAW,CAAC,OAAO,EAAEQ,QAAQ,CAAC;EAChC,CAAC;EAED,MAAMC,uBAAuB,GAAIC,cAAc,IAAK;IAClDV,WAAW,CAAC,aAAa,EAAEU,cAAc,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAOR,WAAW,IAAK;IAC7C,IAAI;MACF,IAAI,EAAChD,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,GAAE;MAEnB,MAAMP,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;;MAEvE;MACA,IAAIqB,WAAW,CAACxB,SAAS,EAAE;QACzB,MAAMiC,oBAAoB,CAACT,WAAW,CAACxB,SAAS,CAAC;QACjD;QACA,MAAM;UAAEA,SAAS;UAAE,GAAGkC;QAAe,CAAC,GAAGV,WAAW;QACpD,IAAIW,MAAM,CAACC,IAAI,CAACF,cAAc,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMnC,UAAU,CAACG,KAAK,CAACiC,MAAM,CAAC9D,QAAQ,CAACiC,EAAE,EAAEyB,cAAc,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,MAAMhC,UAAU,CAACG,KAAK,CAACiC,MAAM,CAAC9D,QAAQ,CAACiC,EAAE,EAAEe,WAAW,CAAC;MACzD;MAEA3B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0B,WAAW,CAAC;IACnD,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM+B,WAAW,GAAIC,GAAG,IAAK;IAC3B,MAAMC,SAAS,GAAG,4EAA4E;IAC9F,OAAOA,SAAS,CAACC,IAAI,CAACF,GAAG,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMP,oBAAoB,GAAG,MAAOU,cAAc,IAAK;IACrD,IAAI;MACF,IAAI,EAACnE,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,KAAI,CAACkC,cAAc,EAAE;MAEtC,MAAMzC,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;;MAEvE;MACA,MAAMyC,YAAY,GAAGpE,QAAQ,CAACwB,SAAS,IAAI,EAAE;;MAE7C;MACA;MACA,MAAM6C,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MACjCF,YAAY,CAACG,OAAO,CAACC,IAAI,IAAI;QAC3B,IAAIA,IAAI,CAACvC,EAAE,EAAE;UACX,MAAMwC,MAAM,GAAGD,IAAI,CAACvC,EAAE,CAACyC,QAAQ,CAAC,CAAC;UACjC,MAAMC,WAAW,GAAG,CAACF,MAAM,IACRA,MAAM,KAAK,EAAE,IACbA,MAAM,CAACG,UAAU,CAAC,OAAO,CAAC,IAC1BH,MAAM,CAACG,UAAU,CAAC,KAAK,CAAC,IACxB,CAACb,WAAW,CAACU,MAAM,CAAC;;UAEvC;UACA,IAAI,CAACE,WAAW,EAAE;YAChBN,eAAe,CAACQ,GAAG,CAACL,IAAI,CAACvC,EAAE,EAAEuC,IAAI,CAAC;UACpC;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMM,WAAW,GAAG,IAAIR,GAAG,CAAC,CAAC;MAC7B,MAAMS,aAAa,GAAG,EAAE;MAExBZ,cAAc,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEQ,KAAK,KAAK;QACtC,MAAMP,MAAM,GAAGD,IAAI,CAACvC,EAAE,GAAGuC,IAAI,CAACvC,EAAE,CAACyC,QAAQ,CAAC,CAAC,GAAG,EAAE;;QAEhD;QACA,MAAMC,WAAW,GAAG,CAACF,MAAM,IACRA,MAAM,KAAK,EAAE,IACbA,MAAM,CAACG,UAAU,CAAC,OAAO,CAAC,IAC1BH,MAAM,CAACG,UAAU,CAAC,KAAK,CAAC,IACxB,CAACb,WAAW,CAACU,MAAM,CAAC,CAAC,CAAC;;QAEzC,IAAID,IAAI,CAACvC,EAAE,IAAI,CAAC0C,WAAW,EAAE;UAC3B;UACAG,WAAW,CAACD,GAAG,CAACL,IAAI,CAACvC,EAAE,EAAE;YACvB,GAAGuC,IAAI;YACPS,QAAQ,EAAET,IAAI,CAACS,QAAQ,KAAKC,SAAS,GAAGV,IAAI,CAACS,QAAQ,GAAGD;UAC1D,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAD,aAAa,CAACI,IAAI,CAAC;YACjBC,IAAI,EAAEZ,IAAI,CAACY,IAAI,IAAIZ,IAAI,CAACtC,KAAK,IAAI,EAAE;YACnCM,SAAS,EAAEgC,IAAI,CAAChC,SAAS,IAAI,KAAK;YAClCyC,QAAQ,EAAET,IAAI,CAACS,QAAQ,KAAKC,SAAS,GAAGV,IAAI,CAACS,QAAQ,GAAGD,KAAK;YAC7DK,YAAY,EAAEb,IAAI,CAACc,WAAW,IAAId,IAAI,CAACa,YAAY,IAAI,KAAK;YAC5DE,UAAU,EAAEf,IAAI,CAACe,UAAU,IAAI,IAAI;YACnCC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ,IAAI;UAC7B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA;MACA,KAAK,MAAM,CAACf,MAAM,CAAC,IAAIJ,eAAe,EAAE;QACtC,IAAI,CAACS,WAAW,CAACW,GAAG,CAAChB,MAAM,CAAC,EAAE;UAC5B,IAAI;YACF;YACA,MAAMiB,SAAS,GAAGjB,MAAM,CAACC,QAAQ,CAAC,CAAC;YACnC,IAAIX,WAAW,CAAC2B,SAAS,CAAC,EAAE;cAC1B,MAAMhE,UAAU,CAACF,SAAS,CAACmE,UAAU,CAAClB,MAAM,CAAC;cAC7CpD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,MAAM,CAAC;YAChD,CAAC,MAAM;cACLpD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmD,MAAM,CAAC;YAC5D;UACF,CAAC,CAAC,OAAOzC,KAAK,EAAE;YACdX,OAAO,CAACuE,IAAI,CAAC,kCAAkC,EAAEnB,MAAM,EAAEzC,KAAK,CAAC;UACjE;QACF;MACF;;MAEA;MACA,KAAK,MAAM,CAACyC,MAAM,EAAEoB,OAAO,CAAC,IAAIf,WAAW,EAAE;QAC3C,MAAMgB,WAAW,GAAGzB,eAAe,CAACjF,GAAG,CAACqF,MAAM,CAAC;QAC/C,IAAIqB,WAAW,EAAE;UACf;UACA,MAAMC,UAAU,GACdD,WAAW,CAACV,IAAI,KAAKS,OAAO,CAACT,IAAI,IACjCU,WAAW,CAACtD,SAAS,KAAKqD,OAAO,CAACrD,SAAS,IAC3CsD,WAAW,CAACb,QAAQ,KAAKY,OAAO,CAACZ,QAAQ;UAE3C,IAAIc,UAAU,EAAE;YACd,IAAI;cACF;cACA,MAAML,SAAS,GAAGjB,MAAM,CAACC,QAAQ,CAAC,CAAC;cACnC,IAAIX,WAAW,CAAC2B,SAAS,CAAC,EAAE;gBAC1B,MAAMhE,UAAU,CAACF,SAAS,CAACwE,UAAU,CAACvB,MAAM,EAAE;kBAC5CW,IAAI,EAAES,OAAO,CAACT,IAAI;kBAClB5C,SAAS,EAAEqD,OAAO,CAACrD,SAAS;kBAC5ByC,QAAQ,EAAEY,OAAO,CAACZ;gBACpB,CAAC,CAAC;gBACF5D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,MAAM,CAAC;cAChD,CAAC,MAAM;gBACLpD,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAEmD,MAAM,CAAC;cACpF;YACF,CAAC,CAAC,OAAOzC,KAAK,EAAE;cACdX,OAAO,CAACuE,IAAI,CAAC,kCAAkC,EAAEnB,MAAM,EAAEzC,KAAK,CAAC;YACjE;UACF;QACF;MACF;;MAEA;MACA,IAAI+C,aAAa,CAAClB,MAAM,GAAG,CAAC,EAAE;QAC5B,IAAI;UACF,MAAMnC,UAAU,CAACF,SAAS,CAACyE,UAAU,CAACjG,QAAQ,CAACiC,EAAE,EAAE;YAAEiE,KAAK,EAAEnB;UAAc,CAAC,CAAC;UAC5E1D,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyD,aAAa,CAAClB,MAAM,CAAC;QACnE,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF;MAEAX,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;EAED,MAAMmE,mBAAmB,GAAIC,UAAU,IAAK;IAC1CvD,WAAW,CAAC,iBAAiB,EAAEuD,UAAU,CAAC;EAC5C,CAAC;EAED,MAAMC,mBAAmB,GAAIC,UAAU,IAAK;IAC1CzD,WAAW,CAAC,SAAS,EAAEyD,UAAU,CAAC;EACpC,CAAC;EAED,MAAMC,kBAAkB,GAAIC,SAAS,IAAK;IACxC3D,WAAW,CAAC,QAAQ,EAAE2D,SAAS,CAAC;EAClC,CAAC;EAED,MAAMC,qBAAqB,GAAIC,YAAY,IAAK;IAC9C7D,WAAW,CAAC,WAAW,EAAE6D,YAAY,CAAC;EACxC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACrG,iBAAiB,IAAI,EAACN,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,GAAE;IAEzCxB,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAM+C,eAAe,CAACpD,cAAc,CAAC;;MAErC;MACA,IAAIJ,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,EAAE;QAChB,IAAI;UACF,MAAMP,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;UACvE,MAAMC,MAAM,GAAG,MAAMF,UAAU,CAACG,KAAK,CAACC,OAAO,CAAC9B,QAAQ,CAACiC,EAAE,CAAC;UAC1D,IAAIL,MAAM,CAACG,IAAI,EAAE;YACf;YACA,MAAMR,kBAAkB,GAAG;cACzB,GAAGK,MAAM,CAACG,IAAI;cACdP,SAAS,EAAEI,MAAM,CAACG,IAAI,CAACN,eAAe,IAAIG,MAAM,CAACG,IAAI,CAACP,SAAS,IAAI;YACrE,CAAC;;YAED;YACA,IAAID,kBAAkB,CAACE,eAAe,EAAE;cACtC,OAAOF,kBAAkB,CAACE,eAAe;YAC3C;YAEAxB,WAAW,CAACsB,kBAAkB,CAAC;UACjC;QACF,CAAC,CAAC,OAAOS,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF;;MAEA;MACA3B,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACrBE,oBAAoB,CAAC,KAAK,CAAC;MAE3Bc,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF,CAAC,SAAS;MACRvB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMmG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACtG,iBAAiB,EAAE;;IAExB;IACA;IACAD,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACrBE,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACAO,MAAM,CAAC7B,QAAQ,CAAC4H,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC1F,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyF,OAAO,CAAC;EAC5C,CAAC;;EAED;EACAnJ,SAAS,CAAC,MAAM;IACd,MAAMoJ,aAAa,GAAIrG,CAAC,IAAK;MAC3B,IAAI,CAACA,CAAC,CAACsG,OAAO,IAAItG,CAAC,CAACuG,OAAO,KAAKvG,CAAC,CAACwG,GAAG,KAAK,GAAG,EAAE;QAC7CxG,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,IAAIN,iBAAiB,EAAE;UACrBqG,iBAAiB,CAAC,CAAC;QACrB;MACF;IACF,CAAC;IAEDS,QAAQ,CAACrG,gBAAgB,CAAC,SAAS,EAAEiG,aAAa,CAAC;IACnD,OAAO,MAAMI,QAAQ,CAACpG,mBAAmB,CAAC,SAAS,EAAEgG,aAAa,CAAC;EACrE,CAAC,EAAE,CAAC1G,iBAAiB,EAAEqG,iBAAiB,CAAC,CAAC;EAE1C,MAAMU,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI/G,iBAAiB,EAAE;MACrB,MAAMgH,YAAY,GAAGxG,MAAM,CAACyG,OAAO,CACjC,0EACF,CAAC;MACD,IAAI,CAACD,YAAY,EAAE;IACrB;IACAtI,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAMwI,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1G,MAAM,CAACyG,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChElG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEtB,QAAQ,CAACiC,EAAE,CAAC;MACzCjD,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;;EAED;EACApB,SAAS,CAAC,MAAM;IACd,MAAM6J,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMjJ,WAAW,CAACkJ,cAAc,CAAC,CAAC;QACvD,MAAMC,WAAW,GAAG,MAAMnJ,WAAW,CAACoJ,sBAAsB,CAAC,CAAC;QAE9D,IAAIH,YAAY,CAAC3F,IAAI,IAAI2F,YAAY,CAAC3F,IAAI,CAAC+F,IAAI,EAAE;UAC/CxI,cAAc,CAACoI,YAAY,CAAC3F,IAAI,CAAC+F,IAAI,CAAC;UACtCtI,WAAW,CAACkI,YAAY,CAAC3F,IAAI,CAAC+F,IAAI,CAACC,IAAI,IAAI,QAAQ,CAAC;QACtD;QAEA,IAAIH,WAAW,CAAC7F,IAAI,IAAI6F,WAAW,CAAC7F,IAAI,CAACiG,YAAY,EAAE;UACrDtI,sBAAsB,CAACkI,WAAW,CAAC7F,IAAI,CAACiG,YAAY,CAAC;QACvD;MACF,CAAC,CAAC,OAAOhG,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAxC,WAAW,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC;IAEDiI,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN7J,SAAS,CAAC,MAAM;IACdwJ,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC,OAAO,MAAM;MACXf,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIjI,SAAS,EAAE;IACb,oBACEvB,OAAA;MAAKyJ,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC1J,OAAA,CAACX,eAAe;QACduB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzBiJ,IAAI,EAAE,GAAGjJ,WAAW,CAACkJ,SAAS,IAAIlJ,WAAW,CAACmJ,QAAQ,EAAE;UACxDC,KAAK,EAAEpJ,WAAW,CAACoJ,KAAK;UACxBC,MAAM,EAAErJ,WAAW,CAACqJ,MAAM,IAAI,2BAA2B;UACzDX,IAAI,EAAExI;QACR,CAAC,GAAG;UACF+I,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCX,IAAI,EAAExI;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAAkJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFnK,OAAA;QAAKyJ,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE1J,OAAA;UAAKyJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1J,OAAA;YAAKyJ,SAAS,EAAC;UAA0E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChGnK,OAAA;YAAKyJ,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjFnK,OAAA;YAAKyJ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAA2C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC9I,QAAQ,EAAE;IACb,oBACErB,OAAA;MAAKyJ,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC1J,OAAA,CAACX,eAAe;QACduB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzBiJ,IAAI,EAAE,GAAGjJ,WAAW,CAACkJ,SAAS,IAAIlJ,WAAW,CAACmJ,QAAQ,EAAE;UACxDC,KAAK,EAAEpJ,WAAW,CAACoJ,KAAK;UACxBC,MAAM,EAAErJ,WAAW,CAACqJ,MAAM,IAAI,2BAA2B;UACzDX,IAAI,EAAExI;QACR,CAAC,GAAG;UACF+I,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCX,IAAI,EAAExI;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAAkJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFnK,OAAA;QAAKyJ,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE1J,OAAA;UAAKyJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1J,OAAA;YAAKyJ,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChFnK,OAAA;YAAKyJ,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAsC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtFnK,OAAA;YAAQoK,OAAO,EAAE1B,WAAY;YAACe,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnK,OAAA;IAAKyJ,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzC1J,OAAA,CAACX,eAAe;MACduB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;MACjCR,WAAW,EAAEA,WAAW,GAAG;QACzBiJ,IAAI,EAAE,GAAGjJ,WAAW,CAACkJ,SAAS,IAAIlJ,WAAW,CAACmJ,QAAQ,EAAE;QACxDC,KAAK,EAAEpJ,WAAW,CAACoJ,KAAK;QACxBC,MAAM,EAAErJ,WAAW,CAACqJ,MAAM,IAAI,2BAA2B;QACzDX,IAAI,EAAExI;MACR,CAAC,GAAG;QACF+I,IAAI,EAAE,YAAY;QAClBG,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,2BAA2B;QACnCX,IAAI,EAAExI;MACR,CAAE;MACFE,mBAAmB,EAAEA;IAAoB;MAAAkJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFnK,OAAA;MAAKyJ,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE1J,OAAA;QAAKyJ,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAE7E1J,OAAA;UAAKyJ,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAE1H1J,OAAA,CAACT,UAAU;YACTkD,IAAI,EAAEpB,QAAS;YACfgJ,aAAa,EAAE5F,iBAAkB;YACjC6F,OAAO,EAAE5B,WAAY;YACrB6B,QAAQ,EAAE1B,YAAa;YACvB7H,OAAO,EAAEA,OAAQ;YACjBG,SAAS,EAAEA,SAAU;YACrBiG,UAAU,EAAE7C,eAAe,CAAC,OAAO;UAAE;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EAGDxI,iBAAiB,iBAChB3B,OAAA;YAAKyJ,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eACjE1J,OAAA;cAAKyJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1J,OAAA;gBAAKyJ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C1J,OAAA,CAACV,IAAI;kBAACqK,IAAI,EAAC,aAAa;kBAACa,IAAI,EAAE,EAAG;kBAACf,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9DnK,OAAA;kBAAA0J,QAAA,gBACE1J,OAAA;oBAAGyJ,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5EnK,OAAA;oBAAGyJ,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAsC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnK,OAAA;gBAAKyJ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C1J,OAAA;kBACEoK,OAAO,EAAEnC,oBAAqB;kBAC9BwC,QAAQ,EAAE5I,QAAS;kBACnB4H,SAAS,EAAC,gKAAgK;kBAAAC,QAAA,EAC3K;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnK,OAAA;kBACEoK,OAAO,EAAEpC,iBAAkB;kBAC3ByC,QAAQ,EAAE5I,QAAS;kBACnB4H,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EAE/J7H,QAAQ,gBACP7B,OAAA,CAAAE,SAAA;oBAAAwJ,QAAA,gBACE1J,OAAA,CAACV,IAAI;sBAACqK,IAAI,EAAC,SAAS;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAc;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1DnK,OAAA;sBAAA0J,QAAA,EAAM;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACtB,CAAC,gBAEHnK,OAAA,CAAAE,SAAA;oBAAAwJ,QAAA,gBACE1J,OAAA,CAACV,IAAI;sBAACqK,IAAI,EAAC,MAAM;sBAACa,IAAI,EAAE;oBAAG;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9BnK,OAAA;sBAAA0J,QAAA,EAAM;oBAAY;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACzB;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnK,OAAA;YAAKyJ,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBAEjF1J,OAAA;cAAKyJ,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5C1J,OAAA,CAACR,eAAe;gBACdiD,IAAI,EAAEpB,QAAS;gBACfqJ,mBAAmB,EAAE/F,uBAAwB;gBAC7C3D,OAAO,EAAEA,OAAQ;gBACjBoG,UAAU,EAAE7C,eAAe,CAAC,aAAa;cAAE;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACFnK,OAAA,CAACJ,gBAAgB;gBACf6C,IAAI,EAAEpB,QAAS;gBACfsJ,iBAAiB,EAAE7C,qBAAsB;gBACzC9G,OAAO,EAAEA,OAAQ;gBACjBoG,UAAU,EAAE7C,eAAe,CAAC,WAAW;cAAE;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACFnK,OAAA,CAACH,gBAAgB;gBAAC4C,IAAI,EAAEpB,QAAS;gBAACuJ,YAAY,EAAEzC,gBAAiB;gBAAC/G,UAAU,EAAEA;cAAW;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eAGNnK,OAAA;cAAKyJ,SAAS,EAAC,6FAA6F;cAAAC,QAAA,gBAC1G1J,OAAA,CAACP,gBAAgB;gBACfgD,IAAI,EAAEpB,QAAS;gBACfwJ,eAAe,EAAErD,mBAAoB;gBACrCxG,OAAO,EAAEA,OAAQ;gBACjBoG,UAAU,EAAE7C,eAAe,CAAC,iBAAiB;cAAE;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACFnK,OAAA,CAACN,aAAa;gBACZ+C,IAAI,EAAEpB,QAAS;gBACfyJ,eAAe,EAAEpD,mBAAoB;gBACrC1G,OAAO,EAAEA,OAAQ;gBACjBoG,UAAU,EAAE7C,eAAe,CAAC,SAAS;cAAE;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACFnK,OAAA,CAACL,YAAY;gBACX8C,IAAI,EAAEpB,QAAS;gBACf0J,cAAc,EAAEnD,kBAAmB;gBACnC5G,OAAO,EAAEA,OAAQ;gBACjBoG,UAAU,EAAE7C,eAAe,CAAC,QAAQ;cAAE;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAGFnK,OAAA;gBAAKyJ,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC7E1J,OAAA;kBAAIyJ,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,gBACrE1J,OAAA,CAACV,IAAI;oBAACqK,IAAI,EAAC,MAAM;oBAACa,IAAI,EAAE,EAAG;oBAACf,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEzD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnK,OAAA;kBAAKyJ,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1J,OAAA;oBAAKyJ,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC/E1J,OAAA;sBAAMyJ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjEnK,OAAA;sBAAMyJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAI3F,IAAI,CAAC1C,QAAQ,CAACyC,SAAS,CAAC,CAACkH,kBAAkB,CAAC;oBAAC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACNnK,OAAA;oBAAKyJ,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC/E1J,OAAA;sBAAMyJ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtEnK,OAAA;sBAAMyJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAI3F,IAAI,CAAC1C,QAAQ,CAAC4C,SAAS,CAAC,CAAC+G,kBAAkB,CAAC;oBAAC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACNnK,OAAA;oBAAKyJ,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1J,OAAA;sBAAMyJ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjEnK,OAAA;sBAAMyJ,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,GAAC,GAAC,EAACrI,QAAQ,CAACiC,EAAE;oBAAA;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL,CAACnJ,OAAO,IAAIG,SAAS,kBACpBnB,OAAA;gBAAKyJ,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC7E1J,OAAA;kBAAIyJ,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,gBACrE1J,OAAA,CAACV,IAAI;oBAACqK,IAAI,EAAC,UAAU;oBAACa,IAAI,EAAE,EAAG;oBAACf,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnK,OAAA;kBAAKyJ,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvB1I,OAAO,iBACNhB,OAAA;oBAAQyJ,SAAS,EAAC,oKAAoK;oBAAAC,QAAA,gBACpL1J,OAAA,CAACV,IAAI;sBAACqK,IAAI,EAAC,SAAS;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAqB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEnE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACAnJ,OAAO,iBACNhB,OAAA;oBAAQyJ,SAAS,EAAC,oKAAoK;oBAAAC,QAAA,gBACpL1J,OAAA,CAACV,IAAI;sBAACqK,IAAI,EAAC,MAAM;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAqB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACAnJ,OAAO,iBACNhB,OAAA;oBAAQyJ,SAAS,EAAC,oKAAoK;oBAAAC,QAAA,gBACpL1J,OAAA,CAACV,IAAI;sBAACqK,IAAI,EAAC,MAAM;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAqB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACAhJ,SAAS,iBACRnB,OAAA;oBACEoK,OAAO,EAAEvB,YAAa;oBACtBY,SAAS,EAAC,8KAA8K;oBAAAC,QAAA,gBAExL1J,OAAA,CAACV,IAAI;sBAACqK,IAAI,EAAC,QAAQ;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAkB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAE/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/J,EAAA,CAzqBID,WAAW;EAAA,QACEjB,WAAW,EACXE,WAAW,EACLD,eAAe;AAAA;AAAA8L,EAAA,GAHlC9K,WAAW;AA2qBjB,eAAeA,WAAW;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}