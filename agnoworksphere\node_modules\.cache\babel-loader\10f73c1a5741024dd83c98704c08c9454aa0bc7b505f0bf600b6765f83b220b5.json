{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\card-details\\\\index.jsx\",\n  _s = $RefreshSig$();\n// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Icon from '../../components/AppIcon';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CardDetails = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Track pending changes\n  const [pendingChanges, setPendingChanges] = useState({});\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Warn user about unsaved changes when leaving\n  useEffect(() => {\n    const handleBeforeUnload = e => {\n      if (hasUnsavedChanges) {\n        e.preventDefault();\n        e.returnValue = '';\n      }\n    };\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload);\n  }, [hasUnsavedChanges]);\n\n  // Load card data from location state or API\n  useEffect(() => {\n    const loadCardData = async () => {\n      var _location$state;\n      setIsLoading(true);\n\n      // First try to get card data from location state (when navigated from kanban board)\n      if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.card) {\n        console.log('Loading card from location state:', location.state.card);\n\n        // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n        const normalizedCardData = {\n          ...location.state.card,\n          checklist: location.state.card.checklist_items || location.state.card.checklist || []\n        };\n\n        // Remove checklist_items to avoid confusion\n        if (normalizedCardData.checklist_items) {\n          delete normalizedCardData.checklist_items;\n        }\n        setCardData(normalizedCardData);\n        setIsLoading(false);\n        return;\n      }\n\n      // If no state data, try to load from API\n      if (cardId) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardId);\n          console.log('Loading card from API:', result);\n          if (result.data) {\n            // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n            setCardData(normalizedCardData);\n            setIsLoading(false);\n            return;\n          }\n        } catch (error) {\n          console.error('Error loading card from API:', error);\n        }\n      }\n\n      // Fallback: use mock data if no card found\n      console.log('No card data found, using fallback data');\n      setCardData({\n        id: cardId || '1',\n        title: 'Card Not Found',\n        description: 'This card could not be loaded. Please return to the board and try again.',\n        columnTitle: 'Unknown',\n        assignedMembers: [],\n        dueDate: null,\n        labels: [],\n        checklist: [],\n        completed: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      });\n      setIsLoading(false);\n    };\n    loadCardData();\n  }, [cardId, location.state]);\n\n  // Helper function to track changes\n  const trackChange = (field, value) => {\n    const updatedData = {\n      [field]: value,\n      updatedAt: new Date().toISOString()\n    };\n\n    // Update local card data immediately for UI\n    setCardData(prev => ({\n      ...prev,\n      ...updatedData\n    }));\n\n    // Track pending changes\n    setPendingChanges(prev => ({\n      ...prev,\n      ...updatedData\n    }));\n    setHasUnsavedChanges(true);\n  };\n\n  // Helper function to check if a field has pending changes\n  const hasFieldChanged = field => {\n    return pendingChanges.hasOwnProperty(field);\n  };\n  const handleTitleChange = newTitle => {\n    trackChange('title', newTitle);\n  };\n  const handleDescriptionChange = newDescription => {\n    trackChange('description', newDescription);\n  };\n\n  // Helper function to update card via API\n  const updateCardInAPI = async updatedData => {\n    try {\n      if (!(cardData !== null && cardData !== void 0 && cardData.id)) return;\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Handle checklist separately if it's being updated\n      if (updatedData.checklist) {\n        await updateChecklistInAPI(updatedData.checklist);\n        // Remove checklist from card update data since it's handled separately\n        const {\n          checklist,\n          ...cardUpdateData\n        } = updatedData;\n        if (Object.keys(cardUpdateData).length > 0) {\n          await apiService.cards.update(cardData.id, cardUpdateData);\n        }\n      } else {\n        await apiService.cards.update(cardData.id, updatedData);\n      }\n      console.log('Card updated via API:', updatedData);\n    } catch (error) {\n      console.error('Error updating card via API:', error);\n    }\n  };\n\n  // Helper function to update checklist via dedicated API\n  const updateChecklistInAPI = async checklistItems => {\n    try {\n      if (!(cardData !== null && cardData !== void 0 && cardData.id) || !checklistItems) return;\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Get current checklist items from the card\n      const currentItems = cardData.checklist || [];\n\n      // Create a map of current items by ID for easy lookup\n      const currentItemsMap = new Map();\n      currentItems.forEach(item => {\n        if (item.id) {\n          currentItemsMap.set(item.id, item);\n        }\n      });\n\n      // Create a map of new items by ID\n      const newItemsMap = new Map();\n      const itemsToCreate = [];\n      checklistItems.forEach((item, index) => {\n        const itemId = item.id ? item.id.toString() : '';\n        const isTemporary = itemId.startsWith('temp-') || itemId.startsWith('ai-') || !itemId || itemId === '';\n        if (item.id && !isTemporary) {\n          // Existing item with real backend ID\n          newItemsMap.set(item.id, {\n            ...item,\n            position: item.position !== undefined ? item.position : index\n          });\n        } else {\n          // New item to create (temporary ID or no ID)\n          itemsToCreate.push({\n            text: item.text || item.title || '',\n            completed: item.completed || false,\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          });\n        }\n      });\n\n      // Delete items that are no longer present\n      for (const [itemId] of currentItemsMap) {\n        if (!newItemsMap.has(itemId)) {\n          try {\n            await apiService.checklist.deleteItem(itemId);\n            console.log('Deleted checklist item:', itemId);\n          } catch (error) {\n            console.warn('Failed to delete checklist item:', itemId, error);\n          }\n        }\n      }\n\n      // Update existing items that have changed\n      for (const [itemId, newItem] of newItemsMap) {\n        const currentItem = currentItemsMap.get(itemId);\n        if (currentItem) {\n          // Check if the item has changed\n          const hasChanged = currentItem.text !== newItem.text || currentItem.completed !== newItem.completed || currentItem.position !== newItem.position;\n          if (hasChanged) {\n            try {\n              await apiService.checklist.updateItem(itemId, {\n                text: newItem.text,\n                completed: newItem.completed,\n                position: newItem.position\n              });\n              console.log('Updated checklist item:', itemId);\n            } catch (error) {\n              console.warn('Failed to update checklist item:', itemId, error);\n            }\n          }\n        }\n      }\n\n      // Create new items\n      if (itemsToCreate.length > 0) {\n        try {\n          await apiService.checklist.createBulk(cardData.id, {\n            items: itemsToCreate\n          });\n          console.log('Created new checklist items:', itemsToCreate.length);\n        } catch (error) {\n          console.error('Failed to create new checklist items:', error);\n        }\n      }\n      console.log('Checklist updated via API successfully');\n    } catch (error) {\n      console.error('Error updating checklist via API:', error);\n    }\n  };\n  const handleMembersChange = newMembers => {\n    trackChange('assignedMembers', newMembers);\n  };\n  const handleDueDateChange = newDueDate => {\n    trackChange('dueDate', newDueDate);\n  };\n  const handleLabelsChange = newLabels => {\n    trackChange('labels', newLabels);\n  };\n  const handleChecklistChange = newChecklist => {\n    trackChange('checklist', newChecklist);\n  };\n\n  // Save all pending changes\n  const handleSaveChanges = async () => {\n    if (!hasUnsavedChanges || !(cardData !== null && cardData !== void 0 && cardData.id)) return;\n    setIsSaving(true);\n    try {\n      await updateCardInAPI(pendingChanges);\n\n      // Reload card data to get the latest state from the backend\n      if (cardData !== null && cardData !== void 0 && cardData.id) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardData.id);\n          if (result.data) {\n            // Normalize the card data\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n            setCardData(normalizedCardData);\n          }\n        } catch (error) {\n          console.error('Error reloading card data after save:', error);\n        }\n      }\n\n      // Clear pending changes\n      setPendingChanges({});\n      setHasUnsavedChanges(false);\n      console.log('All changes saved successfully');\n    } catch (error) {\n      console.error('Error saving changes:', error);\n      // You could show a toast notification here\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Discard all pending changes\n  const handleDiscardChanges = () => {\n    if (!hasUnsavedChanges) return;\n\n    // Revert cardData to original state (you might want to store original data)\n    // For now, we'll just clear pending changes and reload\n    setPendingChanges({});\n    setHasUnsavedChanges(false);\n\n    // Reload card data to revert changes\n    window.location.reload();\n  };\n  const handleAddComment = comment => {\n    console.log('New comment added:', comment);\n  };\n  const handleClose = () => {\n    if (hasUnsavedChanges) {\n      const confirmLeave = window.confirm('You have unsaved changes. Are you sure you want to leave without saving?');\n      if (!confirmLeave) return;\n    }\n    navigate('/kanban-board');\n  };\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n    loadUserData();\n  }, []);\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Loading card...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary\",\n            children: \"Please wait while we load the card details.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state if card not found\n  if (!cardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n        userRole: userRole.toLowerCase(),\n        currentUser: currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        },\n        currentOrganization: currentOrganization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"Card not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary mb-4\",\n            children: \"The requested card could not be found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClose,\n            className: \"text-primary hover:underline\",\n            children: \"Return to Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-1000 pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-center min-h-full p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full max-w-5xl bg-surface rounded-xl shadow-2xl my-8 max-h-screen overflow-hidden border border-border/20\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            card: cardData,\n            onTitleChange: handleTitleChange,\n            onClose: handleClose,\n            onDelete: handleDelete,\n            canEdit: canEdit,\n            canDelete: canDelete,\n            hasChanged: hasFieldChanged('title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), hasUnsavedChanges && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-warning/10 border-b border-warning/20 px-8 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"AlertCircle\",\n                  size: 20,\n                  className: \"text-warning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-warning\",\n                    children: \"You have unsaved changes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-warning/80\",\n                    children: \"Save your changes to avoid losing them\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleDiscardChanges,\n                  disabled: isSaving,\n                  className: \"px-4 py-2 text-sm font-medium text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-muted transition-colors disabled:opacity-50\",\n                  children: \"Discard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSaveChanges,\n                  disabled: isSaving,\n                  className: \"px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50 flex items-center space-x-2\",\n                  children: isSaving ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Loader2\",\n                      size: 16,\n                      className: \"animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Saving...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Save\",\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Save Changes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row overflow-y-auto max-h-[calc(100vh-8rem)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 lg:w-3/5 p-8 space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(CardDescription, {\n                card: cardData,\n                onDescriptionChange: handleDescriptionChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('description')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ChecklistManager, {\n                card: cardData,\n                onChecklistChange: handleChecklistChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('checklist')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ActivityTimeline, {\n                card: cardData,\n                onAddComment: handleAddComment,\n                canComment: canComment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:w-2/5 p-8 bg-gradient-to-b from-muted/20 to-muted/40 border-l border-border/50 space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(MemberAssignment, {\n                card: cardData,\n                onMembersChange: handleMembersChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('assignedMembers')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DueDatePicker, {\n                card: cardData,\n                onDueDateChange: handleDueDateChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('dueDate')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(LabelManager, {\n                card: cardData,\n                onLabelsChange: handleLabelsChange,\n                canEdit: canEdit,\n                hasChanged: hasFieldChanged('labels')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-text-primary flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Info\",\n                    size: 16,\n                    className: \"text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this), \"Card Information\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center py-2 border-b border-border/20\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary font-medium\",\n                      children: \"Created:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.createdAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center py-2 border-b border-border/20\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary font-medium\",\n                      children: \"Last updated:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary\",\n                      children: new Date(cardData.updatedAt).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center py-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-secondary font-medium\",\n                      children: \"Card ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-text-primary font-mono text-xs bg-muted px-2 py-1 rounded\",\n                      children: [\"#\", cardData.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), (canEdit || canDelete) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-text-primary flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Settings\",\n                    size: 16,\n                    className: \"text-primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this), \"Actions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Archive\",\n                      size: 16,\n                      className: \"text-text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 27\n                    }, this), \"Archive Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 25\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Copy\",\n                      size: 16,\n                      className: \"text-text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 27\n                    }, this), \"Copy Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 25\n                  }, this), canEdit && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Move\",\n                      size: 16,\n                      className: \"text-text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 27\n                    }, this), \"Move Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 25\n                  }, this), canDelete && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleDelete,\n                    className: \"w-full flex items-center gap-3 px-4 py-3 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors border border-transparent hover:border-destructive/20\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Trash2\",\n                      size: 16,\n                      className: \"text-destructive\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 27\n                    }, this), \"Delete Card\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 464,\n    columnNumber: 5\n  }, this);\n};\n_s(CardDetails, \"kLbPd5dDlMZj7+r7ZOFG2BYkiJc=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams];\n});\n_c = CardDetails;\nexport default CardDetails;\nvar _c;\n$RefreshReg$(_c, \"CardDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSearchParams", "useLocation", "RoleBasedHeader", "Icon", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "MemberAssignment", "DueDatePicker", "LabelManager", "ChecklistManager", "ActivityTimeline", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CardDetails", "_s", "navigate", "location", "searchParams", "cardId", "get", "currentUser", "setCurrentUser", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "canEdit", "includes", "toLowerCase", "canDelete", "canComment", "cardData", "setCardData", "isLoading", "setIsLoading", "pendingChanges", "setPendingChanges", "hasUnsavedChanges", "setHasUnsavedChanges", "isSaving", "setIsSaving", "handleBeforeUnload", "e", "preventDefault", "returnValue", "window", "addEventListener", "removeEventListener", "loadCardData", "_location$state", "state", "card", "console", "log", "normalizedCardData", "checklist", "checklist_items", "apiService", "default", "result", "cards", "getById", "data", "error", "id", "title", "description", "columnTitle", "assignedMembers", "dueDate", "labels", "completed", "createdAt", "Date", "toISOString", "updatedAt", "trackChange", "field", "value", "updatedData", "prev", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "handleTitleChange", "newTitle", "handleDescriptionChange", "newDescription", "updateCardInAPI", "updateChecklistInAPI", "cardUpdateData", "Object", "keys", "length", "update", "checklistItems", "currentItems", "currentItemsMap", "Map", "for<PERSON>ach", "item", "set", "newItemsMap", "itemsToCreate", "index", "itemId", "toString", "isTemporary", "startsWith", "position", "undefined", "push", "text", "ai_generated", "aiGenerated", "confidence", "metadata", "has", "deleteItem", "warn", "newItem", "currentItem", "has<PERSON><PERSON>ed", "updateItem", "createBulk", "items", "handleMembersChange", "newMembers", "handleDueDateChange", "newDueDate", "handleLabelsChange", "<PERSON><PERSON><PERSON><PERSON>", "handleChecklistChange", "newChecklist", "handleSaveChanges", "handleDiscardChanges", "reload", "handleAddComment", "comment", "handleClose", "confirmLeave", "confirm", "handleDelete", "loadUserData", "userResponse", "getCurrentUser", "orgResponse", "getCurrentOrganization", "user", "role", "organization", "document", "body", "style", "overflow", "className", "children", "name", "firstName", "lastName", "email", "avatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onTitleChange", "onClose", "onDelete", "size", "disabled", "onDescriptionChange", "onChecklistChange", "onAddComment", "onMembersChange", "onDueDateChange", "onLabelsChange", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/card-details/index.jsx"], "sourcesContent": ["// ... imports\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport Icon from '../../components/AppIcon';\nimport CardHeader from './components/CardHeader';\nimport CardDescription from './components/CardDescription';\nimport MemberAssignment from './components/MemberAssignment';\nimport DueDatePicker from './components/DueDatePicker';\nimport LabelManager from './components/LabelManager';\nimport ChecklistManager from './components/ChecklistManager';\nimport ActivityTimeline from './components/ActivityTimeline';\nimport authService from '../../utils/authService';\n\nconst CardDetails = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const cardId = searchParams.get('id');\n\n  // Authentication state\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRole, setUserRole] = useState('member');\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n\n  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());\n  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());\n\n  const [cardData, setCardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Track pending changes\n  const [pendingChanges, setPendingChanges] = useState({});\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n\n\n\n  // Warn user about unsaved changes when leaving\n  useEffect(() => {\n    const handleBeforeUnload = (e) => {\n      if (hasUnsavedChanges) {\n        e.preventDefault();\n        e.returnValue = '';\n      }\n    };\n\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload);\n  }, [hasUnsavedChanges]);\n\n  // Load card data from location state or API\n  useEffect(() => {\n    const loadCardData = async () => {\n      setIsLoading(true);\n\n      // First try to get card data from location state (when navigated from kanban board)\n      if (location.state?.card) {\n        console.log('Loading card from location state:', location.state.card);\n\n        // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n        const normalizedCardData = {\n          ...location.state.card,\n          checklist: location.state.card.checklist_items || location.state.card.checklist || []\n        };\n\n        // Remove checklist_items to avoid confusion\n        if (normalizedCardData.checklist_items) {\n          delete normalizedCardData.checklist_items;\n        }\n\n        setCardData(normalizedCardData);\n        setIsLoading(false);\n        return;\n      }\n\n      // If no state data, try to load from API\n      if (cardId) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardId);\n          console.log('Loading card from API:', result);\n          if (result.data) {\n            // Normalize the card data - convert checklist_items to checklist for frontend compatibility\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n\n            setCardData(normalizedCardData);\n            setIsLoading(false);\n            return;\n          }\n        } catch (error) {\n          console.error('Error loading card from API:', error);\n        }\n      }\n\n      // Fallback: use mock data if no card found\n      console.log('No card data found, using fallback data');\n      setCardData({\n        id: cardId || '1',\n        title: 'Card Not Found',\n        description: 'This card could not be loaded. Please return to the board and try again.',\n        columnTitle: 'Unknown',\n        assignedMembers: [],\n        dueDate: null,\n        labels: [],\n        checklist: [],\n        completed: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      });\n      setIsLoading(false);\n    };\n\n    loadCardData();\n  }, [cardId, location.state]);\n\n  // Helper function to track changes\n  const trackChange = (field, value) => {\n    const updatedData = { [field]: value, updatedAt: new Date().toISOString() };\n\n    // Update local card data immediately for UI\n    setCardData(prev => ({ ...prev, ...updatedData }));\n\n    // Track pending changes\n    setPendingChanges(prev => ({ ...prev, ...updatedData }));\n    setHasUnsavedChanges(true);\n  };\n\n  // Helper function to check if a field has pending changes\n  const hasFieldChanged = (field) => {\n    return pendingChanges.hasOwnProperty(field);\n  };\n\n  const handleTitleChange = (newTitle) => {\n    trackChange('title', newTitle);\n  };\n\n  const handleDescriptionChange = (newDescription) => {\n    trackChange('description', newDescription);\n  };\n\n  // Helper function to update card via API\n  const updateCardInAPI = async (updatedData) => {\n    try {\n      if (!cardData?.id) return;\n\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Handle checklist separately if it's being updated\n      if (updatedData.checklist) {\n        await updateChecklistInAPI(updatedData.checklist);\n        // Remove checklist from card update data since it's handled separately\n        const { checklist, ...cardUpdateData } = updatedData;\n        if (Object.keys(cardUpdateData).length > 0) {\n          await apiService.cards.update(cardData.id, cardUpdateData);\n        }\n      } else {\n        await apiService.cards.update(cardData.id, updatedData);\n      }\n\n      console.log('Card updated via API:', updatedData);\n    } catch (error) {\n      console.error('Error updating card via API:', error);\n    }\n  };\n\n  // Helper function to update checklist via dedicated API\n  const updateChecklistInAPI = async (checklistItems) => {\n    try {\n      if (!cardData?.id || !checklistItems) return;\n\n      const apiService = (await import('../../utils/realApiService')).default;\n\n      // Get current checklist items from the card\n      const currentItems = cardData.checklist || [];\n\n      // Create a map of current items by ID for easy lookup\n      const currentItemsMap = new Map();\n      currentItems.forEach(item => {\n        if (item.id) {\n          currentItemsMap.set(item.id, item);\n        }\n      });\n\n      // Create a map of new items by ID\n      const newItemsMap = new Map();\n      const itemsToCreate = [];\n\n      checklistItems.forEach((item, index) => {\n        const itemId = item.id ? item.id.toString() : '';\n        const isTemporary = itemId.startsWith('temp-') || itemId.startsWith('ai-') || !itemId || itemId === '';\n\n        if (item.id && !isTemporary) {\n          // Existing item with real backend ID\n          newItemsMap.set(item.id, {\n            ...item,\n            position: item.position !== undefined ? item.position : index\n          });\n        } else {\n          // New item to create (temporary ID or no ID)\n          itemsToCreate.push({\n            text: item.text || item.title || '',\n            completed: item.completed || false,\n            position: item.position !== undefined ? item.position : index,\n            ai_generated: item.aiGenerated || item.ai_generated || false,\n            confidence: item.confidence || null,\n            metadata: item.metadata || null\n          });\n        }\n      });\n\n      // Delete items that are no longer present\n      for (const [itemId] of currentItemsMap) {\n        if (!newItemsMap.has(itemId)) {\n          try {\n            await apiService.checklist.deleteItem(itemId);\n            console.log('Deleted checklist item:', itemId);\n          } catch (error) {\n            console.warn('Failed to delete checklist item:', itemId, error);\n          }\n        }\n      }\n\n      // Update existing items that have changed\n      for (const [itemId, newItem] of newItemsMap) {\n        const currentItem = currentItemsMap.get(itemId);\n        if (currentItem) {\n          // Check if the item has changed\n          const hasChanged =\n            currentItem.text !== newItem.text ||\n            currentItem.completed !== newItem.completed ||\n            currentItem.position !== newItem.position;\n\n          if (hasChanged) {\n            try {\n              await apiService.checklist.updateItem(itemId, {\n                text: newItem.text,\n                completed: newItem.completed,\n                position: newItem.position\n              });\n              console.log('Updated checklist item:', itemId);\n            } catch (error) {\n              console.warn('Failed to update checklist item:', itemId, error);\n            }\n          }\n        }\n      }\n\n      // Create new items\n      if (itemsToCreate.length > 0) {\n        try {\n          await apiService.checklist.createBulk(cardData.id, { items: itemsToCreate });\n          console.log('Created new checklist items:', itemsToCreate.length);\n        } catch (error) {\n          console.error('Failed to create new checklist items:', error);\n        }\n      }\n\n      console.log('Checklist updated via API successfully');\n    } catch (error) {\n      console.error('Error updating checklist via API:', error);\n    }\n  };\n\n  const handleMembersChange = (newMembers) => {\n    trackChange('assignedMembers', newMembers);\n  };\n\n  const handleDueDateChange = (newDueDate) => {\n    trackChange('dueDate', newDueDate);\n  };\n\n  const handleLabelsChange = (newLabels) => {\n    trackChange('labels', newLabels);\n  };\n\n  const handleChecklistChange = (newChecklist) => {\n    trackChange('checklist', newChecklist);\n  };\n\n  // Save all pending changes\n  const handleSaveChanges = async () => {\n    if (!hasUnsavedChanges || !cardData?.id) return;\n\n    setIsSaving(true);\n    try {\n      await updateCardInAPI(pendingChanges);\n\n      // Reload card data to get the latest state from the backend\n      if (cardData?.id) {\n        try {\n          const apiService = (await import('../../utils/realApiService')).default;\n          const result = await apiService.cards.getById(cardData.id);\n          if (result.data) {\n            // Normalize the card data\n            const normalizedCardData = {\n              ...result.data,\n              checklist: result.data.checklist_items || result.data.checklist || []\n            };\n\n            // Remove checklist_items to avoid confusion\n            if (normalizedCardData.checklist_items) {\n              delete normalizedCardData.checklist_items;\n            }\n\n            setCardData(normalizedCardData);\n          }\n        } catch (error) {\n          console.error('Error reloading card data after save:', error);\n        }\n      }\n\n      // Clear pending changes\n      setPendingChanges({});\n      setHasUnsavedChanges(false);\n\n      console.log('All changes saved successfully');\n    } catch (error) {\n      console.error('Error saving changes:', error);\n      // You could show a toast notification here\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Discard all pending changes\n  const handleDiscardChanges = () => {\n    if (!hasUnsavedChanges) return;\n\n    // Revert cardData to original state (you might want to store original data)\n    // For now, we'll just clear pending changes and reload\n    setPendingChanges({});\n    setHasUnsavedChanges(false);\n\n    // Reload card data to revert changes\n    window.location.reload();\n  };\n\n  const handleAddComment = (comment) => {\n    console.log('New comment added:', comment);\n  };\n\n  const handleClose = () => {\n    if (hasUnsavedChanges) {\n      const confirmLeave = window.confirm(\n        'You have unsaved changes. Are you sure you want to leave without saving?'\n      );\n      if (!confirmLeave) return;\n    }\n    navigate('/kanban-board');\n  };\n\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this card?')) {\n      console.log('Card deleted:', cardData.id);\n      navigate('/kanban-board');\n    }\n  };\n\n  // Load user authentication data\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const userResponse = await authService.getCurrentUser();\n        const orgResponse = await authService.getCurrentOrganization();\n\n        if (userResponse.data && userResponse.data.user) {\n          setCurrentUser(userResponse.data.user);\n          setUserRole(userResponse.data.user.role || 'member');\n        }\n\n        if (orgResponse.data && orgResponse.data.organization) {\n          setCurrentOrganization(orgResponse.data.organization);\n        }\n      } catch (error) {\n        console.error('Error loading user data:', error);\n        // Set default values if auth fails\n        setUserRole('member');\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  useEffect(() => {\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Loading card...</div>\n            <div className=\"text-text-secondary\">Please wait while we load the card details.</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state if card not found\n  if (!cardData) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <RoleBasedHeader\n          userRole={userRole.toLowerCase()}\n          currentUser={currentUser ? {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n            role: userRole\n          } : {\n            name: 'Loading...',\n            email: '',\n            avatar: '/assets/images/avatar.jpg',\n            role: userRole\n          }}\n          currentOrganization={currentOrganization}\n        />\n        <div className=\"pt-16 flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"text-lg font-medium text-text-primary mb-2\">Card not found</div>\n            <div className=\"text-text-secondary mb-4\">The requested card could not be found.</div>\n            <button onClick={handleClose} className=\"text-primary hover:underline\">\n              Return to Board\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Modal Overlay */}\n      <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm z-1000 pt-16\">\n        <div className=\"flex items-start justify-center min-h-full p-4 overflow-y-auto\">\n          {/* Modal Content */}\n          <div className=\"w-full max-w-5xl bg-surface rounded-xl shadow-2xl my-8 max-h-screen overflow-hidden border border-border/20\">\n            {/* Card Header */}\n            <CardHeader\n              card={cardData}\n              onTitleChange={handleTitleChange}\n              onClose={handleClose}\n              onDelete={handleDelete}\n              canEdit={canEdit}\n              canDelete={canDelete}\n              hasChanged={hasFieldChanged('title')}\n            />\n\n            {/* Save Changes Bar */}\n            {hasUnsavedChanges && (\n              <div className=\"bg-warning/10 border-b border-warning/20 px-8 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Icon name=\"AlertCircle\" size={20} className=\"text-warning\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-warning\">You have unsaved changes</p>\n                      <p className=\"text-xs text-warning/80\">Save your changes to avoid losing them</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={handleDiscardChanges}\n                      disabled={isSaving}\n                      className=\"px-4 py-2 text-sm font-medium text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-muted transition-colors disabled:opacity-50\"\n                    >\n                      Discard\n                    </button>\n                    <button\n                      onClick={handleSaveChanges}\n                      disabled={isSaving}\n                      className=\"px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50 flex items-center space-x-2\"\n                    >\n                      {isSaving ? (\n                        <>\n                          <Icon name=\"Loader2\" size={16} className=\"animate-spin\" />\n                          <span>Saving...</span>\n                        </>\n                      ) : (\n                        <>\n                          <Icon name=\"Save\" size={16} />\n                          <span>Save Changes</span>\n                        </>\n                      )}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Main Content */}\n            <div className=\"flex flex-col lg:flex-row overflow-y-auto max-h-[calc(100vh-8rem)]\">\n              {/* Left Column - Main Content */}\n              <div className=\"flex-1 lg:w-3/5 p-8 space-y-8\">\n                <CardDescription\n                  card={cardData}\n                  onDescriptionChange={handleDescriptionChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('description')}\n                />\n                <ChecklistManager\n                  card={cardData}\n                  onChecklistChange={handleChecklistChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('checklist')}\n                />\n                <ActivityTimeline card={cardData} onAddComment={handleAddComment} canComment={canComment} />\n              </div>\n\n              {/* Right Column - Sidebar */}\n              <div className=\"lg:w-2/5 p-8 bg-gradient-to-b from-muted/20 to-muted/40 border-l border-border/50 space-y-8\">\n                <MemberAssignment\n                  card={cardData}\n                  onMembersChange={handleMembersChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('assignedMembers')}\n                />\n                <DueDatePicker\n                  card={cardData}\n                  onDueDateChange={handleDueDateChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('dueDate')}\n                />\n                <LabelManager\n                  card={cardData}\n                  onLabelsChange={handleLabelsChange}\n                  canEdit={canEdit}\n                  hasChanged={hasFieldChanged('labels')}\n                />\n\n                {/* Card Information Section */}\n                <div className=\"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\">\n                  <h4 className=\"font-semibold text-text-primary flex items-center gap-2\">\n                    <Icon name=\"Info\" size={16} className=\"text-primary\" />\n                    Card Information\n                  </h4>\n                  <div className=\"space-y-3 text-sm\">\n                    <div className=\"flex justify-between items-center py-2 border-b border-border/20\">\n                      <span className=\"text-text-secondary font-medium\">Created:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.createdAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center py-2 border-b border-border/20\">\n                      <span className=\"text-text-secondary font-medium\">Last updated:</span>\n                      <span className=\"text-text-primary\">{new Date(cardData.updatedAt).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center py-2\">\n                      <span className=\"text-text-secondary font-medium\">Card ID:</span>\n                      <span className=\"text-text-primary font-mono text-xs bg-muted px-2 py-1 rounded\">#{cardData.id}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Actions Section */}\n                {(canEdit || canDelete) && (\n                  <div className=\"bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4\">\n                    <h4 className=\"font-semibold text-text-primary flex items-center gap-2\">\n                      <Icon name=\"Settings\" size={16} className=\"text-primary\" />\n                      Actions\n                    </h4>\n                    <div className=\"space-y-2\">\n                      {canEdit && (\n                        <button className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\">\n                          <Icon name=\"Archive\" size={16} className=\"text-text-secondary\" />\n                          Archive Card\n                        </button>\n                      )}\n                      {canEdit && (\n                        <button className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\">\n                          <Icon name=\"Copy\" size={16} className=\"text-text-secondary\" />\n                          Copy Card\n                        </button>\n                      )}\n                      {canEdit && (\n                        <button className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30\">\n                          <Icon name=\"Move\" size={16} className=\"text-text-secondary\" />\n                          Move Card\n                        </button>\n                      )}\n                      {canDelete && (\n                        <button\n                          onClick={handleDelete}\n                          className=\"w-full flex items-center gap-3 px-4 py-3 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors border border-transparent hover:border-destructive/20\"\n                        >\n                          <Icon name=\"Trash2\" size={16} className=\"text-destructive\" />\n                          Delete Card\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CardDetails;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC5E,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,YAAY,CAAC,GAAGpB,eAAe,CAAC,CAAC;EACxC,MAAMqB,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAMgC,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAC7E,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAACF,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EACrE,MAAME,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACH,QAAQ,CAACL,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;EAEhF,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAI/C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8C,kBAAkB,GAAIC,CAAC,IAAK;MAChC,IAAIL,iBAAiB,EAAE;QACrBK,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBD,CAAC,CAACE,WAAW,GAAG,EAAE;MACpB;IACF,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEL,kBAAkB,CAAC;IAC3D,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEN,kBAAkB,CAAC;EAC7E,CAAC,EAAE,CAACJ,iBAAiB,CAAC,CAAC;;EAEvB;EACA1C,SAAS,CAAC,MAAM;IACd,MAAMqD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAAA,IAAAC,eAAA;MAC/Bf,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,KAAAe,eAAA,GAAIjC,QAAQ,CAACkC,KAAK,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,IAAI,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAErC,QAAQ,CAACkC,KAAK,CAACC,IAAI,CAAC;;QAErE;QACA,MAAMG,kBAAkB,GAAG;UACzB,GAAGtC,QAAQ,CAACkC,KAAK,CAACC,IAAI;UACtBI,SAAS,EAAEvC,QAAQ,CAACkC,KAAK,CAACC,IAAI,CAACK,eAAe,IAAIxC,QAAQ,CAACkC,KAAK,CAACC,IAAI,CAACI,SAAS,IAAI;QACrF,CAAC;;QAED;QACA,IAAID,kBAAkB,CAACE,eAAe,EAAE;UACtC,OAAOF,kBAAkB,CAACE,eAAe;QAC3C;QAEAxB,WAAW,CAACsB,kBAAkB,CAAC;QAC/BpB,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACA,IAAIhB,MAAM,EAAE;QACV,IAAI;UACF,MAAMuC,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;UACvE,MAAMC,MAAM,GAAG,MAAMF,UAAU,CAACG,KAAK,CAACC,OAAO,CAAC3C,MAAM,CAAC;UACrDkC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEM,MAAM,CAAC;UAC7C,IAAIA,MAAM,CAACG,IAAI,EAAE;YACf;YACA,MAAMR,kBAAkB,GAAG;cACzB,GAAGK,MAAM,CAACG,IAAI;cACdP,SAAS,EAAEI,MAAM,CAACG,IAAI,CAACN,eAAe,IAAIG,MAAM,CAACG,IAAI,CAACP,SAAS,IAAI;YACrE,CAAC;;YAED;YACA,IAAID,kBAAkB,CAACE,eAAe,EAAE;cACtC,OAAOF,kBAAkB,CAACE,eAAe;YAC3C;YAEAxB,WAAW,CAACsB,kBAAkB,CAAC;YAC/BpB,YAAY,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;MACF;;MAEA;MACAX,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDrB,WAAW,CAAC;QACVgC,EAAE,EAAE9C,MAAM,IAAI,GAAG;QACjB+C,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,0EAA0E;QACvFC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,EAAE;QACVf,SAAS,EAAE,EAAE;QACbgB,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;MACFxC,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDc,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC9B,MAAM,EAAEF,QAAQ,CAACkC,KAAK,CAAC,CAAC;;EAE5B;EACA,MAAM0B,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACpC,MAAMC,WAAW,GAAG;MAAE,CAACF,KAAK,GAAGC,KAAK;MAAEH,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC;;IAE3E;IACA1C,WAAW,CAACgD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAY,CAAC,CAAC,CAAC;;IAElD;IACA3C,iBAAiB,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAY,CAAC,CAAC,CAAC;IACxDzC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM2C,eAAe,GAAIJ,KAAK,IAAK;IACjC,OAAO1C,cAAc,CAAC+C,cAAc,CAACL,KAAK,CAAC;EAC7C,CAAC;EAED,MAAMM,iBAAiB,GAAIC,QAAQ,IAAK;IACtCR,WAAW,CAAC,OAAO,EAAEQ,QAAQ,CAAC;EAChC,CAAC;EAED,MAAMC,uBAAuB,GAAIC,cAAc,IAAK;IAClDV,WAAW,CAAC,aAAa,EAAEU,cAAc,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAOR,WAAW,IAAK;IAC7C,IAAI;MACF,IAAI,EAAChD,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,GAAE;MAEnB,MAAMP,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;;MAEvE;MACA,IAAIqB,WAAW,CAACxB,SAAS,EAAE;QACzB,MAAMiC,oBAAoB,CAACT,WAAW,CAACxB,SAAS,CAAC;QACjD;QACA,MAAM;UAAEA,SAAS;UAAE,GAAGkC;QAAe,CAAC,GAAGV,WAAW;QACpD,IAAIW,MAAM,CAACC,IAAI,CAACF,cAAc,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMnC,UAAU,CAACG,KAAK,CAACiC,MAAM,CAAC9D,QAAQ,CAACiC,EAAE,EAAEyB,cAAc,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,MAAMhC,UAAU,CAACG,KAAK,CAACiC,MAAM,CAAC9D,QAAQ,CAACiC,EAAE,EAAEe,WAAW,CAAC;MACzD;MAEA3B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0B,WAAW,CAAC;IACnD,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMyB,oBAAoB,GAAG,MAAOM,cAAc,IAAK;IACrD,IAAI;MACF,IAAI,EAAC/D,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,KAAI,CAAC8B,cAAc,EAAE;MAEtC,MAAMrC,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;;MAEvE;MACA,MAAMqC,YAAY,GAAGhE,QAAQ,CAACwB,SAAS,IAAI,EAAE;;MAE7C;MACA,MAAMyC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MACjCF,YAAY,CAACG,OAAO,CAACC,IAAI,IAAI;QAC3B,IAAIA,IAAI,CAACnC,EAAE,EAAE;UACXgC,eAAe,CAACI,GAAG,CAACD,IAAI,CAACnC,EAAE,EAAEmC,IAAI,CAAC;QACpC;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,WAAW,GAAG,IAAIJ,GAAG,CAAC,CAAC;MAC7B,MAAMK,aAAa,GAAG,EAAE;MAExBR,cAAc,CAACI,OAAO,CAAC,CAACC,IAAI,EAAEI,KAAK,KAAK;QACtC,MAAMC,MAAM,GAAGL,IAAI,CAACnC,EAAE,GAAGmC,IAAI,CAACnC,EAAE,CAACyC,QAAQ,CAAC,CAAC,GAAG,EAAE;QAChD,MAAMC,WAAW,GAAGF,MAAM,CAACG,UAAU,CAAC,OAAO,CAAC,IAAIH,MAAM,CAACG,UAAU,CAAC,KAAK,CAAC,IAAI,CAACH,MAAM,IAAIA,MAAM,KAAK,EAAE;QAEtG,IAAIL,IAAI,CAACnC,EAAE,IAAI,CAAC0C,WAAW,EAAE;UAC3B;UACAL,WAAW,CAACD,GAAG,CAACD,IAAI,CAACnC,EAAE,EAAE;YACvB,GAAGmC,IAAI;YACPS,QAAQ,EAAET,IAAI,CAACS,QAAQ,KAAKC,SAAS,GAAGV,IAAI,CAACS,QAAQ,GAAGL;UAC1D,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAD,aAAa,CAACQ,IAAI,CAAC;YACjBC,IAAI,EAAEZ,IAAI,CAACY,IAAI,IAAIZ,IAAI,CAAClC,KAAK,IAAI,EAAE;YACnCM,SAAS,EAAE4B,IAAI,CAAC5B,SAAS,IAAI,KAAK;YAClCqC,QAAQ,EAAET,IAAI,CAACS,QAAQ,KAAKC,SAAS,GAAGV,IAAI,CAACS,QAAQ,GAAGL,KAAK;YAC7DS,YAAY,EAAEb,IAAI,CAACc,WAAW,IAAId,IAAI,CAACa,YAAY,IAAI,KAAK;YAC5DE,UAAU,EAAEf,IAAI,CAACe,UAAU,IAAI,IAAI;YACnCC,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ,IAAI;UAC7B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA,KAAK,MAAM,CAACX,MAAM,CAAC,IAAIR,eAAe,EAAE;QACtC,IAAI,CAACK,WAAW,CAACe,GAAG,CAACZ,MAAM,CAAC,EAAE;UAC5B,IAAI;YACF,MAAM/C,UAAU,CAACF,SAAS,CAAC8D,UAAU,CAACb,MAAM,CAAC;YAC7CpD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,MAAM,CAAC;UAChD,CAAC,CAAC,OAAOzC,KAAK,EAAE;YACdX,OAAO,CAACkE,IAAI,CAAC,kCAAkC,EAAEd,MAAM,EAAEzC,KAAK,CAAC;UACjE;QACF;MACF;;MAEA;MACA,KAAK,MAAM,CAACyC,MAAM,EAAEe,OAAO,CAAC,IAAIlB,WAAW,EAAE;QAC3C,MAAMmB,WAAW,GAAGxB,eAAe,CAAC7E,GAAG,CAACqF,MAAM,CAAC;QAC/C,IAAIgB,WAAW,EAAE;UACf;UACA,MAAMC,UAAU,GACdD,WAAW,CAACT,IAAI,KAAKQ,OAAO,CAACR,IAAI,IACjCS,WAAW,CAACjD,SAAS,KAAKgD,OAAO,CAAChD,SAAS,IAC3CiD,WAAW,CAACZ,QAAQ,KAAKW,OAAO,CAACX,QAAQ;UAE3C,IAAIa,UAAU,EAAE;YACd,IAAI;cACF,MAAMhE,UAAU,CAACF,SAAS,CAACmE,UAAU,CAAClB,MAAM,EAAE;gBAC5CO,IAAI,EAAEQ,OAAO,CAACR,IAAI;gBAClBxC,SAAS,EAAEgD,OAAO,CAAChD,SAAS;gBAC5BqC,QAAQ,EAAEW,OAAO,CAACX;cACpB,CAAC,CAAC;cACFxD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,MAAM,CAAC;YAChD,CAAC,CAAC,OAAOzC,KAAK,EAAE;cACdX,OAAO,CAACkE,IAAI,CAAC,kCAAkC,EAAEd,MAAM,EAAEzC,KAAK,CAAC;YACjE;UACF;QACF;MACF;;MAEA;MACA,IAAIuC,aAAa,CAACV,MAAM,GAAG,CAAC,EAAE;QAC5B,IAAI;UACF,MAAMnC,UAAU,CAACF,SAAS,CAACoE,UAAU,CAAC5F,QAAQ,CAACiC,EAAE,EAAE;YAAE4D,KAAK,EAAEtB;UAAc,CAAC,CAAC;UAC5ElD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiD,aAAa,CAACV,MAAM,CAAC;QACnE,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF;MAEAX,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;EAED,MAAM8D,mBAAmB,GAAIC,UAAU,IAAK;IAC1ClD,WAAW,CAAC,iBAAiB,EAAEkD,UAAU,CAAC;EAC5C,CAAC;EAED,MAAMC,mBAAmB,GAAIC,UAAU,IAAK;IAC1CpD,WAAW,CAAC,SAAS,EAAEoD,UAAU,CAAC;EACpC,CAAC;EAED,MAAMC,kBAAkB,GAAIC,SAAS,IAAK;IACxCtD,WAAW,CAAC,QAAQ,EAAEsD,SAAS,CAAC;EAClC,CAAC;EAED,MAAMC,qBAAqB,GAAIC,YAAY,IAAK;IAC9CxD,WAAW,CAAC,WAAW,EAAEwD,YAAY,CAAC;EACxC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAChG,iBAAiB,IAAI,EAACN,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,GAAE;IAEzCxB,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAM+C,eAAe,CAACpD,cAAc,CAAC;;MAErC;MACA,IAAIJ,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEiC,EAAE,EAAE;QAChB,IAAI;UACF,MAAMP,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEC,OAAO;UACvE,MAAMC,MAAM,GAAG,MAAMF,UAAU,CAACG,KAAK,CAACC,OAAO,CAAC9B,QAAQ,CAACiC,EAAE,CAAC;UAC1D,IAAIL,MAAM,CAACG,IAAI,EAAE;YACf;YACA,MAAMR,kBAAkB,GAAG;cACzB,GAAGK,MAAM,CAACG,IAAI;cACdP,SAAS,EAAEI,MAAM,CAACG,IAAI,CAACN,eAAe,IAAIG,MAAM,CAACG,IAAI,CAACP,SAAS,IAAI;YACrE,CAAC;;YAED;YACA,IAAID,kBAAkB,CAACE,eAAe,EAAE;cACtC,OAAOF,kBAAkB,CAACE,eAAe;YAC3C;YAEAxB,WAAW,CAACsB,kBAAkB,CAAC;UACjC;QACF,CAAC,CAAC,OAAOS,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF;;MAEA;MACA3B,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACrBE,oBAAoB,CAAC,KAAK,CAAC;MAE3Bc,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF,CAAC,SAAS;MACRvB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM8F,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACjG,iBAAiB,EAAE;;IAExB;IACA;IACAD,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACrBE,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACAO,MAAM,CAAC7B,QAAQ,CAACuH,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpCrF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoF,OAAO,CAAC;EAC5C,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIrG,iBAAiB,EAAE;MACrB,MAAMsG,YAAY,GAAG9F,MAAM,CAAC+F,OAAO,CACjC,0EACF,CAAC;MACD,IAAI,CAACD,YAAY,EAAE;IACrB;IACA5H,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAM8H,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhG,MAAM,CAAC+F,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChExF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEtB,QAAQ,CAACiC,EAAE,CAAC;MACzCjD,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;;EAED;EACApB,SAAS,CAAC,MAAM;IACd,MAAMmJ,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMvI,WAAW,CAACwI,cAAc,CAAC,CAAC;QACvD,MAAMC,WAAW,GAAG,MAAMzI,WAAW,CAAC0I,sBAAsB,CAAC,CAAC;QAE9D,IAAIH,YAAY,CAACjF,IAAI,IAAIiF,YAAY,CAACjF,IAAI,CAACqF,IAAI,EAAE;UAC/C9H,cAAc,CAAC0H,YAAY,CAACjF,IAAI,CAACqF,IAAI,CAAC;UACtC5H,WAAW,CAACwH,YAAY,CAACjF,IAAI,CAACqF,IAAI,CAACC,IAAI,IAAI,QAAQ,CAAC;QACtD;QAEA,IAAIH,WAAW,CAACnF,IAAI,IAAImF,WAAW,CAACnF,IAAI,CAACuF,YAAY,EAAE;UACrD5H,sBAAsB,CAACwH,WAAW,CAACnF,IAAI,CAACuF,YAAY,CAAC;QACvD;MACF,CAAC,CAAC,OAAOtF,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAxC,WAAW,CAAC,QAAQ,CAAC;MACvB;IACF,CAAC;IAEDuH,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAENnJ,SAAS,CAAC,MAAM;IACd2J,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIxH,SAAS,EAAE;IACb,oBACEvB,OAAA;MAAKgJ,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCjJ,OAAA,CAACX,eAAe;QACduB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzBwI,IAAI,EAAE,GAAGxI,WAAW,CAACyI,SAAS,IAAIzI,WAAW,CAAC0I,QAAQ,EAAE;UACxDC,KAAK,EAAE3I,WAAW,CAAC2I,KAAK;UACxBC,MAAM,EAAE5I,WAAW,CAAC4I,MAAM,IAAI,2BAA2B;UACzDZ,IAAI,EAAE9H;QACR,CAAC,GAAG;UACFsI,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCZ,IAAI,EAAE9H;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAAyI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACF1J,OAAA;QAAKgJ,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEjJ,OAAA;UAAKgJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjJ,OAAA;YAAKgJ,SAAS,EAAC;UAA0E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChG1J,OAAA;YAAKgJ,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjF1J,OAAA;YAAKgJ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAA2C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrI,QAAQ,EAAE;IACb,oBACErB,OAAA;MAAKgJ,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCjJ,OAAA,CAACX,eAAe;QACduB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;QACjCR,WAAW,EAAEA,WAAW,GAAG;UACzBwI,IAAI,EAAE,GAAGxI,WAAW,CAACyI,SAAS,IAAIzI,WAAW,CAAC0I,QAAQ,EAAE;UACxDC,KAAK,EAAE3I,WAAW,CAAC2I,KAAK;UACxBC,MAAM,EAAE5I,WAAW,CAAC4I,MAAM,IAAI,2BAA2B;UACzDZ,IAAI,EAAE9H;QACR,CAAC,GAAG;UACFsI,IAAI,EAAE,YAAY;UAClBG,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,2BAA2B;UACnCZ,IAAI,EAAE9H;QACR,CAAE;QACFE,mBAAmB,EAAEA;MAAoB;QAAAyI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACF1J,OAAA;QAAKgJ,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEjJ,OAAA;UAAKgJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjJ,OAAA;YAAKgJ,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChF1J,OAAA;YAAKgJ,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAsC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtF1J,OAAA;YAAQ2J,OAAO,EAAE3B,WAAY;YAACgB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1J,OAAA;IAAKgJ,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzCjJ,OAAA,CAACX,eAAe;MACduB,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAE;MACjCR,WAAW,EAAEA,WAAW,GAAG;QACzBwI,IAAI,EAAE,GAAGxI,WAAW,CAACyI,SAAS,IAAIzI,WAAW,CAAC0I,QAAQ,EAAE;QACxDC,KAAK,EAAE3I,WAAW,CAAC2I,KAAK;QACxBC,MAAM,EAAE5I,WAAW,CAAC4I,MAAM,IAAI,2BAA2B;QACzDZ,IAAI,EAAE9H;MACR,CAAC,GAAG;QACFsI,IAAI,EAAE,YAAY;QAClBG,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,2BAA2B;QACnCZ,IAAI,EAAE9H;MACR,CAAE;MACFE,mBAAmB,EAAEA;IAAoB;MAAAyI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGF1J,OAAA;MAAKgJ,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtEjJ,OAAA;QAAKgJ,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAE7EjJ,OAAA;UAAKgJ,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAE1HjJ,OAAA,CAACT,UAAU;YACTkD,IAAI,EAAEpB,QAAS;YACfuI,aAAa,EAAEnF,iBAAkB;YACjCoF,OAAO,EAAE7B,WAAY;YACrB8B,QAAQ,EAAE3B,YAAa;YACvBnH,OAAO,EAAEA,OAAQ;YACjBG,SAAS,EAAEA,SAAU;YACrB4F,UAAU,EAAExC,eAAe,CAAC,OAAO;UAAE;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EAGD/H,iBAAiB,iBAChB3B,OAAA;YAAKgJ,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eACjEjJ,OAAA;cAAKgJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjJ,OAAA;gBAAKgJ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CjJ,OAAA,CAACV,IAAI;kBAAC4J,IAAI,EAAC,aAAa;kBAACa,IAAI,EAAE,EAAG;kBAACf,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9D1J,OAAA;kBAAAiJ,QAAA,gBACEjJ,OAAA;oBAAGgJ,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5E1J,OAAA;oBAAGgJ,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAsC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1J,OAAA;gBAAKgJ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CjJ,OAAA;kBACE2J,OAAO,EAAE/B,oBAAqB;kBAC9BoC,QAAQ,EAAEnI,QAAS;kBACnBmH,SAAS,EAAC,gKAAgK;kBAAAC,QAAA,EAC3K;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1J,OAAA;kBACE2J,OAAO,EAAEhC,iBAAkB;kBAC3BqC,QAAQ,EAAEnI,QAAS;kBACnBmH,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EAE/JpH,QAAQ,gBACP7B,OAAA,CAAAE,SAAA;oBAAA+I,QAAA,gBACEjJ,OAAA,CAACV,IAAI;sBAAC4J,IAAI,EAAC,SAAS;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAc;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1D1J,OAAA;sBAAAiJ,QAAA,EAAM;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACtB,CAAC,gBAEH1J,OAAA,CAAAE,SAAA;oBAAA+I,QAAA,gBACEjJ,OAAA,CAACV,IAAI;sBAAC4J,IAAI,EAAC,MAAM;sBAACa,IAAI,EAAE;oBAAG;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9B1J,OAAA;sBAAAiJ,QAAA,EAAM;oBAAY;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACzB;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD1J,OAAA;YAAKgJ,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBAEjFjJ,OAAA;cAAKgJ,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5CjJ,OAAA,CAACR,eAAe;gBACdiD,IAAI,EAAEpB,QAAS;gBACf4I,mBAAmB,EAAEtF,uBAAwB;gBAC7C3D,OAAO,EAAEA,OAAQ;gBACjB+F,UAAU,EAAExC,eAAe,CAAC,aAAa;cAAE;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACF1J,OAAA,CAACJ,gBAAgB;gBACf6C,IAAI,EAAEpB,QAAS;gBACf6I,iBAAiB,EAAEzC,qBAAsB;gBACzCzG,OAAO,EAAEA,OAAQ;gBACjB+F,UAAU,EAAExC,eAAe,CAAC,WAAW;cAAE;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACF1J,OAAA,CAACH,gBAAgB;gBAAC4C,IAAI,EAAEpB,QAAS;gBAAC8I,YAAY,EAAErC,gBAAiB;gBAAC1G,UAAU,EAAEA;cAAW;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eAGN1J,OAAA;cAAKgJ,SAAS,EAAC,6FAA6F;cAAAC,QAAA,gBAC1GjJ,OAAA,CAACP,gBAAgB;gBACfgD,IAAI,EAAEpB,QAAS;gBACf+I,eAAe,EAAEjD,mBAAoB;gBACrCnG,OAAO,EAAEA,OAAQ;gBACjB+F,UAAU,EAAExC,eAAe,CAAC,iBAAiB;cAAE;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF1J,OAAA,CAACN,aAAa;gBACZ+C,IAAI,EAAEpB,QAAS;gBACfgJ,eAAe,EAAEhD,mBAAoB;gBACrCrG,OAAO,EAAEA,OAAQ;gBACjB+F,UAAU,EAAExC,eAAe,CAAC,SAAS;cAAE;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACF1J,OAAA,CAACL,YAAY;gBACX8C,IAAI,EAAEpB,QAAS;gBACfiJ,cAAc,EAAE/C,kBAAmB;gBACnCvG,OAAO,EAAEA,OAAQ;gBACjB+F,UAAU,EAAExC,eAAe,CAAC,QAAQ;cAAE;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAGF1J,OAAA;gBAAKgJ,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC7EjJ,OAAA;kBAAIgJ,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,gBACrEjJ,OAAA,CAACV,IAAI;oBAAC4J,IAAI,EAAC,MAAM;oBAACa,IAAI,EAAE,EAAG;oBAACf,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEzD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1J,OAAA;kBAAKgJ,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjJ,OAAA;oBAAKgJ,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC/EjJ,OAAA;sBAAMgJ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjE1J,OAAA;sBAAMgJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAIlF,IAAI,CAAC1C,QAAQ,CAACyC,SAAS,CAAC,CAACyG,kBAAkB,CAAC;oBAAC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACN1J,OAAA;oBAAKgJ,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,gBAC/EjJ,OAAA;sBAAMgJ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtE1J,OAAA;sBAAMgJ,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAE,IAAIlF,IAAI,CAAC1C,QAAQ,CAAC4C,SAAS,CAAC,CAACsG,kBAAkB,CAAC;oBAAC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC,eACN1J,OAAA;oBAAKgJ,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDjJ,OAAA;sBAAMgJ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjE1J,OAAA;sBAAMgJ,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,GAAC,GAAC,EAAC5H,QAAQ,CAACiC,EAAE;oBAAA;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL,CAAC1I,OAAO,IAAIG,SAAS,kBACpBnB,OAAA;gBAAKgJ,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC7EjJ,OAAA;kBAAIgJ,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,gBACrEjJ,OAAA,CAACV,IAAI;oBAAC4J,IAAI,EAAC,UAAU;oBAACa,IAAI,EAAE,EAAG;oBAACf,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1J,OAAA;kBAAKgJ,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvBjI,OAAO,iBACNhB,OAAA;oBAAQgJ,SAAS,EAAC,oKAAoK;oBAAAC,QAAA,gBACpLjJ,OAAA,CAACV,IAAI;sBAAC4J,IAAI,EAAC,SAAS;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAqB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEnE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACA1I,OAAO,iBACNhB,OAAA;oBAAQgJ,SAAS,EAAC,oKAAoK;oBAAAC,QAAA,gBACpLjJ,OAAA,CAACV,IAAI;sBAAC4J,IAAI,EAAC,MAAM;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAqB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACA1I,OAAO,iBACNhB,OAAA;oBAAQgJ,SAAS,EAAC,oKAAoK;oBAAAC,QAAA,gBACpLjJ,OAAA,CAACV,IAAI;sBAAC4J,IAAI,EAAC,MAAM;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAqB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACAvI,SAAS,iBACRnB,OAAA;oBACE2J,OAAO,EAAExB,YAAa;oBACtBa,SAAS,EAAC,8KAA8K;oBAAAC,QAAA,gBAExLjJ,OAAA,CAACV,IAAI;sBAAC4J,IAAI,EAAC,QAAQ;sBAACa,IAAI,EAAE,EAAG;sBAACf,SAAS,EAAC;oBAAkB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAE/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtJ,EAAA,CAtnBID,WAAW;EAAA,QACEjB,WAAW,EACXE,WAAW,EACLD,eAAe;AAAA;AAAAqL,EAAA,GAHlCrK,WAAW;AAwnBjB,eAAeA,WAAW;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}